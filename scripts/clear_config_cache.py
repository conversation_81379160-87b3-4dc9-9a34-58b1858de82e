#!/usr/bin/env python3
"""
清除配置缓存并验证配置

解决配置缓存导致的配置不生效问题
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def clear_config_cache():
    """清除配置缓存"""
    print("=" * 60)
    print("清除配置缓存")
    print("=" * 60)
    
    try:
        # 导入配置模块
        from shared.config.settings import get_settings
        
        # 清除lru_cache缓存
        get_settings.cache_clear()
        print("[成功] 已清除get_settings缓存")
        
        # 清除全局配置实例
        import shared.config.settings as settings_module
        settings_module._settings_instance = None
        print("[成功] 已清除全局配置实例")
        
        return True
        
    except Exception as e:
        print(f"[错误] 清除缓存失败: {e}")
        return False

def verify_current_config():
    """验证当前配置"""
    print("\n" + "=" * 60)
    print("验证当前配置")
    print("=" * 60)
    
    try:
        # 重新导入配置
        from shared.config.settings import get_settings
        
        # 获取新的配置实例
        settings = get_settings()
        
        print("当前配置值:")
        config_items = [
            ("ENVIRONMENT", settings.ENVIRONMENT),
            ("MILVUS_URI", settings.MILVUS_URI),
            ("MILVUS_HOST", settings.MILVUS_HOST),
            ("MILVUS_PORT", settings.MILVUS_PORT),
            ("GPU_SERVICE_URL", settings.GPU_SERVICE_URL),
            ("DEBUG", settings.DEBUG),
        ]
        
        for name, value in config_items:
            print(f"  {name}: {value}")
            
        # 检查配置文件路径
        env = os.getenv("ENVIRONMENT", "development")
        if env == "production":
            expected_file = "configs/.env.prod"
        elif env == "testing":
            expected_file = "configs/.env.test"
        else:
            expected_file = "configs/.env.local"
            
        print(f"\n使用的配置文件: {expected_file}")
        
        # 验证配置文件内容
        config_file_path = project_root / expected_file
        if config_file_path.exists():
            print(f"[存在] 配置文件: {expected_file}")
            
            # 读取并显示Milvus相关配置
            with open(config_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            milvus_lines = [line.strip() for line in content.split('\n') 
                          if line.strip() and not line.strip().startswith('#') 
                          and 'MILVUS' in line.upper()]
            
            if milvus_lines:
                print("配置文件中的Milvus配置:")
                for line in milvus_lines:
                    print(f"  {line}")
        else:
            print(f"[不存在] 配置文件: {expected_file}")
            
        return settings.MILVUS_URI
        
    except Exception as e:
        print(f"[错误] 验证配置失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_milvus_connection_with_new_config(milvus_uri):
    """使用新配置测试Milvus连接"""
    print("\n" + "=" * 60)
    print("测试Milvus连接")
    print("=" * 60)
    
    if not milvus_uri:
        print("[跳过] 无法获取Milvus URI")
        return False
        
    print(f"测试连接: {milvus_uri}")
    
    try:
        from pymilvus import connections
        
        # 解析URI
        if milvus_uri.startswith("http://") or milvus_uri.startswith("https://"):
            host_port = milvus_uri.replace("http://", "").replace("https://", "")
            if ":" in host_port:
                host, port = host_port.split(":", 1)
                port = int(port)
            else:
                host = host_port
                port = 19530
        else:
            # 如果不是HTTP URI，可能是文件路径
            print(f"[信息] 非HTTP URI，可能是文件模式: {milvus_uri}")
            return True
            
        print(f"连接参数: host={host}, port={port}")
        
        # 测试连接
        connections.connect(
            alias="test_new_config",
            host=host,
            port=port,
            timeout=10
        )
        
        print("[成功] Milvus连接成功!")
        
        # 断开测试连接
        connections.disconnect("test_new_config")
        return True
        
    except Exception as e:
        print(f"[失败] Milvus连接失败: {e}")
        return False

def restart_suggestion():
    """提供重启建议"""
    print("\n" + "=" * 60)
    print("重启建议")
    print("=" * 60)
    
    print("配置缓存已清除，建议执行以下操作:")
    print("1. 重启CPU服务以应用新配置")
    print("2. 检查服务日志确认配置生效")
    print("3. 验证Milvus连接是否正常")
    
    print("\n重启命令:")
    print("  python cpu-service/main.py")
    print("  或")
    print("  python scripts/cpu-local-dev.py")

def main():
    """主函数"""
    print("AI接诉即办助手v3.0 - 配置缓存清除工具")
    print(f"项目根目录: {project_root}")
    
    # 清除缓存
    cache_cleared = clear_config_cache()
    
    if cache_cleared:
        # 验证新配置
        milvus_uri = verify_current_config()
        
        # 测试连接
        if milvus_uri:
            connection_success = test_milvus_connection_with_new_config(milvus_uri)
            
            if connection_success:
                print("\n[结果] 配置修复成功，Milvus连接正常")
            else:
                print("\n[结果] 配置已更新，但Milvus连接仍有问题")
        else:
            print("\n[结果] 配置验证失败")
    else:
        print("\n[结果] 缓存清除失败")
    
    # 提供重启建议
    restart_suggestion()
    
    print("\n" + "=" * 60)
    print("操作完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
