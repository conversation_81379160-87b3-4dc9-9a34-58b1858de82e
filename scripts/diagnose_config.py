#!/usr/bin/env python3
"""
配置诊断脚本

诊断环境变量配置加载问题和Milvus连接问题
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_environment_variables():
    """检查环境变量设置"""
    print("=" * 60)
    print("环境变量诊断")
    print("=" * 60)
    
    # 检查ENVIRONMENT变量
    env = os.getenv("ENVIRONMENT", "未设置")
    print(f"ENVIRONMENT: {env}")
    
    # 检查关键环境变量
    key_vars = [
        "MILVUS_URI",
        "MILVUS_HOST", 
        "MILVUS_PORT",
        "GPU_SERVICE_URL",
        "CPU_SERVICE_PORT",
        "DEBUG",
        "LOG_LEVEL"
    ]
    
    print("\n关键环境变量:")
    for var in key_vars:
        value = os.getenv(var, "未设置")
        print(f"  {var}: {value}")
    
    return env

def check_config_files():
    """检查配置文件存在性和内容"""
    print("\n" + "=" * 60)
    print("配置文件诊断")
    print("=" * 60)
    
    config_files = [
        "configs/.env.local",
        "configs/.env.dev",
        "configs/.env.dev-local", 
        "configs/.env.prod",
        ".env",
        ".env.example"
    ]
    
    for config_file in config_files:
        file_path = project_root / config_file
        if file_path.exists():
            print(f"[存在] {config_file}")
            
            # 读取MILVUS相关配置
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                milvus_lines = [line.strip() for line in content.split('\n') 
                              if line.strip() and not line.strip().startswith('#') 
                              and 'MILVUS' in line.upper()]
                
                if milvus_lines:
                    print(f"   Milvus配置:")
                    for line in milvus_lines:
                        print(f"     {line}")

            except Exception as e:
                print(f"   [错误] 读取失败: {e}")
        else:
            print(f"[不存在] {config_file}")

def test_config_loading():
    """测试配置加载"""
    print("\n" + "=" * 60)
    print("配置加载测试")
    print("=" * 60)
    
    try:
        # 导入配置模块
        from shared.config.settings import get_settings, Settings
        
        print("[成功] 导入配置模块")

        # 获取配置实例
        settings = get_settings()
        print("[成功] 获取配置实例")
        
        # 检查关键配置
        config_items = [
            ("ENVIRONMENT", settings.ENVIRONMENT),
            ("DEBUG", settings.DEBUG),
            ("LOG_LEVEL", settings.LOG_LEVEL),
            ("MILVUS_URI", settings.MILVUS_URI),
            ("MILVUS_HOST", settings.MILVUS_HOST),
            ("MILVUS_PORT", settings.MILVUS_PORT),
            ("GPU_SERVICE_URL", settings.GPU_SERVICE_URL),
            ("CPU_SERVICE_PORT", settings.CPU_SERVICE_PORT),
        ]
        
        print("\n当前配置值:")
        for name, value in config_items:
            print(f"  {name}: {value}")
            
        # 检查配置文件路径
        env = os.getenv("ENVIRONMENT", "development")
        if env == "production":
            expected_file = "configs/.env.prod"
        elif env == "testing":
            expected_file = "configs/.env.test"
        else:
            expected_file = "configs/.env.local"
            
        print(f"\n预期配置文件: {expected_file}")
        
        file_path = project_root / expected_file
        if file_path.exists():
            print(f"[存在] 配置文件: {expected_file}")
        else:
            print(f"[不存在] 配置文件: {expected_file}")
            print("   将使用默认配置")

    except Exception as e:
        print(f"[错误] 配置加载失败: {e}")
        import traceback
        traceback.print_exc()

def test_milvus_connection():
    """测试Milvus连接"""
    print("\n" + "=" * 60)
    print("Milvus连接测试")
    print("=" * 60)
    
    try:
        from shared.config.settings import get_settings
        settings = get_settings()
        
        print(f"配置的Milvus URI: {settings.MILVUS_URI}")
        print(f"配置的Milvus Host: {settings.MILVUS_HOST}")
        print(f"配置的Milvus Port: {settings.MILVUS_PORT}")
        
        # 尝试连接测试
        try:
            from pymilvus import connections
            
            # 解析URI
            uri = settings.MILVUS_URI
            if uri.startswith("http://") or uri.startswith("https://"):
                host_port = uri.replace("http://", "").replace("https://", "")
                if ":" in host_port:
                    host, port = host_port.split(":", 1)
                    port = int(port)
                else:
                    host = host_port
                    port = 19530
            else:
                host = settings.MILVUS_HOST
                port = settings.MILVUS_PORT
                
            print(f"\n尝试连接: {host}:{port}")
            
            # 测试连接
            connections.connect(
                alias="test_connection",
                host=host,
                port=port,
                timeout=10
            )
            
            print("[成功] Milvus连接成功!")

            # 断开测试连接
            connections.disconnect("test_connection")

        except Exception as e:
            print(f"[失败] Milvus连接失败: {e}")
            
            # 尝试本地连接
            print("\n尝试连接本地Milvus...")
            try:
                connections.connect(
                    alias="test_local",
                    host="localhost",
                    port=19530,
                    timeout=10
                )
                print("[成功] 本地Milvus连接成功!")
                connections.disconnect("test_local")
            except Exception as local_e:
                print(f"[失败] 本地Milvus连接也失败: {local_e}")

    except Exception as e:
        print(f"[错误] Milvus连接测试失败: {e}")

def suggest_fixes():
    """提供修复建议"""
    print("\n" + "=" * 60)
    print("修复建议")
    print("=" * 60)
    
    print("1. 环境变量配置问题:")
    print("   - 确保在启动CPU服务前设置 ENVIRONMENT=development")
    print("   - 检查 configs/.env.local 文件是否存在且配置正确")
    print("   - 验证配置文件中的 MILVUS_URI 设置")
    
    print("\n2. Milvus连接问题:")
    print("   - 检查Milvus服务是否在 ************:19530 运行")
    print("   - 验证网络连通性: ping ************")
    print("   - 检查防火墙设置")
    print("   - 确认Milvus服务状态")
    
    print("\n3. 配置加载优先级:")
    print("   - 环境变量 > 配置文件 > 默认值")
    print("   - 检查是否有环境变量覆盖了配置文件设置")
    
    print("\n4. 调试步骤:")
    print("   - 设置 ENVIRONMENT=development")
    print("   - 确保 configs/.env.local 存在")
    print("   - 检查 MILVUS_URI=http://************:19530")
    print("   - 重启CPU服务")

def main():
    """主函数"""
    print("AI接诉即办助手v3.0 - 配置诊断工具")
    print(f"项目根目录: {project_root}")

    # 执行诊断
    env = check_environment_variables()
    check_config_files()
    test_config_loading()
    test_milvus_connection()
    suggest_fixes()

    print("\n" + "=" * 60)
    print("诊断完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
