#!/usr/bin/env python3
"""
AI接诉即办助手 v3.0 - 统一服务启动脚本
支持跨平台、多环境、智能依赖检查的服务管理工具

使用方法:
    python start_services.py --service [gpu|cpu|all] --replicas [1-5] --env [dev|prod|test] --network [bridge|host]

示例:
    python start_services.py --service all --env prod --replicas 2
    python start_services.py --service gpu --env dev
    python start_services.py --service cpu --replicas 3 --network host
"""

import os
import sys
import time
import json
import argparse
import platform
import subprocess
import requests
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime

class ServiceManager:
    """服务管理器"""
    
    def __init__(self):
        self.system = platform.system()
        self.project_root = Path.cwd()
        self.log_level = "INFO"
        self.start_time = datetime.now()
        
        # 服务配置 - 统一命名规范
        self.services = {
            "gpu": {
                "image": "ai-v3-gpu-service:prod",
                "container_name": "ai-v3-gpu-service-prod",
                "port": 8001,
                "health_endpoint": "/health",
                "required_gpu": True
            },
            "cpu": {
                "image": "ai-v3-cpu-service:prod",
                "container_name_template": "ai-v3-cpu-service-{}",
                "port": 8000,
                "health_endpoint": "/health",
                "required_gpu": False
            }
        }
    
    def log(self, level: str, message: str):
        """日志输出"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        icons = {"INFO": "ℹ️", "WARN": "⚠️", "ERROR": "❌", "SUCCESS": "✅"}
        icon = icons.get(level, "📝")
        print(f"[{timestamp}] {icon} {message}")
    
    def check_docker_environment(self) -> bool:
        """检查Docker环境"""
        self.log("INFO", "检查Docker环境...")
        
        try:
            # 检查Docker是否安装
            result = subprocess.run(
                ["docker", "--version"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            if result.returncode != 0:
                self.log("ERROR", "Docker未安装或无法访问")
                return False
            
            # 检查Docker是否运行
            result = subprocess.run(
                ["docker", "info"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            if result.returncode != 0:
                self.log("ERROR", "Docker未运行，请启动Docker Desktop")
                return False
            
            # 检查Docker Compose
            result = subprocess.run(
                ["docker-compose", "--version"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            if result.returncode != 0:
                self.log("WARN", "Docker Compose未安装，将使用docker命令")
            
            self.log("SUCCESS", "Docker环境检查通过")
            return True
            
        except subprocess.TimeoutExpired:
            self.log("ERROR", "Docker命令超时")
            return False
        except FileNotFoundError:
            self.log("ERROR", "Docker未安装")
            return False
        except Exception as e:
            self.log("ERROR", f"Docker环境检查失败: {str(e)}")
            return False

    def _is_container_running(self, container_name: str) -> bool:
        """检查容器是否正在运行"""
        try:
            result = subprocess.run(
                ["docker", "ps", "--filter", f"name={container_name}", "--format", "{{.Names}}"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return container_name in result.stdout
        except Exception:
            return False

    def check_images_exist(self, services: List[str]) -> bool:
        """检查Docker镜像是否存在或容器已运行"""
        self.log("INFO", "检查Docker镜像和运行状态...")

        for service in services:
            if service not in self.services:
                self.log("ERROR", f"未知服务: {service}")
                return False

            # 检查容器是否已经运行
            if service == "gpu":
                container_name = self.services[service]["container_name"]
            else:
                container_name = self.services[service]["container_name_template"].format(1)

            if self._is_container_running(container_name):
                self.log("SUCCESS", f"{service}服务容器已在运行: {container_name}")
                continue

            # 检查镜像是否存在
            image = self.services[service]["image"]
            try:
                result = subprocess.run(
                    ["docker", "image", "inspect", image],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                if result.returncode != 0:
                    self.log("ERROR", f"镜像不存在: {image}")
                    self.log("INFO", f"请先构建镜像: python scripts/build.py")
                    return False
                else:
                    self.log("SUCCESS", f"镜像存在: {image}")
            except Exception as e:
                self.log("ERROR", f"检查镜像失败: {str(e)}")
                return False

        return True
    
    def check_ports_available(self, services: List[str], replicas: int = 1) -> bool:
        """检查端口是否可用"""
        self.log("INFO", "检查端口可用性...")
        
        import socket
        
        for service in services:
            if service == "gpu":
                port = self.services[service]["port"]
                if not self._is_port_available(port):
                    self.log("ERROR", f"端口 {port} 已被占用 (GPU服务)")
                    return False
                self.log("SUCCESS", f"端口 {port} 可用 (GPU服务)")
            
            elif service == "cpu":
                # CPU服务通常不直接暴露端口，由Nginx代理
                # 但在开发模式下可能需要检查
                pass
        
        return True
    
    def _is_port_available(self, port: int) -> bool:
        """检查单个端口是否可用"""
        import socket
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex(('localhost', port))
                return result != 0  # 如果连接失败，说明端口可用
        except Exception:
            return True  # 异常时假设端口可用
    
    def get_host_ip(self) -> str:
        """获取宿主机IP地址"""
        try:
            if self.system == "Windows":
                result = subprocess.run(
                    ["ipconfig"], 
                    capture_output=True, 
                    text=True, 
                    timeout=10
                )
                # 简单解析，获取第一个IPv4地址
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'IPv4' in line and '192.168' in line:
                        return line.split(':')[-1].strip()
                return "***********"  # 默认值
            else:
                result = subprocess.run(
                    ["hostname", "-I"], 
                    capture_output=True, 
                    text=True, 
                    timeout=10
                )
                return result.stdout.strip().split()[0]
        except Exception:
            return "localhost"
    
    def start_gpu_service(self, env: str, network: str) -> bool:
        """启动GPU服务"""
        self.log("INFO", "启动GPU服务...")
        
        service_config = self.services["gpu"]
        container_name = service_config["container_name"]
        
        # 检查是否已经运行
        if self._is_container_running(container_name):
            self.log("SUCCESS", f"GPU服务已在运行: {container_name}")
            return True
        
        # 构建Docker命令
        cmd = [
            "docker", "run", "-d",
            "--name", container_name,
            "--restart", "unless-stopped",
            "-p", f"{service_config['port']}:{service_config['port']}",
            "-v", "ai-models-cache:/app/models",
            "-v", f"{self.project_root}/logs/gpu:/app/logs",
            "--gpus", "all",
            "-e", f"ENVIRONMENT={env}",
            "-e", "GPU_SERVICE_HOST=0.0.0.0",
            "-e", f"GPU_SERVICE_PORT={service_config['port']}",
            "-e", "BGE_M3_MODEL_PATH=/app/models/hub/bge-m3"
        ]
        
        if network != "bridge":
            cmd.extend(["--network", network])
        
        cmd.append(service_config["image"])
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                self.log("SUCCESS", f"GPU服务启动成功: {container_name}")
                return True
            else:
                self.log("ERROR", f"GPU服务启动失败: {result.stderr}")
                return False
        except Exception as e:
            self.log("ERROR", f"启动GPU服务异常: {str(e)}")
            return False
    
    def start_cpu_service(self, env: str, network: str, replicas: int) -> bool:
        """启动CPU服务"""
        self.log("INFO", f"启动CPU服务 (副本数: {replicas})...")
        
        service_config = self.services["cpu"]
        host_ip = self.get_host_ip()
        
        success_count = 0
        
        for i in range(1, replicas + 1):
            container_name = service_config["container_name_template"].format(i)
            
            # 检查是否已经运行
            if self._is_container_running(container_name):
                self.log("SUCCESS", f"CPU服务副本{i}已在运行: {container_name}")
                success_count += 1
                continue
            
            # 构建Docker命令
            cmd = [
                "docker", "run", "-d",
                "--name", container_name,
                "--restart", "unless-stopped",
                "-p", f"{service_config['port']}:{service_config['port']}",
                "-v", f"{self.project_root}/logs/cpu-{i}:/app/logs",
                "-e", f"ENVIRONMENT={env}",
                "-e", "CPU_SERVICE_HOST=0.0.0.0",
                "-e", f"CPU_SERVICE_PORT={service_config['port']}",
                "-e", "CPU_SERVICE_WORKERS=2",
                "-e", f"GPU_SERVICE_URL=http://{host_ip}:8001"
            ]
            
            if network != "bridge":
                cmd.extend(["--network", network])
            
            cmd.append(service_config["image"])
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                if result.returncode == 0:
                    self.log("SUCCESS", f"CPU服务副本{i}启动成功: {container_name}")
                    success_count += 1
                else:
                    self.log("ERROR", f"CPU服务副本{i}启动失败: {result.stderr}")
            except Exception as e:
                self.log("ERROR", f"启动CPU服务副本{i}异常: {str(e)}")
        
        return success_count == replicas

    def _is_container_running(self, container_name: str) -> bool:
        """检查容器是否正在运行"""
        try:
            result = subprocess.run(
                ["docker", "ps", "--filter", f"name={container_name}", "--format", "{{.Names}}"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return container_name in result.stdout
        except Exception:
            return False

    def wait_for_service_health(self, service: str, container_name: str, timeout: int = 60) -> bool:
        """等待服务健康检查通过"""
        self.log("INFO", f"等待 {service} 服务健康检查...")

        service_config = self.services[service]
        port = service_config["port"]
        health_endpoint = service_config["health_endpoint"]

        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(
                    f"http://localhost:{port}{health_endpoint}",
                    timeout=5
                )
                if response.status_code == 200:
                    self.log("SUCCESS", f"{service} 服务健康检查通过")
                    return True
            except Exception:
                pass

            time.sleep(2)
            print(".", end="", flush=True)

        print()  # 换行
        self.log("ERROR", f"{service} 服务健康检查超时")
        return False

    def test_service_connectivity(self) -> Dict[str, bool]:
        """测试服务连接性"""
        self.log("INFO", "测试服务连接性...")

        results = {}

        # 测试GPU服务
        try:
            response = requests.get("http://localhost:8001/health", timeout=10)
            results["gpu"] = response.status_code == 200
            if results["gpu"]:
                self.log("SUCCESS", "GPU服务连接正常")
            else:
                self.log("ERROR", f"GPU服务响应异常: {response.status_code}")
        except Exception as e:
            results["gpu"] = False
            self.log("ERROR", f"GPU服务连接失败: {str(e)}")

        # 测试CPU服务
        try:
            response = requests.get("http://localhost:8000/health", timeout=10)
            results["cpu"] = response.status_code == 200
            if results["cpu"]:
                self.log("SUCCESS", "CPU服务连接正常")
            else:
                self.log("ERROR", f"CPU服务响应异常: {response.status_code}")
        except Exception as e:
            results["cpu"] = False
            self.log("ERROR", f"CPU服务连接失败: {str(e)}")

        return results

    def show_service_status(self):
        """显示服务状态"""
        self.log("INFO", "服务状态总览:")

        try:
            result = subprocess.run(
                ["docker", "ps", "--filter", "name=ai-v3", "--format",
                 "table {{.Names}}\t{{.Status}}\t{{.Ports}}"],
                capture_output=True,
                text=True,
                timeout=10
            )
            print(result.stdout)
        except Exception as e:
            self.log("ERROR", f"获取服务状态失败: {str(e)}")

    def generate_summary_report(self, services: List[str], replicas: int, env: str, network: str):
        """生成启动总结报告"""
        elapsed_time = (datetime.now() - self.start_time).total_seconds()

        print("\n" + "=" * 60)
        print("🎉 服务启动完成")
        print("=" * 60)
        print(f"启动时间: {elapsed_time:.2f}秒")
        print(f"环境: {env}")
        print(f"网络: {network}")
        print(f"服务: {', '.join(services)}")
        if "cpu" in services:
            print(f"CPU副本数: {replicas}")
        print()

        # 显示访问地址
        print("🌐 服务访问地址:")
        if "gpu" in services:
            print("  GPU服务: http://localhost:8001")
            print("  GPU健康检查: http://localhost:8001/health")
        if "cpu" in services:
            print("  CPU服务: http://localhost:8000")
            print("  CPU健康检查: http://localhost:8000/health")
            print("  API文档: http://localhost:8000/docs")
        print()

        # 显示管理命令
        print("🛠️ 管理命令:")
        print("  查看日志: docker logs <container_name>")
        print("  停止服务: docker stop <container_name>")
        print("  重启服务: docker restart <container_name>")
        print("  服务状态: docker ps")
        print()

        # 保存启动信息
        startup_info = {
            "timestamp": self.start_time.isoformat(),
            "services": services,
            "replicas": replicas,
            "environment": env,
            "network": network,
            "elapsed_time": elapsed_time
        }

        with open("startup_info.json", "w", encoding="utf-8") as f:
            json.dump(startup_info, f, ensure_ascii=False, indent=2)

        print("📄 启动信息已保存到: startup_info.json")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="AI接诉即办助手 v3.0 - 统一服务启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python start_services.py --service all --env prod --replicas 2
  python start_services.py --service gpu --env dev
  python start_services.py --service cpu --replicas 3 --network host
        """
    )

    parser.add_argument(
        "--service",
        choices=["gpu", "cpu", "all"],
        default="all",
        help="要启动的服务 (默认: all)"
    )

    parser.add_argument(
        "--replicas",
        type=int,
        choices=range(1, 6),
        default=2,
        help="CPU服务副本数 (1-5, 默认: 2)"
    )

    parser.add_argument(
        "--env",
        choices=["dev", "prod", "test"],
        default="prod",
        help="运行环境 (默认: prod)"
    )

    parser.add_argument(
        "--network",
        choices=["bridge", "host"],
        default="bridge",
        help="网络模式 (默认: bridge)"
    )

    parser.add_argument(
        "--verbose",
        action="store_true",
        help="详细输出"
    )

    parser.add_argument(
        "--no-health-check",
        action="store_true",
        help="跳过健康检查"
    )

    args = parser.parse_args()

    # 创建服务管理器
    manager = ServiceManager()

    if args.verbose:
        manager.log_level = "DEBUG"

    print("=" * 60)
    print("🚀 AI接诉即办助手 v3.0 - 服务启动器")
    print("=" * 60)
    print(f"服务: {args.service}")
    print(f"环境: {args.env}")
    print(f"网络: {args.network}")
    if args.service in ["cpu", "all"]:
        print(f"CPU副本数: {args.replicas}")
    print()

    # 确定要启动的服务
    services_to_start = []
    if args.service == "all":
        services_to_start = ["gpu", "cpu"]
    else:
        services_to_start = [args.service]

    # 1. 环境检查
    if not manager.check_docker_environment():
        sys.exit(1)

    if not manager.check_images_exist(services_to_start):
        sys.exit(1)

    if not manager.check_ports_available(services_to_start, args.replicas):
        sys.exit(1)

    # 2. 启动服务
    success = True

    if "gpu" in services_to_start:
        if not manager.start_gpu_service(args.env, args.network):
            success = False
        elif not args.no_health_check:
            if not manager.wait_for_service_health("gpu", "ai-v3-gpu-service-prod"):
                success = False

    if "cpu" in services_to_start and success:
        if not manager.start_cpu_service(args.env, args.network, args.replicas):
            success = False
        elif not args.no_health_check:
            # 等待第一个CPU服务健康检查
            if not manager.wait_for_service_health("cpu", "ai-v3-cpu-service-1"):
                success = False

    # 3. 连接性测试
    if success and not args.no_health_check:
        time.sleep(2)  # 等待服务完全启动
        connectivity_results = manager.test_service_connectivity()
        if not all(connectivity_results.values()):
            success = False

    # 4. 显示结果
    manager.show_service_status()

    if success:
        manager.generate_summary_report(services_to_start, args.replicas, args.env, args.network)
        sys.exit(0)
    else:
        manager.log("ERROR", "服务启动失败，请检查日志")
        sys.exit(1)

if __name__ == "__main__":
    main()
