@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: AI接诉即办助手 v3.0 - 批量构建脚本 (Windows)
:: 更新时间：2025-06-12

echo.
echo ==========================================
echo   AI接诉即办助手 v3.0 - Docker镜像构建
echo ==========================================
echo.

:: 检查Docker是否可用
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未安装或不可用
    echo 请先安装Docker Desktop
    pause
    exit /b 1
)

:: 检查是否在项目根目录
if not exist "gpu-service\dockerfile" (
    echo ❌ 请在项目根目录运行此脚本
    echo 当前目录: %cd%
    pause
    exit /b 1
)

if not exist "cpu-service\dockerfile" (
    echo ❌ 找不到CPU服务Dockerfile
    echo 请确保在正确的项目目录中
    pause
    exit /b 1
)

echo ✅ 环境检查通过
echo.

:: 询问是否清理缓存
set /p clean_cache="是否清理Docker构建缓存? (y/N): "
if /i "%clean_cache%"=="y" (
    echo 🧹 清理Docker构建缓存...
    docker builder prune -f
    echo.
)

:: 构建GPU服务
echo [1/2] 🔨 构建GPU服务镜像...
echo 命令: docker build -f gpu-service/dockerfile -t ai-v3-gpu-service:prod .
echo.

docker build -f gpu-service/dockerfile -t ai-v3-gpu-service:prod .

if %errorlevel% neq 0 (
    echo.
    echo ❌ GPU服务构建失败！
    echo 请检查错误信息并重试
    pause
    exit /b 1
)

echo.
echo ✅ GPU服务构建成功
echo.

:: 构建CPU服务
echo [2/2] 🔨 构建CPU服务镜像...
echo 命令: docker build -f cpu-service/dockerfile -t ai-v3-cpu-service:prod .
echo.

docker build -f cpu-service/dockerfile -t ai-v3-cpu-service:prod .

if %errorlevel% neq 0 (
    echo.
    echo ❌ CPU服务构建失败！
    echo 请检查错误信息并重试
    pause
    exit /b 1
)

echo.
echo ✅ CPU服务构建成功
echo.

:: 显示构建结果
echo ==========================================
echo   🎉 所有服务构建完成！
echo ==========================================
echo.
echo 📦 构建的镜像:
docker images | findstr ai-v3

echo.
echo 📋 下一步操作:
echo   1. 部署服务: cd deployment ^&^& docker-compose up -d
echo   2. 健康检查: curl http://localhost:8001/health
echo   3. 查看文档: docs/DOCKER_BUILD_DEPLOY_GUIDE.md
echo.

pause
