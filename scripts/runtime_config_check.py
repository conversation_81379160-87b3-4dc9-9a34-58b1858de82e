#!/usr/bin/env python3
"""
运行时配置检查脚本

检查CPU服务运行时的实际配置值
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_environment_variables():
    """检查环境变量"""
    print("=" * 60)
    print("环境变量检查")
    print("=" * 60)
    
    env_vars = [
        "ENVIRONMENT",
        "MILVUS_URI", 
        "MILVUS_HOST",
        "MILVUS_PORT",
        "PYTHONPATH"
    ]
    
    for var in env_vars:
        value = os.getenv(var, "未设置")
        print(f"{var}: {value}")

def check_config_loading():
    """检查配置加载"""
    print("\n" + "=" * 60)
    print("配置加载检查")
    print("=" * 60)
    
    try:
        # 清除缓存
        from shared.config.settings import get_settings
        get_settings.cache_clear()
        
        # 重新获取配置
        settings = get_settings()
        
        print("当前配置值:")
        print(f"  ENVIRONMENT: {settings.ENVIRONMENT}")
        print(f"  MILVUS_URI: {settings.MILVUS_URI}")
        print(f"  MILVUS_HOST: {settings.MILVUS_HOST}")
        print(f"  MILVUS_PORT: {settings.MILVUS_PORT}")
        
        # 检查配置文件路径
        env = os.getenv("ENVIRONMENT", "development")
        if env == "production":
            expected_file = "configs/.env.prod"
        elif env == "testing":
            expected_file = "configs/.env.test"
        else:
            expected_file = "configs/.env.local"
            
        print(f"\n预期配置文件: {expected_file}")
        
        # 检查文件是否存在
        config_file = project_root / expected_file
        if config_file.exists():
            print(f"配置文件存在: {config_file}")
            
            # 读取文件内容
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 查找MILVUS_URI
            for line in content.split('\n'):
                if line.strip().startswith('MILVUS_URI='):
                    print(f"文件中的MILVUS_URI: {line.strip()}")
                    break
        else:
            print(f"配置文件不存在: {config_file}")
            
        return settings
        
    except Exception as e:
        print(f"配置加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def check_milvus_client_config():
    """检查Milvus客户端配置"""
    print("\n" + "=" * 60)
    print("Milvus客户端配置检查")
    print("=" * 60)
    
    try:
        from shared.clients.milvus_client import OptimizedMilvusClient
        from shared.config.settings import get_settings
        
        # 获取配置
        settings = get_settings()
        print(f"settings.MILVUS_URI: {settings.MILVUS_URI}")
        
        # 模拟客户端初始化（不实际连接）
        print("\n模拟Milvus客户端初始化...")
        
        # 检查传入的URI参数
        uri = None or settings.MILVUS_URI
        print(f"实际使用的URI: {uri}")
        
        return uri
        
    except Exception as e:
        print(f"Milvus客户端配置检查失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def check_legacy_config():
    """检查旧版配置适配器"""
    print("\n" + "=" * 60)
    print("旧版配置适配器检查")
    print("=" * 60)
    
    try:
        from cpu_service.cores.config import my_settings
        
        print("旧版配置值:")
        print(f"  MILVUS_URI: {my_settings.MILVUS_URI}")
        print(f"  MILVUS_HOST: {my_settings.MILVUS_HOST}")
        print(f"  MILVUS_PORT: {my_settings.MILVUS_PORT}")
        
        return my_settings
        
    except Exception as e:
        print(f"旧版配置检查失败: {e}")
        return None

def test_actual_connection():
    """测试实际连接"""
    print("\n" + "=" * 60)
    print("实际连接测试")
    print("=" * 60)
    
    try:
        from pymilvus import connections
        
        # 测试************:19530
        print("测试 ************:19530...")
        try:
            connections.connect(
                alias="test_correct",
                host="************",
                port=19530,
                timeout=5
            )
            print("[成功] ************:19530 连接成功")
            connections.disconnect("test_correct")
        except Exception as e:
            print(f"[失败] ************:19530 连接失败: {e}")
            
        # 测试localhost:19530
        print("\n测试 localhost:19530...")
        try:
            connections.connect(
                alias="test_local",
                host="localhost", 
                port=19530,
                timeout=5
            )
            print("[成功] localhost:19530 连接成功")
            connections.disconnect("test_local")
        except Exception as e:
            print(f"[失败] localhost:19530 连接失败: {e}")
            
    except Exception as e:
        print(f"连接测试失败: {e}")

def main():
    """主函数"""
    print("AI接诉即办助手v3.0 - 运行时配置检查")
    print(f"项目根目录: {project_root}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 执行检查
    check_environment_variables()
    settings = check_config_loading()
    milvus_uri = check_milvus_client_config()
    legacy_settings = check_legacy_config()
    test_actual_connection()
    
    # 分析结果
    print("\n" + "=" * 60)
    print("分析结果")
    print("=" * 60)
    
    if settings and milvus_uri:
        if "************" in milvus_uri:
            print("[正确] 配置指向正确的Milvus服务器")
        else:
            print("[错误] 配置仍然指向错误的地址")
            print(f"       当前: {milvus_uri}")
            print(f"       应该: http://************:19530")
    
    if legacy_settings:
        print(f"[信息] 旧版配置: {legacy_settings.MILVUS_URI}")
    
    print("\n建议:")
    print("1. 如果配置正确但仍连接localhost，可能是缓存问题")
    print("2. 检查是否有多个配置源在冲突")
    print("3. 确认服务重启后配置是否生效")

if __name__ == "__main__":
    main()
