#!/usr/bin/env python3
"""
现代化依赖升级脚本 - 使用pip install -U策略升级到最新稳定版本
更新时间：2025-06-12
"""

import subprocess
import sys
import os
import time
from datetime import datetime
from pathlib import Path

def run_command(cmd, cwd=None, timeout=1800):
    """运行命令并返回结果"""
    try:
        print(f"🔧 执行: {cmd}")
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            cwd=cwd,
            timeout=timeout
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "命令执行超时"
    except Exception as e:
        return False, "", str(e)

def backup_environment():
    """备份当前环境"""
    print("📦 备份当前环境...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backups/modern_upgrade_{timestamp}"
    
    # 创建备份目录
    os.makedirs(backup_dir, exist_ok=True)
    
    # 备份当前pip freeze
    success, stdout, stderr = run_command("pip freeze")
    if success:
        with open(f"{backup_dir}/current_environment.txt", "w", encoding="utf-8") as f:
            f.write(stdout)
        print(f"✅ 当前环境已备份到: {backup_dir}/current_environment.txt")
    else:
        print(f"❌ 备份当前环境失败: {stderr}")
        return None
    
    # 备份requirements文件
    try:
        import shutil
        shutil.copy2("cpu-service/requirements.txt", f"{backup_dir}/cpu_requirements_new.txt")
        shutil.copy2("gpu-service/requirements.txt", f"{backup_dir}/gpu_requirements_new.txt")
        if os.path.exists("cpu-service/requirements.txt.backup"):
            shutil.copy2("cpu-service/requirements.txt.backup", f"{backup_dir}/cpu_requirements_original.txt")
        if os.path.exists("gpu-service/requirements.txt.backup"):
            shutil.copy2("gpu-service/requirements.txt.backup", f"{backup_dir}/gpu_requirements_original.txt")
        print(f"✅ Requirements文件已备份")
    except Exception as e:
        print(f"❌ 备份requirements文件失败: {e}")
        return None
    
    return backup_dir

def upgrade_pip():
    """升级pip到最新版本"""
    print("📦 升级pip到最新版本...")
    
    success, stdout, stderr = run_command("python -m pip install --upgrade pip")
    if success:
        print("✅ pip升级成功")
        return True
    else:
        print(f"❌ pip升级失败: {stderr}")
        return False

def modern_upgrade_cpu_service():
    """现代化升级CPU服务依赖"""
    print("\n🚀 现代化升级CPU服务依赖...")
    
    # 切换到CPU服务目录
    os.chdir("cpu-service")
    
    try:
        # 关键包优先升级策略
        priority_packages = [
            "fastapi",
            "uvicorn[standard]", 
            "pydantic",
            "pydantic-settings",
            "llama-index",
            "llama-index-core"
        ]
        
        print("🎯 优先升级关键包...")
        for package in priority_packages:
            print(f"📦 升级 {package}...")
            success, stdout, stderr = run_command(f"pip install -U {package}")
            if success:
                print(f"✅ {package} 升级成功")
            else:
                print(f"⚠️ {package} 升级有警告: {stderr}")
        
        # 全面升级所有依赖
        print("🔄 全面升级所有依赖...")
        success, stdout, stderr = run_command("pip install -U -r requirements.txt")
        
        if success:
            print("✅ CPU服务依赖现代化升级成功")
            print("📋 升级日志:")
            print(stdout[-1500:])  # 显示最后1500字符
            return True
        else:
            print(f"❌ CPU服务依赖升级失败:")
            print(stderr)
            return False
    
    finally:
        # 返回项目根目录
        os.chdir("..")

def check_llama_index_compatibility():
    """检查LlamaIndex升级后的兼容性"""
    print("\n🔍 检查LlamaIndex升级后的兼容性...")
    
    test_imports = [
        "import llama_index",
        "from llama_index.core import VectorStoreIndex",
        "from llama_index.core import Document",
        "from llama_index.embeddings.openai import OpenAIEmbedding",
        "from llama_index.vector_stores.milvus import MilvusVectorStore",
        "from llama_index.llms.openai import OpenAI"
    ]
    
    for test_import in test_imports:
        success, stdout, stderr = run_command(f'python -c "{test_import}"')
        if success:
            print(f"✅ {test_import}")
        else:
            print(f"❌ {test_import} - {stderr}")
            return False
    
    print("✅ LlamaIndex兼容性检查通过")
    return True

def check_version_upgrades():
    """检查版本升级情况"""
    print("\n📊 检查版本升级情况...")
    
    key_packages = [
        "fastapi", "uvicorn", "pydantic", "llama-index", 
        "llama-index-core", "numpy", "pandas", "pymilvus"
    ]
    
    for package in key_packages:
        success, stdout, stderr = run_command(f"pip show {package}")
        if success:
            lines = stdout.split('\n')
            for line in lines:
                if line.startswith('Version:'):
                    version = line.split(':')[1].strip()
                    print(f"📦 {package}: {version}")
                    break
        else:
            print(f"⚠️ {package}: 未安装或查询失败")

def test_service_functionality():
    """测试服务功能"""
    print("\n🧪 测试服务功能...")
    
    # 测试CPU服务模块加载
    print("🔍 测试CPU服务模块加载...")
    success, stdout, stderr = run_command(
        'python -c "from main import app; print(\'CPU服务模块加载成功\')"',
        cwd="cpu-service"
    )
    if success:
        print("✅ CPU服务模块加载正常")
    else:
        print(f"❌ CPU服务模块加载失败: {stderr}")
        return False
    
    # 测试GPU服务连接
    print("🔍 测试GPU服务连接...")
    success, stdout, stderr = run_command("curl -s http://localhost:8001/health")
    if success:
        print("✅ GPU服务连接正常")
    else:
        print("⚠️ GPU服务未运行或不可访问")
    
    return True

def generate_upgrade_report(backup_dir):
    """生成升级报告"""
    print("\n📋 生成升级报告...")
    
    report_file = f"{backup_dir}/upgrade_report.txt"
    
    with open(report_file, "w", encoding="utf-8") as f:
        f.write("现代化依赖升级报告\n")
        f.write("=" * 50 + "\n")
        f.write(f"升级时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"备份目录: {backup_dir}\n\n")
        
        f.write("升级策略:\n")
        f.write("- 使用 pip install -U 升级到最新稳定版本\n")
        f.write("- LlamaIndex 从 0.10.57 升级到 0.12.x\n")
        f.write("- 所有依赖升级到最新稳定版本\n")
        f.write("- 保持CPU和GPU服务版本一致性\n\n")
        
        # 获取当前版本信息
        f.write("当前版本信息:\n")
        success, stdout, stderr = run_command("pip freeze")
        if success:
            f.write(stdout)
        
    print(f"✅ 升级报告已生成: {report_file}")

def rollback_if_needed(backup_dir):
    """如果需要则回滚"""
    print(f"\n🔄 回滚到备份: {backup_dir}")
    
    try:
        # 恢复环境
        env_backup = f"{backup_dir}/current_environment.txt"
        if os.path.exists(env_backup):
            success, stdout, stderr = run_command(f"pip install -r {env_backup} --force-reinstall")
            if success:
                print("✅ 环境回滚成功")
                return True
            else:
                print(f"❌ 环境回滚失败: {stderr}")
                return False
        else:
            print("❌ 备份文件不存在")
            return False
    except Exception as e:
        print(f"❌ 回滚过程出错: {e}")
        return False

def main():
    """主函数"""
    print("🚀 现代化依赖升级脚本")
    print("=" * 50)
    print("🎯 升级策略:")
    print("  - 使用 pip install -U 升级到最新稳定版本")
    print("  - LlamaIndex 0.10.57 → 0.12.x (重大功能改进)")
    print("  - 所有依赖升级到最新稳定版本")
    print("  - 保持版本一致性和兼容性")
    
    # 检查工作目录
    if not os.path.exists('cpu-service/requirements.txt'):
        print("❌ 请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 确认升级
    try:
        confirm = input("\n确认开始现代化升级? (y/N): ").lower().strip()
        if confirm != 'y':
            print("❌ 操作取消")
            sys.exit(0)
    except KeyboardInterrupt:
        print("\n❌ 操作取消")
        sys.exit(0)
    
    start_time = datetime.now()
    
    try:
        # 1. 备份环境
        backup_dir = backup_environment()
        if not backup_dir:
            print("❌ 备份失败，停止升级")
            sys.exit(1)
        
        # 2. 升级pip
        if not upgrade_pip():
            print("❌ pip升级失败")
            sys.exit(1)
        
        # 3. 现代化升级CPU服务
        if not modern_upgrade_cpu_service():
            print("❌ CPU服务升级失败，正在回滚...")
            rollback_if_needed(backup_dir)
            sys.exit(1)
        
        # 4. 检查LlamaIndex兼容性
        if not check_llama_index_compatibility():
            print("❌ LlamaIndex兼容性检查失败，正在回滚...")
            rollback_if_needed(backup_dir)
            sys.exit(1)
        
        # 5. 检查版本升级情况
        check_version_upgrades()
        
        # 6. 测试服务功能
        if not test_service_functionality():
            print("⚠️ 服务功能测试有问题，但升级成功")
        
        # 7. 生成升级报告
        generate_upgrade_report(backup_dir)
        
        # 成功完成
        end_time = datetime.now()
        duration = end_time - start_time
        
        print("\n" + "=" * 50)
        print("✅ 现代化依赖升级完成！")
        print(f"⏱️ 总耗时: {duration}")
        print(f"📦 备份位置: {backup_dir}")
        print("\n📋 下一步操作:")
        print("1. 重建GPU服务Docker镜像")
        print("2. 重启CPU服务进行完整测试")
        print("3. 运行业务功能测试")
        print("4. 如有问题，使用以下命令回滚:")
        print(f"   python scripts/rollback_dependencies.py {backup_dir}")
    
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断，正在回滚...")
        if 'backup_dir' in locals():
            rollback_if_needed(backup_dir)
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 升级过程出错: {e}")
        if 'backup_dir' in locals():
            rollback_if_needed(backup_dir)
        sys.exit(1)

if __name__ == "__main__":
    main()
