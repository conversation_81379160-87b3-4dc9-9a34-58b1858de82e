#!/usr/bin/env python3
"""
依赖回滚脚本 - 回滚到指定的备份版本
更新时间：2025-06-12
"""

import subprocess
import sys
import os
import shutil
from pathlib import Path

def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            cwd=cwd,
            timeout=600
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "命令执行超时"
    except Exception as e:
        return False, "", str(e)

def rollback_to_backup(backup_dir):
    """回滚到指定备份"""
    print(f"🔄 回滚到备份: {backup_dir}")
    
    # 检查备份目录是否存在
    if not os.path.exists(backup_dir):
        print(f"❌ 备份目录不存在: {backup_dir}")
        return False
    
    try:
        # 1. 恢复requirements文件
        print("📋 恢复requirements文件...")
        
        cpu_backup = os.path.join(backup_dir, "cpu_requirements_original.txt")
        gpu_backup = os.path.join(backup_dir, "gpu_requirements_original.txt")
        
        if os.path.exists(cpu_backup):
            shutil.copy2(cpu_backup, "cpu-service/requirements.txt")
            print("✅ CPU服务requirements.txt已恢复")
        else:
            print("⚠️ CPU服务备份文件不存在")
        
        if os.path.exists(gpu_backup):
            shutil.copy2(gpu_backup, "gpu-service/requirements.txt")
            print("✅ GPU服务requirements.txt已恢复")
        else:
            print("⚠️ GPU服务备份文件不存在")
        
        # 2. 恢复Python环境
        print("🐍 恢复Python环境...")
        
        env_backup = os.path.join(backup_dir, "current_environment.txt")
        if os.path.exists(env_backup):
            # 卸载当前包
            print("📦 卸载当前包...")
            success, stdout, stderr = run_command("pip freeze > temp_current.txt")
            if success:
                success, stdout, stderr = run_command("pip uninstall -y -r temp_current.txt")
                if success:
                    print("✅ 当前包已卸载")
                else:
                    print(f"⚠️ 卸载包时有警告: {stderr}")
            
            # 安装备份环境
            print("📦 安装备份环境...")
            success, stdout, stderr = run_command(f"pip install -r {env_backup}")
            if success:
                print("✅ 备份环境已恢复")
                return True
            else:
                print(f"❌ 恢复备份环境失败: {stderr}")
                return False
        else:
            print("❌ 环境备份文件不存在")
            return False
    
    except Exception as e:
        print(f"❌ 回滚过程出错: {e}")
        return False
    
    finally:
        # 清理临时文件
        if os.path.exists("temp_current.txt"):
            os.remove("temp_current.txt")

def list_available_backups():
    """列出可用的备份"""
    print("📋 可用的备份:")
    
    backup_base = "backups"
    if not os.path.exists(backup_base):
        print("❌ 没有找到备份目录")
        return []
    
    backups = []
    for item in os.listdir(backup_base):
        backup_path = os.path.join(backup_base, item)
        if os.path.isdir(backup_path) and item.startswith("dependencies_"):
            backups.append(backup_path)
            print(f"  - {backup_path}")
    
    return backups

def main():
    """主函数"""
    print("🔄 依赖回滚脚本")
    print("=" * 50)
    
    # 检查工作目录
    if not os.path.exists('cpu-service/requirements.txt'):
        print("❌ 请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 获取备份目录参数
    if len(sys.argv) > 1:
        backup_dir = sys.argv[1]
    else:
        # 列出可用备份
        backups = list_available_backups()
        if not backups:
            print("❌ 没有可用的备份")
            sys.exit(1)
        
        print("\n请选择要回滚的备份:")
        for i, backup in enumerate(backups, 1):
            print(f"{i}. {backup}")
        
        try:
            choice = int(input("\n请输入选择 (1-{}): ".format(len(backups))))
            if 1 <= choice <= len(backups):
                backup_dir = backups[choice - 1]
            else:
                print("❌ 无效选择")
                sys.exit(1)
        except (ValueError, KeyboardInterrupt):
            print("\n❌ 操作取消")
            sys.exit(1)
    
    # 确认回滚
    print(f"\n⚠️ 即将回滚到: {backup_dir}")
    print("这将:")
    print("1. 恢复原始的requirements.txt文件")
    print("2. 卸载当前所有Python包")
    print("3. 重新安装备份时的包版本")
    
    try:
        confirm = input("\n确认继续? (y/N): ").lower().strip()
        if confirm != 'y':
            print("❌ 操作取消")
            sys.exit(0)
    except KeyboardInterrupt:
        print("\n❌ 操作取消")
        sys.exit(0)
    
    # 执行回滚
    if rollback_to_backup(backup_dir):
        print("\n" + "=" * 50)
        print("✅ 回滚完成！")
        print("\n📋 下一步操作:")
        print("1. 重启CPU服务")
        print("2. 重新构建GPU服务Docker镜像")
        print("3. 运行测试验证功能正常")
    else:
        print("\n❌ 回滚失败！")
        print("请手动检查环境状态")
        sys.exit(1)

if __name__ == "__main__":
    main()
