#!/usr/bin/env python3
"""
配置管理优化脚本

基于问题分析，优化项目的配置管理机制
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def analyze_current_config():
    """分析当前配置状态"""
    print("=" * 60)
    print("当前配置状态分析")
    print("=" * 60)
    
    # 检查配置文件
    config_files = [
        "configs/.env.local",
        "configs/.env.dev", 
        "configs/.env.prod",
        "configs/.env.test",
        ".env.example"
    ]
    
    print("配置文件状态:")
    for config_file in config_files:
        file_path = project_root / config_file
        if file_path.exists():
            print(f"  [存在] {config_file}")
            
            # 检查关键配置
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                if 'MILVUS_URI=' in content:
                    for line in content.split('\n'):
                        if line.strip().startswith('MILVUS_URI='):
                            print(f"     MILVUS_URI: {line.split('=', 1)[1]}")
                            break
            except Exception as e:
                print(f"     读取失败: {e}")
        else:
            print(f"  [缺失] {config_file}")

def check_default_values():
    """检查默认值设置"""
    print("\n" + "=" * 60)
    print("默认值检查")
    print("=" * 60)
    
    try:
        from shared.config.settings import Settings
        
        # 创建一个不加载任何配置文件的实例
        default_settings = Settings()
        
        print("当前默认值:")
        print(f"  MILVUS_URI: {default_settings.MILVUS_URI}")
        print(f"  MILVUS_HOST: {default_settings.MILVUS_HOST}")
        print(f"  MILVUS_PORT: {default_settings.MILVUS_PORT}")
        print(f"  GPU_SERVICE_URL: {default_settings.GPU_SERVICE_URL}")
        
        # 分析默认值合理性
        print("\n默认值分析:")
        if "localhost" in default_settings.MILVUS_URI:
            print("  ✅ MILVUS_URI 使用 localhost（适合本地开发）")
        else:
            print("  ⚠️ MILVUS_URI 不是 localhost（可能不适合作为默认值）")
            
        if default_settings.GPU_SERVICE_URL and "localhost" in default_settings.GPU_SERVICE_URL:
            print("  ✅ GPU_SERVICE_URL 使用 localhost（适合本地开发）")
        elif not default_settings.GPU_SERVICE_URL:
            print("  ✅ GPU_SERVICE_URL 为空（将根据环境动态设置）")
        else:
            print("  ⚠️ GPU_SERVICE_URL 不是 localhost（可能不适合作为默认值）")
            
    except Exception as e:
        print(f"检查默认值失败: {e}")

def test_config_priority():
    """测试配置优先级"""
    print("\n" + "=" * 60)
    print("配置优先级测试")
    print("=" * 60)
    
    try:
        # 清除缓存
        from shared.config.settings import get_settings
        get_settings.cache_clear()
        
        # 测试不同环境的配置加载
        environments = ["development", "testing", "production"]
        
        for env in environments:
            print(f"\n测试环境: {env}")
            
            # 临时设置环境变量
            original_env = os.getenv("ENVIRONMENT")
            os.environ["ENVIRONMENT"] = env
            
            try:
                # 清除缓存并重新加载
                get_settings.cache_clear()
                settings = get_settings()
                
                print(f"  MILVUS_URI: {settings.MILVUS_URI}")
                print(f"  GPU_SERVICE_URL: {settings.GPU_SERVICE_URL}")
                
            except Exception as e:
                print(f"  加载失败: {e}")
            finally:
                # 恢复原始环境变量
                if original_env:
                    os.environ["ENVIRONMENT"] = original_env
                else:
                    os.environ.pop("ENVIRONMENT", None)
                    
    except Exception as e:
        print(f"配置优先级测试失败: {e}")

def suggest_optimizations():
    """提供优化建议"""
    print("\n" + "=" * 60)
    print("配置优化建议")
    print("=" * 60)
    
    print("1. 默认值策略:")
    print("   ✅ 保持 localhost 作为默认值（适合本地开发）")
    print("   ✅ 通过配置文件覆盖生产环境设置")
    print("   ✅ 使用环境变量进行最终覆盖")
    
    print("\n2. 配置文件完整性:")
    config_files = [
        ("configs/.env.local", "本地开发环境"),
        ("configs/.env.test", "测试环境"),
        ("configs/.env.prod", "生产环境"),
        (".env.example", "配置模板")
    ]
    
    for config_file, description in config_files:
        file_path = project_root / config_file
        if file_path.exists():
            print(f"   ✅ {config_file} - {description}")
        else:
            print(f"   ❌ {config_file} - {description} (建议创建)")
    
    print("\n3. 安全性建议:")
    print("   🔒 生产环境使用环境变量而非配置文件")
    print("   🔒 敏感信息（API密钥）不要提交到版本控制")
    print("   🔒 使用 .gitignore 保护本地配置文件")
    
    print("\n4. 维护性建议:")
    print("   📝 添加配置验证机制")
    print("   📝 增强配置加载日志")
    print("   📝 创建配置文档")
    print("   📝 实施配置变更审查流程")

def create_missing_configs():
    """创建缺失的配置文件"""
    print("\n" + "=" * 60)
    print("创建缺失配置文件")
    print("=" * 60)
    
    # 检查并创建 .env.test
    test_config_path = project_root / "configs" / ".env.test"
    if not test_config_path.exists():
        test_config_content = """# AI接诉即办助手 v3.0 - 测试环境配置

# =============================================================================
# 基础配置
# =============================================================================
ENVIRONMENT=testing
DEBUG=false
LOG_LEVEL=INFO

# =============================================================================
# API嵌入配置
# =============================================================================
API_EMBED_MODEL=BAAI/bge-m3
API_EMBED_KEY=sk-test-key
API_EMBED_BASE=https://api.siliconflow.cn/v1
API_EMBED_BATCH_SIZE=4

# =============================================================================
# GPU服务配置
# =============================================================================
GPU_SERVICE_URL=http://test-gpu:8001

# =============================================================================
# CPU服务配置
# =============================================================================
CPU_SERVICE_HOST=0.0.0.0
CPU_SERVICE_PORT=8009
CPU_SERVICE_WORKERS=1

# =============================================================================
# 数据库配置
# =============================================================================
MILVUS_URI=http://test-milvus:19530
MILVUS_DIMENSION=1024

# =============================================================================
# 性能配置
# =============================================================================
REQUEST_TIMEOUT=60
GPU_REQUEST_TIMEOUT=30
MAX_CONCURRENT_REQUESTS=10
"""
        
        with open(test_config_path, 'w', encoding='utf-8') as f:
            f.write(test_config_content)
        print(f"✅ 创建测试环境配置: {test_config_path}")
    else:
        print(f"✅ 测试环境配置已存在: {test_config_path}")

def validate_config_consistency():
    """验证配置一致性"""
    print("\n" + "=" * 60)
    print("配置一致性验证")
    print("=" * 60)
    
    try:
        from shared.config.settings import get_settings
        
        # 测试各环境配置
        environments = ["development", "testing", "production"]
        
        for env in environments:
            print(f"\n验证 {env} 环境:")
            
            # 设置环境
            original_env = os.getenv("ENVIRONMENT")
            os.environ["ENVIRONMENT"] = env
            
            try:
                get_settings.cache_clear()
                settings = get_settings()
                
                # 验证必需配置
                required_configs = [
                    'MILVUS_URI', 'GPU_SERVICE_URL', 'CPU_SERVICE_PORT'
                ]
                
                for config in required_configs:
                    value = getattr(settings, config, None)
                    if value:
                        print(f"  ✅ {config}: {value}")
                    else:
                        print(f"  ❌ {config}: 未设置")
                        
            except Exception as e:
                print(f"  ❌ 配置加载失败: {e}")
            finally:
                # 恢复环境变量
                if original_env:
                    os.environ["ENVIRONMENT"] = original_env
                else:
                    os.environ.pop("ENVIRONMENT", None)
                    
    except Exception as e:
        print(f"配置一致性验证失败: {e}")

def main():
    """主函数"""
    print("AI接诉即办助手v3.0 - 配置管理优化")
    print(f"项目根目录: {project_root}")
    
    # 执行分析和优化
    analyze_current_config()
    check_default_values()
    test_config_priority()
    create_missing_configs()
    validate_config_consistency()
    suggest_optimizations()
    
    print("\n" + "=" * 60)
    print("优化完成")
    print("=" * 60)
    print("\n建议下一步:")
    print("1. 审查生成的配置文件")
    print("2. 根据实际环境调整配置值")
    print("3. 实施配置验证机制")
    print("4. 更新部署文档")

if __name__ == "__main__":
    main()
