#!/usr/bin/env python3
"""
简化版配置分析脚本
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    print("配置管理分析报告")
    print("=" * 50)
    
    # 1. 检查配置文件
    print("\n1. 配置文件状态:")
    config_files = [
        "configs/.env.local",
        "configs/.env.prod", 
        "configs/.env.test",
        ".env.example"
    ]
    
    for config_file in config_files:
        file_path = project_root / config_file
        status = "存在" if file_path.exists() else "缺失"
        print(f"   {config_file}: {status}")
    
    # 2. 检查默认值
    print("\n2. 默认配置值:")
    try:
        from shared.config.settings import Settings
        default_settings = Settings()
        
        print(f"   MILVUS_URI: {default_settings.MILVUS_URI}")
        print(f"   MILVUS_HOST: {default_settings.MILVUS_HOST}")
        print(f"   GPU_SERVICE_URL: {default_settings.GPU_SERVICE_URL}")
        
    except Exception as e:
        print(f"   检查失败: {e}")
    
    # 3. 测试配置加载
    print("\n3. 配置加载测试:")
    try:
        from shared.config.settings import get_settings
        get_settings.cache_clear()
        
        settings = get_settings()
        print(f"   当前MILVUS_URI: {settings.MILVUS_URI}")
        print(f"   当前GPU_SERVICE_URL: {settings.GPU_SERVICE_URL}")
        
    except Exception as e:
        print(f"   加载失败: {e}")
    
    # 4. 分析结果
    print("\n4. 分析结果:")
    print("   问题根源: shared/config/settings.py 中的默认值")
    print("   解决方案: 修复默认值 + 保持配置文件")
    print("   配置优先级: 环境变量 > .env文件 > 默认值")
    
    # 5. 建议
    print("\n5. 配置管理建议:")
    print("   开发环境: 使用 configs/.env.local")
    print("   测试环境: 使用 configs/.env.test") 
    print("   生产环境: 使用环境变量")
    print("   默认值: 保持 localhost (安全回退)")
    
    print("\n" + "=" * 50)
    print("分析完成")

if __name__ == "__main__":
    main()
