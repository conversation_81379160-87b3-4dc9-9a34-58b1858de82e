#!/usr/bin/env python3
"""
配置问题修复脚本

自动修复环境变量配置和Milvus连接问题
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_network_connectivity():
    """测试网络连通性"""
    print("=" * 60)
    print("网络连通性测试")
    print("=" * 60)
    
    # 测试目标IP
    target_ips = [
        "************",
        "localhost", 
        "127.0.0.1"
    ]
    
    reachable_ips = []
    
    for ip in target_ips:
        print(f"\n测试连接: {ip}")
        try:
            # Windows ping命令
            result = subprocess.run(
                ["ping", "-n", "2", ip], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if result.returncode == 0:
                print(f"[成功] {ip} 可达")
                reachable_ips.append(ip)
            else:
                print(f"[失败] {ip} 不可达")
                
        except Exception as e:
            print(f"[错误] 测试 {ip} 失败: {e}")
    
    return reachable_ips

def test_milvus_ports():
    """测试Milvus端口连通性"""
    print("\n" + "=" * 60)
    print("Milvus端口测试")
    print("=" * 60)
    
    import socket
    
    # 测试目标
    targets = [
        ("************", 19530),
        ("localhost", 19530),
        ("127.0.0.1", 19530)
    ]
    
    available_targets = []
    
    for host, port in targets:
        print(f"\n测试端口: {host}:{port}")
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"[成功] {host}:{port} 端口开放")
                available_targets.append((host, port))
            else:
                print(f"[失败] {host}:{port} 端口关闭或不可达")
                
        except Exception as e:
            print(f"[错误] 测试 {host}:{port} 失败: {e}")
    
    return available_targets

def suggest_milvus_solutions():
    """提供Milvus解决方案"""
    print("\n" + "=" * 60)
    print("Milvus解决方案")
    print("=" * 60)
    
    print("1. 检查Milvus服务状态:")
    print("   - 确认 ************ 服务器上的Milvus是否正在运行")
    print("   - 检查Milvus服务端口 19530 是否开放")
    print("   - 验证防火墙设置")
    
    print("\n2. 网络连接问题:")
    print("   - 检查网络连通性: ping ************")
    print("   - 确认VPN或网络配置")
    print("   - 检查路由表设置")
    
    print("\n3. 临时解决方案:")
    print("   - 使用本地Milvus服务 (localhost:19530)")
    print("   - 配置Docker Milvus容器")
    print("   - 使用Milvus Lite (文件模式)")
    
    print("\n4. 配置修改选项:")
    print("   a) 修改为本地Milvus: MILVUS_URI=http://localhost:19530")
    print("   b) 修改为Docker Milvus: MILVUS_URI=http://127.0.0.1:19530")
    print("   c) 使用文件模式: MILVUS_URI=./milvus_data/milvus.db")

def create_local_milvus_config():
    """创建本地Milvus配置"""
    print("\n" + "=" * 60)
    print("创建本地Milvus配置")
    print("=" * 60)
    
    # 创建本地开发配置文件
    local_config_path = project_root / "configs" / ".env.local.backup"
    
    # 备份当前配置
    current_config = project_root / "configs" / ".env.local"
    if current_config.exists():
        import shutil
        shutil.copy2(current_config, local_config_path)
        print(f"[备份] 当前配置已备份到: {local_config_path}")
    
    # 读取当前配置
    with open(current_config, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修改Milvus配置为本地
    updated_content = content.replace(
        "MILVUS_URI=http://************:19530",
        "MILVUS_URI=http://localhost:19530"
    )
    
    # 写入修改后的配置
    with open(current_config, 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    print("[修改] 已将Milvus配置修改为本地模式")
    print("       MILVUS_URI=http://localhost:19530")

def create_docker_milvus_setup():
    """创建Docker Milvus设置脚本"""
    print("\n" + "=" * 60)
    print("创建Docker Milvus设置")
    print("=" * 60)
    
    docker_script = """#!/bin/bash
# Milvus Docker 快速启动脚本

echo "启动Milvus Docker容器..."

# 停止现有容器
docker stop milvus-standalone 2>/dev/null || true
docker rm milvus-standalone 2>/dev/null || true

# 创建数据目录
mkdir -p ./milvus_data

# 启动Milvus容器
docker run -d \\
  --name milvus-standalone \\
  -p 19530:19530 \\
  -p 9091:9091 \\
  -v $(pwd)/milvus_data:/var/lib/milvus \\
  milvusdb/milvus:latest \\
  milvus run standalone

echo "等待Milvus启动..."
sleep 10

echo "检查Milvus状态..."
docker logs milvus-standalone --tail 20

echo "Milvus已启动，访问地址: http://localhost:19530"
"""
    
    script_path = project_root / "scripts" / "start_milvus_docker.sh"
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(docker_script)
    
    print(f"[创建] Docker启动脚本: {script_path}")
    print("使用方法: bash scripts/start_milvus_docker.sh")

def interactive_fix():
    """交互式修复"""
    print("\n" + "=" * 60)
    print("交互式修复向导")
    print("=" * 60)
    
    print("请选择修复方案:")
    print("1. 修改配置为本地Milvus (localhost:19530)")
    print("2. 创建Docker Milvus启动脚本")
    print("3. 保持当前配置 (需要手动解决网络问题)")
    print("4. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-4): ").strip()
            
            if choice == "1":
                create_local_milvus_config()
                print("\n[完成] 配置已修改为本地Milvus模式")
                print("请确保本地Milvus服务正在运行")
                break
                
            elif choice == "2":
                create_docker_milvus_setup()
                print("\n[完成] Docker启动脚本已创建")
                print("请运行脚本启动Milvus容器")
                break
                
            elif choice == "3":
                print("\n[保持] 当前配置未修改")
                print("请手动解决 ************:19530 的网络连接问题")
                break
                
            elif choice == "4":
                print("\n[退出] 未进行任何修改")
                break
                
            else:
                print("无效选择，请输入 1-4")
                
        except KeyboardInterrupt:
            print("\n\n[取消] 用户中断操作")
            break

def main():
    """主函数"""
    print("AI接诉即办助手v3.0 - 配置问题修复工具")
    print(f"项目根目录: {project_root}")
    
    # 执行诊断
    reachable_ips = test_network_connectivity()
    available_targets = test_milvus_ports()
    
    # 分析结果
    if ("************", 19530) in available_targets:
        print("\n[结果] ************:19530 可用，配置正确")
        return
    
    if ("localhost", 19530) in available_targets or ("127.0.0.1", 19530) in available_targets:
        print("\n[结果] 本地Milvus可用，建议修改配置")
    else:
        print("\n[结果] 没有可用的Milvus服务")
    
    # 提供解决方案
    suggest_milvus_solutions()
    
    # 交互式修复
    interactive_fix()
    
    print("\n" + "=" * 60)
    print("修复完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
