@echo off
setlocal

echo ========================================
echo CPU Service Stable Mode (No Reload)
echo ========================================

:: Check project structure
if not exist "cpu-service" (
    echo ERROR: Please run from project root directory
    pause
    exit /b 1
)

:: Check GPU service
echo Checking GPU service...
curl -s http://localhost:8001/health >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: GPU service not running
    pause
    exit /b 1
)
echo OK: GPU service is running

:: Setup virtual environment
if not exist "venv-cpu-dev" (
    echo Creating virtual environment...
    python -m venv venv-cpu-dev
)

:: Activate virtual environment
echo Activating virtual environment...
if exist "venv-cpu-dev\Scripts\activate.bat" (
    call venv-cpu-dev\Scripts\activate.bat
)

:: Install dependencies
pip show fastapi >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing dependencies...
    cd cpu-service
    pip install -r requirements.txt
    cd ..
)

:: Set environment variables
set ENVIRONMENT=development
set PYTHONPATH=%CD%
set GPU_SERVICE_URL=http://localhost:8001

:: Start service
echo.
echo ========================================
echo Starting CPU Service
echo ========================================
echo Service URL: http://localhost:8009
echo API Docs:    http://localhost:8009/docs
echo Mode:        Production-ready (no hot reload)
echo Note:        Restart manually after code changes
echo ========================================

cd cpu-service
python main.py

echo.
echo Service stopped
pause
