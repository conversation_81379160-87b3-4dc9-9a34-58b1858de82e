#!/bin/bash

# AI接诉即办助手 v3.0 - Linux Docker启动脚本
# 用于启动和管理Docker容器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查Docker环境
check_docker() {
    log_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker version &> /dev/null; then
        log_error "Docker未运行，请启动Docker服务"
        exit 1
    fi
    
    log_success "Docker环境正常"
}

# 检查docker-compose
check_compose() {
    if command -v docker-compose &> /dev/null; then
        COMPOSE_CMD="docker-compose"
    elif docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    else
        log_error "docker-compose未安装，请先安装docker-compose"
        exit 1
    fi
    
    log_info "使用命令: $COMPOSE_CMD"
}

# 显示主菜单
show_menu() {
    clear
    echo "========================================"
    echo "AI接诉即办助手 v3.0 - Docker容器管理"
    echo "========================================"
    echo ""
    echo "请选择操作:"
    echo "  1. 启动所有服务"
    echo "  2. 启动CPU服务"
    echo "  3. 启动GPU服务"
    echo "  4. 查看服务状态"
    echo "  5. 查看服务日志"
    echo "  6. 停止所有服务"
    echo "  7. 重启所有服务"
    echo "  8. 清理停止的容器"
    echo "  0. 退出"
    echo ""
}

# 启动所有服务
start_all_services() {
    log_info "启动所有服务..."
    
    if $COMPOSE_CMD -f deployment/docker-compose.yml up -d; then
        log_success "所有服务启动成功"
        echo ""
        log_info "服务端点:"
        echo "  主服务: http://localhost:8009"
        echo "  GPU服务: http://localhost:8001"
        echo "  API文档: http://localhost:8009/docs"
        echo ""
        log_info "健康检查:"
        echo "  curl http://localhost:8009/health"
        echo "  curl http://localhost:8001/health"
    else
        log_error "服务启动失败"
    fi
}

# 启动CPU服务
start_cpu_service() {
    log_info "启动CPU服务..."
    
    if $COMPOSE_CMD -f deployment/docker-compose.yml up -d cpu-service; then
        log_success "CPU服务启动成功"
        log_info "服务地址: http://localhost:8000"
    else
        log_error "CPU服务启动失败"
    fi
}

# 启动GPU服务
start_gpu_service() {
    log_info "启动GPU服务..."
    
    if $COMPOSE_CMD -f deployment/docker-compose.yml up -d gpu-service; then
        log_success "GPU服务启动成功"
        log_info "服务地址: http://localhost:8001"
    else
        log_error "GPU服务启动失败"
    fi
}

# 查看服务状态
show_status() {
    log_info "服务状态:"
    $COMPOSE_CMD -f deployment/docker-compose.yml ps
    echo ""
    log_info "容器资源使用:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
}

# 查看服务日志
show_logs() {
    echo ""
    echo "请选择查看日志的服务:"
    echo "  1. 所有服务日志"
    echo "  2. CPU服务日志"
    echo "  3. GPU服务日志"
    echo "  4. 返回主菜单"
    echo ""
    read -p "请选择 (1-4): " log_choice
    
    case $log_choice in
        1)
            log_info "显示所有服务日志 (按Ctrl+C退出)..."
            $COMPOSE_CMD -f deployment/docker-compose.yml logs -f
            ;;
        2)
            log_info "显示CPU服务日志 (按Ctrl+C退出)..."
            $COMPOSE_CMD -f deployment/docker-compose.yml logs -f cpu-service
            ;;
        3)
            log_info "显示GPU服务日志 (按Ctrl+C退出)..."
            $COMPOSE_CMD -f deployment/docker-compose.yml logs -f gpu-service
            ;;
        4)
            return
            ;;
        *)
            log_error "无效选择"
            ;;
    esac
}

# 停止所有服务
stop_all_services() {
    log_info "停止所有服务..."
    
    if $COMPOSE_CMD -f deployment/docker-compose.yml down; then
        log_success "所有服务已停止"
    else
        log_error "停止服务时出现错误"
    fi
}

# 重启所有服务
restart_all_services() {
    log_info "重启所有服务..."
    
    if $COMPOSE_CMD -f deployment/docker-compose.optimized.yml restart; then
        log_success "所有服务已重启"
    else
        log_error "重启服务时出现错误"
    fi
}

# 清理容器
cleanup_containers() {
    log_info "清理停止的容器和未使用的镜像..."
    docker container prune -f
    docker image prune -f
    log_success "清理完成"
}

# 主函数
main() {
    # 检查环境
    check_docker
    check_compose
    
    while true; do
        show_menu
        read -p "请输入选择 (0-8): " choice
        
        case $choice in
            1)
                start_all_services
                read -p "按回车键继续..."
                ;;
            2)
                start_cpu_service
                read -p "按回车键继续..."
                ;;
            3)
                start_gpu_service
                read -p "按回车键继续..."
                ;;
            4)
                show_status
                read -p "按回车键继续..."
                ;;
            5)
                show_logs
                ;;
            6)
                stop_all_services
                read -p "按回车键继续..."
                ;;
            7)
                restart_all_services
                read -p "按回车键继续..."
                ;;
            8)
                cleanup_containers
                read -p "按回车键继续..."
                ;;
            0)
                echo ""
                log_success "感谢使用AI接诉即办助手 v3.0 Docker管理工具！"
                exit 0
                ;;
            *)
                log_error "无效选择，请输入0-8之间的数字"
                read -p "按回车键继续..."
                ;;
        esac
    done
}

# 运行主函数
main "$@"
