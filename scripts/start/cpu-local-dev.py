#!/usr/bin/env python3
"""
CPU服务本地开发启动脚本

用于在GPU服务Docker部署的情况下，启动本地CPU服务进行开发调试
"""

import os
import sys
import subprocess
import time
import requests
import json
from pathlib import Path
from typing import Optional

class CPULocalDev:
    """CPU服务本地开发管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.cpu_service_dir = self.project_root / "cpu-service"
        self.venv_path = self.project_root / ".venv"
        self.config_file = self.project_root / "configs" / ".env.dev-local"
        
    def check_prerequisites(self) -> bool:
        """检查前置条件"""
        print("🔍 检查前置条件...")
        
        # 检查项目结构
        if not self.cpu_service_dir.exists():
            print("❌ cpu-service目录不存在")
            return False
            
        if not (self.cpu_service_dir / "main.py").exists():
            print("❌ cpu-service/main.py不存在")
            return False
            
        if not (self.cpu_service_dir / "requirements.txt").exists():
            print("❌ cpu-service/requirements.txt不存在")
            return False
            
        print("✅ 项目结构检查通过")
        return True
    
    def check_gpu_service(self) -> bool:
        """检查GPU服务状态"""
        print("🔍 检查GPU服务状态...")
        
        try:
            # 检查GPU服务健康状态
            response = requests.get("http://localhost:8001/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ GPU服务运行正常: {data.get('status', 'unknown')}")
                return True
            else:
                print(f"❌ GPU服务响应异常: {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            print("❌ 无法连接到GPU服务 (http://localhost:8001)")
            print("   请确保GPU服务Docker容器正在运行")
            return False
        except Exception as e:
            print(f"❌ GPU服务检查失败: {str(e)}")
            return False
    
    def setup_virtual_environment(self) -> bool:
        """设置Python虚拟环境"""
        print("🐍 设置Python虚拟环境...")
        
        # 检查虚拟环境是否存在
        if self.venv_path.exists():
            print("✅ 虚拟环境已存在")
            return True
            
        try:
            # 创建虚拟环境
            print("正在创建虚拟环境...")
            subprocess.run([
                sys.executable, "-m", "venv", str(self.venv_path)
            ], check=True, cwd=self.project_root)
            
            print("✅ 虚拟环境创建成功")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 虚拟环境创建失败: {e}")
            return False
    
    def install_dependencies(self) -> bool:
        """安装依赖"""
        print("📦 安装依赖...")

        # 检查是否已在虚拟环境中
        in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)

        if in_venv:
            # 如果已在虚拟环境中，直接使用当前的python和pip
            python_exe = sys.executable
            pip_exe = "pip"
            print("✅ 检测到已激活虚拟环境，使用当前环境")
        else:
            # 获取虚拟环境中的Python可执行文件路径
            if os.name == 'nt':  # Windows
                python_exe = self.venv_path / "Scripts" / "python.exe"
                pip_exe = self.venv_path / "Scripts" / "pip.exe"
            else:  # Linux/Mac
                python_exe = self.venv_path / "bin" / "python"
                pip_exe = self.venv_path / "bin" / "pip"

            if not Path(python_exe).exists():
                print("❌ 虚拟环境Python不存在")
                return False

            python_exe = str(python_exe)
            pip_exe = str(pip_exe)

        try:
            # 升级pip
            print("正在升级pip...")
            subprocess.run([
                python_exe, "-m", "pip", "install", "--upgrade", "pip",
                "-i", "https://pypi.tuna.tsinghua.edu.cn/simple"
            ], check=True, cwd=self.cpu_service_dir)

            # 安装CPU服务依赖
            print("正在安装依赖...")
            subprocess.run([
                python_exe, "-m", "pip", "install", "-r", "requirements.txt",
                "-i", "https://pypi.tuna.tsinghua.edu.cn/simple"
            ], check=True, cwd=self.cpu_service_dir)

            # 安装开发工具
            print("正在安装开发工具...")
            subprocess.run([
                python_exe, "-m", "pip", "install", "ipython", "debugpy", "pytest",
                "-i", "https://pypi.tuna.tsinghua.edu.cn/simple"
            ], check=True, cwd=self.cpu_service_dir)

            print("✅ 依赖安装完成")
            return True

        except subprocess.CalledProcessError as e:
            print(f"❌ 依赖安装失败: {e}")
            return False
    
    def create_dev_config(self) -> bool:
        """创建开发配置文件"""
        print("⚙️ 创建开发配置文件...")
        
        # 确保configs目录存在
        configs_dir = self.project_root / "configs"
        configs_dir.mkdir(exist_ok=True)
        
        # 检查是否已存在
        if self.config_file.exists():
            print("✅ 开发配置文件已存在")
            return True
            
        # 创建配置文件内容
        config_content = """# CPU服务本地开发配置
# AI接诉即办助手 v3.0 - GPU+CPU分离架构

# =============================================================================
# 基础配置
# =============================================================================
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG

# =============================================================================
# CPU服务配置
# =============================================================================
CPU_SERVICE_HOST=0.0.0.0
CPU_SERVICE_PORT=8009
CPU_SERVICE_WORKERS=1

# =============================================================================
# GPU服务配置（连接Docker部署的GPU服务）
# =============================================================================
GPU_SERVICE_URL=http://localhost:8001

# =============================================================================
# API嵌入配置（SiliconFlow）
# =============================================================================
API_EMBED_MODEL=BAAI/bge-m3
API_EMBED_KEY=sk-hconcoielayrcqyfoqibziztdnchplrhkmupsvddcfkwehrd
API_EMBED_BASE=https://api.siliconflow.cn/v1
API_EMBED_BATCH_SIZE=4

# =============================================================================
# Milvus配置
# =============================================================================
MILVUS_URI=http://localhost:19530
MILVUS_DIMENSION=1024

# =============================================================================
# 开发调试配置
# =============================================================================
CORS_ORIGINS=http://localhost:3000,http://localhost:8080,http://127.0.0.1:3000
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=*

# =============================================================================
# 性能配置（开发环境优化）
# =============================================================================
REQUEST_TIMEOUT=60
GPU_REQUEST_TIMEOUT=30
MAX_CONCURRENT_REQUESTS=5
"""
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                f.write(config_content)
            print("✅ 开发配置文件创建成功")
            return True
        except Exception as e:
            print(f"❌ 配置文件创建失败: {e}")
            return False
    
    def start_cpu_service(self) -> bool:
        """启动CPU服务"""
        print("🚀 启动CPU服务...")

        # 检查是否已在虚拟环境中
        in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)

        if in_venv:
            # 如果已在虚拟环境中，直接使用当前的python
            python_exe = sys.executable
            print("✅ 使用当前激活的虚拟环境")
        else:
            # 获取虚拟环境中的Python可执行文件路径
            if os.name == 'nt':  # Windows
                python_exe = self.venv_path / "Scripts" / "python.exe"
            else:  # Linux/Mac
                python_exe = self.venv_path / "bin" / "python"
            python_exe = str(python_exe)

        # 设置环境变量
        env = os.environ.copy()
        env['ENVIRONMENT'] = 'development'
        env['PYTHONPATH'] = str(self.project_root)
        
        try:
            print("\n" + "="*40)
            print("Starting CPU Service")
            print("="*40)
            print(f"Service URL: http://localhost:8009")
            print(f"API Docs:    http://localhost:8009/docs")
            print(f"Mode:        Production-ready (no hot reload)")
            print(f"Note:        Restart manually after code changes")
            print("="*40)

            # 启动服务
            subprocess.run([
                python_exe, "main.py"
            ], cwd=self.cpu_service_dir, env=env)
            
            return True
            
        except KeyboardInterrupt:
            print("\n🛑 服务已停止")
            return True
        except Exception as e:
            print(f"❌ 服务启动失败: {e}")
            return False
    
    def test_services(self) -> bool:
        """测试服务连接"""
        print("🧪 测试服务连接...")
        
        # 等待服务启动
        time.sleep(3)
        
        try:
            # 测试CPU服务
            response = requests.get("http://localhost:8009/health", timeout=10)
            if response.status_code == 200:
                print("✅ CPU服务连接正常")
            else:
                print(f"❌ CPU服务响应异常: {response.status_code}")
                return False
                
            # 测试GPU服务连接
            response = requests.get("http://localhost:8009/v2/status", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print("✅ CPU-GPU服务通信正常")
                print(f"   GPU服务状态: {data.get('gpu_service_status', 'unknown')}")
            else:
                print("⚠️ CPU-GPU服务通信可能有问题")
                
            return True
            
        except Exception as e:
            print(f"❌ 服务测试失败: {e}")
            return False
    
    def show_usage_info(self):
        """显示使用信息"""
        print("\n" + "="*60)
        print("🎉 CPU服务本地开发环境准备完成！")
        print("="*60)
        print("\n📚 快速导航:")
        print("   CPU服务首页: http://localhost:8009")
        print("   健康检查:   http://localhost:8009/health")
        print("   API文档:    http://localhost:8009/docs")
        print("   ReDoc文档:  http://localhost:8009/redoc")
        print("\n🧪 测试命令:")
        print("   # 健康检查")
        print("   curl http://localhost:8009/health")
        print("\n   # 测试检索")
        print("   curl -X POST http://localhost:8009/v2/retrieval/search \\")
        print("     -H \"Content-Type: application/json\" \\")
        print("     -d '{\"query\": \"测试查询\", \"collection\": \"department\"}'")
        print("\n💡 开发提示:")
        print("   - 生产级稳定模式（无热重载）")
        print("   - 日志级别设为DEBUG，便于调试")
        print("   - 支持IDE断点调试")
        print("   - 代码修改后需手动重启服务")
        print("   - GPU服务运行在Docker中，CPU服务本地运行")
        print("\n🔧 故障排除:")
        print("   - 确保GPU服务Docker容器正在运行")
        print("   - 检查端口8009是否被占用")
        print("   - 查看终端日志获取详细错误信息")
        print("\n" + "="*60)
    
    def run(self):
        """运行完整的设置和启动流程"""
        print("🚀 CPU服务本地开发环境设置")
        print("="*50)
        
        # 检查前置条件
        if not self.check_prerequisites():
            return False
            
        # 检查GPU服务
        if not self.check_gpu_service():
            print("\n💡 提示: 请先启动GPU服务Docker容器")
            print("   docker-compose up -d gpu-service")
            return False
            
        # 设置虚拟环境
        if not self.setup_virtual_environment():
            return False
            
        # 安装依赖
        if not self.install_dependencies():
            return False
            
        # 创建配置文件
        if not self.create_dev_config():
            return False
            
        # 显示使用信息
        self.show_usage_info()
        
        # 询问是否立即启动
        try:
            choice = input("\n是否立即启动CPU服务? (y/n): ").lower().strip()
            if choice in ['y', 'yes', '']:
                return self.start_cpu_service()
            else:
                print("\n✅ 环境准备完成，可以手动启动CPU服务:")
                print(f"   cd {self.cpu_service_dir}")
                print("   python main.py")
                return True
        except KeyboardInterrupt:
            print("\n\n👋 设置已完成，随时可以启动CPU服务")
            return True


def main():
    """主函数"""
    dev_manager = CPULocalDev()
    success = dev_manager.run()
    
    if not success:
        print("\n❌ 设置过程中遇到问题，请检查错误信息")
        sys.exit(1)
    else:
        print("\n✅ 设置完成")


if __name__ == "__main__":
    main()
