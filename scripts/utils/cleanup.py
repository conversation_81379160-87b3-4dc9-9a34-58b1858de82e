#!/usr/bin/env python3
"""
AI接诉即办助手 v3.0 - 清理工具
清理Docker容器、镜像、缓存和日志文件
"""

import os
import sys
import subprocess
import shutil
import argparse
from pathlib import Path
from typing import List, Dict

class CleanupTool:
    """清理工具类"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.dry_run = False
    
    def set_dry_run(self, dry_run: bool):
        """设置试运行模式"""
        self.dry_run = dry_run
        if dry_run:
            print("🔍 试运行模式：只显示将要执行的操作，不实际执行")
    
    def run_command(self, cmd: List[str], description: str) -> bool:
        """执行命令"""
        print(f"🔧 {description}")
        print(f"   命令: {' '.join(cmd)}")
        
        if self.dry_run:
            print("   [试运行] 跳过执行")
            return True
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print("   ✅ 成功")
                if result.stdout.strip():
                    print(f"   输出: {result.stdout.strip()}")
                return True
            else:
                print(f"   ❌ 失败: {result.stderr.strip()}")
                return False
        except Exception as e:
            print(f"   ❌ 错误: {e}")
            return False
    
    def get_docker_info(self) -> Dict:
        """获取Docker信息"""
        info = {
            'containers': [],
            'images': [],
            'volumes': [],
            'networks': []
        }
        
        try:
            # 获取容器信息
            result = subprocess.run([
                "docker", "ps", "-a", "--format", 
                "{{.ID}}\t{{.Names}}\t{{.Status}}\t{{.Image}}"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                for line in result.stdout.strip().split('\n'):
                    if line:
                        parts = line.split('\t')
                        if len(parts) >= 4:
                            info['containers'].append({
                                'id': parts[0],
                                'name': parts[1],
                                'status': parts[2],
                                'image': parts[3]
                            })
            
            # 获取镜像信息
            result = subprocess.run([
                "docker", "images", "--format",
                "{{.ID}}\t{{.Repository}}\t{{.Tag}}\t{{.Size}}"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                for line in result.stdout.strip().split('\n'):
                    if line:
                        parts = line.split('\t')
                        if len(parts) >= 4:
                            info['images'].append({
                                'id': parts[0],
                                'repository': parts[1],
                                'tag': parts[2],
                                'size': parts[3]
                            })
            
        except Exception as e:
            print(f"⚠️  获取Docker信息失败: {e}")
        
        return info
    
    def cleanup_docker_containers(self, all_containers: bool = False) -> bool:
        """清理Docker容器"""
        print("\n🗑️  清理Docker容器")
        print("-" * 40)
        
        # 获取容器信息
        info = self.get_docker_info()
        containers = info['containers']
        
        if not containers:
            print("📊 没有找到容器")
            return True
        
        # 显示容器列表
        print("📊 当前容器:")
        for container in containers:
            status_icon = "🟢" if "Up" in container['status'] else "🔴"
            print(f"   {status_icon} {container['name']} ({container['id'][:12]}) - {container['status']}")
        
        # 停止运行中的AI项目容器
        ai_containers = [c for c in containers if 'ai-v3' in c['name']]
        if ai_containers:
            print(f"\n🛑 停止AI项目容器 ({len(ai_containers)}个)")
            for container in ai_containers:
                if "Up" in container['status']:
                    self.run_command(
                        ["docker", "stop", container['id']],
                        f"停止容器 {container['name']}"
                    )
        
        # 删除停止的容器
        if all_containers:
            return self.run_command(
                ["docker", "container", "prune", "-f"],
                "删除所有停止的容器"
            )
        else:
            # 只删除AI项目容器
            stopped_ai_containers = [
                c for c in ai_containers 
                if "Exited" in c['status'] or "Created" in c['status']
            ]
            
            if stopped_ai_containers:
                print(f"\n🗑️  删除停止的AI项目容器 ({len(stopped_ai_containers)}个)")
                for container in stopped_ai_containers:
                    self.run_command(
                        ["docker", "rm", container['id']],
                        f"删除容器 {container['name']}"
                    )
                return True
            else:
                print("📊 没有需要删除的停止容器")
                return True
    
    def cleanup_docker_images(self, all_images: bool = False) -> bool:
        """清理Docker镜像"""
        print("\n🗑️  清理Docker镜像")
        print("-" * 40)
        
        if all_images:
            # 清理所有未使用的镜像
            return self.run_command(
                ["docker", "image", "prune", "-a", "-f"],
                "删除所有未使用的镜像"
            )
        else:
            # 只清理悬空镜像
            success1 = self.run_command(
                ["docker", "image", "prune", "-f"],
                "删除悬空镜像"
            )
            
            # 清理AI项目的旧镜像版本
            info = self.get_docker_info()
            ai_images = [
                img for img in info['images'] 
                if 'ai-v3' in img['repository'] and img['tag'] not in ['optimized', 'latest']
            ]
            
            if ai_images:
                print(f"\n🗑️  清理AI项目旧镜像 ({len(ai_images)}个)")
                for image in ai_images:
                    self.run_command(
                        ["docker", "rmi", image['id']],
                        f"删除镜像 {image['repository']}:{image['tag']}"
                    )
            
            return success1
    
    def cleanup_docker_volumes(self) -> bool:
        """清理Docker卷"""
        print("\n🗑️  清理Docker卷")
        print("-" * 40)
        
        return self.run_command(
            ["docker", "volume", "prune", "-f"],
            "删除未使用的Docker卷"
        )
    
    def cleanup_docker_networks(self) -> bool:
        """清理Docker网络"""
        print("\n🗑️  清理Docker网络")
        print("-" * 40)
        
        return self.run_command(
            ["docker", "network", "prune", "-f"],
            "删除未使用的Docker网络"
        )
    
    def cleanup_build_cache(self) -> bool:
        """清理构建缓存"""
        print("\n🗑️  清理构建缓存")
        print("-" * 40)
        
        return self.run_command(
            ["docker", "builder", "prune", "-a", "-f"],
            "清理Docker构建缓存"
        )
    
    def cleanup_logs(self, max_size_mb: int = 100) -> bool:
        """清理日志文件"""
        print(f"\n🗑️  清理日志文件 (保留最近{max_size_mb}MB)")
        print("-" * 40)
        
        logs_dir = self.project_root / "logs"
        if not logs_dir.exists():
            print("📊 没有找到日志目录")
            return True
        
        total_size = 0
        log_files = []
        
        # 收集日志文件信息
        for log_file in logs_dir.glob("*.log"):
            size = log_file.stat().st_size
            total_size += size
            log_files.append({
                'path': log_file,
                'size': size,
                'mtime': log_file.stat().st_mtime
            })
        
        if not log_files:
            print("📊 没有找到日志文件")
            return True
        
        # 按修改时间排序（最新的在前）
        log_files.sort(key=lambda x: x['mtime'], reverse=True)
        
        total_size_mb = total_size / (1024 * 1024)
        print(f"📊 找到 {len(log_files)} 个日志文件，总大小: {total_size_mb:.2f}MB")
        
        if total_size_mb <= max_size_mb:
            print("📊 日志文件大小在限制范围内，无需清理")
            return True
        
        # 删除旧的日志文件
        current_size = 0
        kept_files = 0
        
        for log_file in log_files:
            file_size_mb = log_file['size'] / (1024 * 1024)
            
            if current_size + file_size_mb <= max_size_mb:
                current_size += file_size_mb
                kept_files += 1
            else:
                # 删除这个文件
                print(f"🗑️  删除日志文件: {log_file['path'].name} ({file_size_mb:.2f}MB)")
                if not self.dry_run:
                    try:
                        log_file['path'].unlink()
                    except Exception as e:
                        print(f"   ❌ 删除失败: {e}")
        
        print(f"✅ 保留 {kept_files} 个最新日志文件，总大小: {current_size:.2f}MB")
        return True
    
    def cleanup_python_cache(self) -> bool:
        """清理Python缓存"""
        print("\n🗑️  清理Python缓存")
        print("-" * 40)
        
        cache_dirs = []
        
        # 查找__pycache__目录
        for pycache_dir in self.project_root.rglob("__pycache__"):
            cache_dirs.append(pycache_dir)
        
        # 查找.pyc文件
        pyc_files = list(self.project_root.rglob("*.pyc"))
        
        print(f"📊 找到 {len(cache_dirs)} 个缓存目录和 {len(pyc_files)} 个.pyc文件")
        
        if not cache_dirs and not pyc_files:
            print("📊 没有找到Python缓存文件")
            return True
        
        # 删除缓存目录
        for cache_dir in cache_dirs:
            print(f"🗑️  删除缓存目录: {cache_dir.relative_to(self.project_root)}")
            if not self.dry_run:
                try:
                    shutil.rmtree(cache_dir)
                except Exception as e:
                    print(f"   ❌ 删除失败: {e}")
        
        # 删除.pyc文件
        for pyc_file in pyc_files:
            print(f"🗑️  删除.pyc文件: {pyc_file.relative_to(self.project_root)}")
            if not self.dry_run:
                try:
                    pyc_file.unlink()
                except Exception as e:
                    print(f"   ❌ 删除失败: {e}")
        
        return True
    
    def show_disk_usage(self):
        """显示磁盘使用情况"""
        print("\n📊 磁盘使用情况")
        print("-" * 40)
        
        try:
            # Docker系统信息
            result = subprocess.run(
                ["docker", "system", "df"],
                capture_output=True, text=True
            )
            if result.returncode == 0:
                print("Docker系统使用情况:")
                print(result.stdout)
        except Exception:
            print("⚠️  无法获取Docker系统信息")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="AI接诉即办助手 v3.0 - 清理工具"
    )
    parser.add_argument(
        "--containers", "-c",
        action="store_true",
        help="清理Docker容器"
    )
    parser.add_argument(
        "--images", "-i", 
        action="store_true",
        help="清理Docker镜像"
    )
    parser.add_argument(
        "--volumes", "-v",
        action="store_true", 
        help="清理Docker卷"
    )
    parser.add_argument(
        "--networks", "-n",
        action="store_true",
        help="清理Docker网络"
    )
    parser.add_argument(
        "--build-cache", "-b",
        action="store_true",
        help="清理构建缓存"
    )
    parser.add_argument(
        "--logs", "-l",
        action="store_true",
        help="清理日志文件"
    )
    parser.add_argument(
        "--python-cache", "-p",
        action="store_true",
        help="清理Python缓存"
    )
    parser.add_argument(
        "--all", "-a",
        action="store_true",
        help="执行所有清理操作"
    )
    parser.add_argument(
        "--all-images",
        action="store_true",
        help="清理所有未使用的镜像（危险操作）"
    )
    parser.add_argument(
        "--all-containers", 
        action="store_true",
        help="清理所有停止的容器（危险操作）"
    )
    parser.add_argument(
        "--dry-run", "-d",
        action="store_true",
        help="试运行模式，只显示操作不执行"
    )
    parser.add_argument(
        "--log-size",
        type=int,
        default=100,
        help="保留的日志文件大小限制(MB，默认100)"
    )
    
    args = parser.parse_args()
    
    # 创建清理工具
    cleaner = CleanupTool()
    cleaner.set_dry_run(args.dry_run)
    
    print("="*60)
    print("🧹 AI接诉即办助手 v3.0 - 清理工具")
    print("="*60)
    
    # 显示当前磁盘使用情况
    cleaner.show_disk_usage()
    
    # 执行清理操作
    if args.all:
        cleaner.cleanup_docker_containers(args.all_containers)
        cleaner.cleanup_docker_images(args.all_images)
        cleaner.cleanup_docker_volumes()
        cleaner.cleanup_docker_networks()
        cleaner.cleanup_build_cache()
        cleaner.cleanup_logs(args.log_size)
        cleaner.cleanup_python_cache()
    else:
        if args.containers:
            cleaner.cleanup_docker_containers(args.all_containers)
        if args.images:
            cleaner.cleanup_docker_images(args.all_images)
        if args.volumes:
            cleaner.cleanup_docker_volumes()
        if args.networks:
            cleaner.cleanup_docker_networks()
        if args.build_cache:
            cleaner.cleanup_build_cache()
        if args.logs:
            cleaner.cleanup_logs(args.log_size)
        if args.python_cache:
            cleaner.cleanup_python_cache()
        
        # 如果没有指定任何选项，显示帮助
        if not any([args.containers, args.images, args.volumes, 
                   args.networks, args.build_cache, args.logs, args.python_cache]):
            parser.print_help()
            return
    
    print("\n✅ 清理操作完成")
    
    # 显示清理后的磁盘使用情况
    cleaner.show_disk_usage()

if __name__ == "__main__":
    main()
