#!/usr/bin/env python3
"""
AI接诉即办助手 v3.0 - 健康检查工具
检查各个服务的运行状态和健康状况
"""

import requests
import time
import sys
import argparse
from typing import Dict, List, Optional
from dataclasses import dataclass

@dataclass
class ServiceConfig:
    """服务配置"""
    name: str
    url: str
    health_endpoint: str
    timeout: int = 10

class HealthChecker:
    """健康检查器"""
    
    def __init__(self):
        self.services = {
            'cpu': ServiceConfig(
                name="CPU服务",
                url="http://localhost:8000",
                health_endpoint="/v2/system/health"
            ),
            'gpu': ServiceConfig(
                name="GPU服务", 
                url="http://localhost:8001",
                health_endpoint="/health"
            ),
            'milvus': ServiceConfig(
                name="Milvus向量数据库",
                url="http://localhost:19530",
                health_endpoint="/health",
                timeout=5
            ),
            'redis': ServiceConfig(
                name="Redis缓存",
                url="http://localhost:6379",
                health_endpoint="/ping",
                timeout=5
            ),
            'minio': ServiceConfig(
                name="MinIO对象存储",
                url="http://localhost:9000",
                health_endpoint="/minio/health/live",
                timeout=5
            )
        }
    
    def check_service(self, service: ServiceConfig) -> Dict:
        """检查单个服务"""
        result = {
            'name': service.name,
            'url': service.url,
            'status': 'unknown',
            'response_time': 0,
            'error': None
        }
        
        try:
            start_time = time.time()
            
            # 发送健康检查请求
            response = requests.get(
                service.url + service.health_endpoint,
                timeout=service.timeout
            )
            
            end_time = time.time()
            result['response_time'] = round((end_time - start_time) * 1000, 2)
            
            if response.status_code == 200:
                result['status'] = 'healthy'
            else:
                result['status'] = 'unhealthy'
                result['error'] = f"HTTP {response.status_code}"
                
        except requests.exceptions.ConnectionError:
            result['status'] = 'down'
            result['error'] = "连接失败"
        except requests.exceptions.Timeout:
            result['status'] = 'timeout'
            result['error'] = f"超时 (>{service.timeout}s)"
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
        
        return result
    
    def check_all_services(self, service_names: Optional[List[str]] = None) -> List[Dict]:
        """检查所有服务或指定服务"""
        if service_names is None:
            services_to_check = self.services.values()
        else:
            services_to_check = [
                self.services[name] for name in service_names 
                if name in self.services
            ]
        
        results = []
        for service in services_to_check:
            print(f"🔍 检查 {service.name}...")
            result = self.check_service(service)
            results.append(result)
        
        return results
    
    def print_results(self, results: List[Dict], detailed: bool = False):
        """打印检查结果"""
        print("\n" + "="*70)
        print("📊 健康检查结果")
        print("="*70)
        
        # 状态图标映射
        status_icons = {
            'healthy': '✅',
            'unhealthy': '⚠️',
            'down': '❌',
            'timeout': '⏰',
            'error': '💥',
            'unknown': '❓'
        }
        
        # 打印表头
        if detailed:
            print(f"{'状态':<4} {'服务名':<20} {'地址':<25} {'响应时间':<10} {'错误信息':<20}")
            print("-" * 70)
        else:
            print(f"{'状态':<4} {'服务名':<20} {'状态描述':<20}")
            print("-" * 50)
        
        # 打印结果
        healthy_count = 0
        total_count = len(results)
        
        for result in results:
            status = result['status']
            icon = status_icons.get(status, '❓')
            name = result['name']
            
            if status == 'healthy':
                healthy_count += 1
                status_desc = "正常运行"
            elif status == 'unhealthy':
                status_desc = "运行异常"
            elif status == 'down':
                status_desc = "服务停止"
            elif status == 'timeout':
                status_desc = "响应超时"
            elif status == 'error':
                status_desc = "检查错误"
            else:
                status_desc = "状态未知"
            
            if detailed:
                url = result['url']
                response_time = f"{result['response_time']}ms" if result['response_time'] > 0 else "N/A"
                error = result['error'] or "无"
                print(f"{icon:<4} {name:<20} {url:<25} {response_time:<10} {error:<20}")
            else:
                print(f"{icon:<4} {name:<20} {status_desc:<20}")
        
        # 打印汇总
        print("-" * (70 if detailed else 50))
        print(f"📈 汇总: {healthy_count}/{total_count} 服务正常运行")
        
        if healthy_count == total_count:
            print("🎉 所有服务运行正常！")
        elif healthy_count == 0:
            print("🚨 所有服务都有问题，请检查部署状态")
        else:
            print("⚠️  部分服务有问题，请检查异常服务")
    
    def continuous_check(self, interval: int = 30, service_names: Optional[List[str]] = None):
        """持续健康检查"""
        print(f"🔄 开始持续健康检查 (间隔: {interval}秒)")
        print("按 Ctrl+C 停止检查")
        
        try:
            while True:
                print(f"\n⏰ {time.strftime('%Y-%m-%d %H:%M:%S')}")
                results = self.check_all_services(service_names)
                self.print_results(results, detailed=False)
                
                print(f"\n💤 等待 {interval} 秒...")
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n\n🛑 健康检查已停止")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="AI接诉即办助手 v3.0 - 健康检查工具"
    )
    parser.add_argument(
        "services",
        nargs="*",
        choices=["cpu", "gpu", "milvus", "redis", "minio"],
        help="要检查的服务 (默认检查所有服务)"
    )
    parser.add_argument(
        "--detailed", "-d",
        action="store_true",
        help="显示详细信息"
    )
    parser.add_argument(
        "--continuous", "-c",
        action="store_true",
        help="持续检查模式"
    )
    parser.add_argument(
        "--interval", "-i",
        type=int,
        default=30,
        help="持续检查间隔 (秒，默认30)"
    )
    
    args = parser.parse_args()
    
    # 创建健康检查器
    checker = HealthChecker()
    
    # 检查服务
    service_names = args.services if args.services else None
    
    if args.continuous:
        checker.continuous_check(args.interval, service_names)
    else:
        print("🔍 开始健康检查...")
        results = checker.check_all_services(service_names)
        checker.print_results(results, args.detailed)
        
        # 根据结果设置退出码
        healthy_count = sum(1 for r in results if r['status'] == 'healthy')
        if healthy_count == len(results):
            sys.exit(0)  # 所有服务正常
        else:
            sys.exit(1)  # 有服务异常

if __name__ == "__main__":
    main()
