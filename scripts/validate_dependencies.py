#!/usr/bin/env python3
"""
依赖验证脚本 - 验证CPU和GPU服务的依赖兼容性
更新时间：2025-06-12
"""

import subprocess
import sys
import os
from pathlib import Path
import tempfile
import json

def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            cwd=cwd,
            timeout=300
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "命令执行超时"
    except Exception as e:
        return False, "", str(e)

def check_requirements_syntax(requirements_file):
    """检查requirements.txt语法"""
    print(f"\n🔍 检查 {requirements_file} 语法...")
    
    if not os.path.exists(requirements_file):
        print(f"❌ 文件不存在: {requirements_file}")
        return False
    
    try:
        with open(requirements_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if line and not line.startswith('#'):
                # 基本语法检查
                if '==' in line or '>=' in line or '<=' in line or '>' in line or '<' in line:
                    continue
                elif line.replace('-', '').replace('_', '').replace('[', '').replace(']', '').isalnum():
                    continue
                else:
                    print(f"❌ 第{i}行语法可能有问题: {line}")
                    return False
        
        print(f"✅ {requirements_file} 语法检查通过")
        return True
    except Exception as e:
        print(f"❌ 检查 {requirements_file} 时出错: {e}")
        return False

def check_dependency_conflicts(requirements_file):
    """检查依赖冲突"""
    print(f"\n🔍 检查 {requirements_file} 依赖冲突...")
    
    # 创建临时虚拟环境进行测试
    with tempfile.TemporaryDirectory() as temp_dir:
        venv_path = os.path.join(temp_dir, 'test_env')
        
        # 创建虚拟环境
        success, stdout, stderr = run_command(f"python -m venv {venv_path}")
        if not success:
            print(f"❌ 创建虚拟环境失败: {stderr}")
            return False
        
        # 激活虚拟环境的pip路径
        if sys.platform == "win32":
            pip_path = os.path.join(venv_path, "Scripts", "pip.exe")
        else:
            pip_path = os.path.join(venv_path, "bin", "pip")
        
        # 尝试安装依赖（dry-run模式）
        success, stdout, stderr = run_command(
            f'"{pip_path}" install --dry-run -r "{requirements_file}"'
        )
        
        if success:
            print(f"✅ {requirements_file} 依赖冲突检查通过")
            return True
        else:
            print(f"❌ {requirements_file} 存在依赖冲突:")
            print(stderr)
            return False

def compare_common_dependencies():
    """比较CPU和GPU服务的共同依赖版本"""
    print(f"\n🔍 比较CPU和GPU服务共同依赖版本...")
    
    cpu_deps = {}
    gpu_deps = {}
    
    # 解析CPU服务依赖
    try:
        with open('cpu-service/requirements.txt', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    if '==' in line:
                        name, version = line.split('==', 1)
                        # 移除注释部分
                        if '#' in version:
                            version = version.split('#')[0]
                        cpu_deps[name.strip()] = version.strip()
    except Exception as e:
        print(f"❌ 解析CPU服务依赖失败: {e}")
        return False
    
    # 解析GPU服务依赖
    try:
        with open('gpu-service/requirements.txt', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    if '==' in line:
                        name, version = line.split('==', 1)
                        # 移除注释部分
                        if '#' in version:
                            version = version.split('#')[0]
                        gpu_deps[name.strip()] = version.strip()
    except Exception as e:
        print(f"❌ 解析GPU服务依赖失败: {e}")
        return False
    
    # 比较共同依赖
    common_deps = set(cpu_deps.keys()) & set(gpu_deps.keys())
    conflicts = []
    
    for dep in common_deps:
        if cpu_deps[dep] != gpu_deps[dep]:
            conflicts.append((dep, cpu_deps[dep], gpu_deps[dep]))
    
    if conflicts:
        print(f"❌ 发现版本冲突:")
        for dep, cpu_ver, gpu_ver in conflicts:
            print(f"  - {dep}: CPU={cpu_ver}, GPU={gpu_ver}")
        return False
    else:
        print(f"✅ 共同依赖版本一致 ({len(common_deps)} 个)")
        return True

def main():
    """主函数"""
    print("🚀 依赖验证脚本")
    print("=" * 50)
    
    # 检查工作目录
    if not os.path.exists('cpu-service/requirements.txt'):
        print("❌ 请在项目根目录运行此脚本")
        sys.exit(1)
    
    all_passed = True
    
    # 1. 语法检查
    if not check_requirements_syntax('cpu-service/requirements.txt'):
        all_passed = False
    
    if not check_requirements_syntax('gpu-service/requirements.txt'):
        all_passed = False
    
    # 2. 版本一致性检查
    if not compare_common_dependencies():
        all_passed = False
    
    # 3. 依赖冲突检查（可选，因为需要时间）
    print(f"\n💡 提示: 可以手动运行以下命令进行完整依赖冲突检查:")
    print(f"   pip-compile --dry-run cpu-service/requirements.txt")
    print(f"   pip-compile --dry-run gpu-service/requirements.txt")
    
    # 总结
    print("\n" + "=" * 50)
    if all_passed:
        print("✅ 所有检查通过！依赖文件更新成功")
        print("\n📋 下一步操作:")
        print("1. 备份当前环境: pip freeze > backup_requirements.txt")
        print("2. 更新CPU服务: cd cpu-service && pip install -r requirements.txt")
        print("3. 更新GPU服务: 重新构建Docker镜像")
        print("4. 运行测试验证功能正常")
    else:
        print("❌ 检查失败！请修复问题后重新运行")
        sys.exit(1)

if __name__ == "__main__":
    main()
