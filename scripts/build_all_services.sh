#!/bin/bash

# AI接诉即办助手 v3.0 - 批量构建脚本 (Linux)
# 更新时间：2025-06-12

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo ""
echo "=========================================="
echo "  AI接诉即办助手 v3.0 - Docker镜像构建"
echo "=========================================="
echo ""

# 检查Docker是否可用
if ! command -v docker &> /dev/null; then
    log_error "Docker未安装或不可用"
    echo "请先安装Docker"
    exit 1
fi

log_info "Docker版本: $(docker --version)"

# 检查是否在项目根目录
if [[ ! -f "gpu-service/dockerfile" ]]; then
    log_error "请在项目根目录运行此脚本"
    echo "当前目录: $(pwd)"
    exit 1
fi

if [[ ! -f "cpu-service/dockerfile" ]]; then
    log_error "找不到CPU服务Dockerfile"
    echo "请确保在正确的项目目录中"
    exit 1
fi

log_success "环境检查通过"
echo ""

# 询问是否清理缓存
read -p "是否清理Docker构建缓存? (y/N): " clean_cache
if [[ "$clean_cache" =~ ^[Yy]$ ]]; then
    log_info "清理Docker构建缓存..."
    docker builder prune -f
    echo ""
fi

# 构建GPU服务
echo "[1/2] 🔨 构建GPU服务镜像..."
log_info "命令: docker build -f gpu-service/dockerfile -t ai-v3-gpu-service:prod ."
echo ""

if docker build -f gpu-service/dockerfile -t ai-v3-gpu-service:prod .; then
    log_success "GPU服务构建成功"
else
    log_error "GPU服务构建失败！"
    echo "请检查错误信息并重试"
    exit 1
fi

echo ""

# 构建CPU服务
echo "[2/2] 🔨 构建CPU服务镜像..."
log_info "命令: docker build -f cpu-service/dockerfile -t ai-v3-cpu-service:prod ."
echo ""

if docker build -f cpu-service/dockerfile -t ai-v3-cpu-service:prod .; then
    log_success "CPU服务构建成功"
else
    log_error "CPU服务构建失败！"
    echo "请检查错误信息并重试"
    exit 1
fi

echo ""

# 显示构建结果
echo "=========================================="
echo "  🎉 所有服务构建完成！"
echo "=========================================="
echo ""
echo "📦 构建的镜像:"
docker images | grep ai-v3

echo ""
echo "📋 下一步操作:"
echo "  1. 部署服务: cd deployment && docker-compose up -d"
echo "  2. 健康检查: curl http://localhost:8001/health"
echo "  3. 查看文档: docs/DOCKER_BUILD_DEPLOY_GUIDE.md"
echo ""

# 询问是否立即部署
read -p "是否立即部署服务? (y/N): " deploy_now
if [[ "$deploy_now" =~ ^[Yy]$ ]]; then
    if [[ -d "deployment" ]]; then
        log_info "切换到deployment目录..."
        cd deployment
        
        log_info "启动服务..."
        docker-compose up -d
        
        log_success "服务部署完成！"
        echo ""
        echo "🔗 服务地址:"
        echo "  - GPU服务: http://localhost:8001"
        echo "  - CPU服务: http://localhost:8000"
        echo "  - API文档: http://localhost:8000/docs"
    else
        log_warning "deployment目录不存在，请手动部署"
    fi
fi
