# AI接诉即办助手 v3.0 - Nginx Scale方案配置
# 支持动态服务发现的负载均衡配置

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

worker_rlimit_nofile 65535;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time" '
                    'upstream="$upstream_addr"';

    access_log /var/log/nginx/access.log main;

    # 基础优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 动态解析配置
    resolver 127.0.0.11 valid=30s;
    resolver_timeout 5s;

    # CPU服务负载均衡池（动态发现）
    upstream cpu_service_backend {
        # 使用Docker内部DNS进行服务发现
        # Docker Compose会自动为scaled服务创建DNS记录
        server cpu-service:8000 max_fails=3 fail_timeout=30s;
        
        # 保持连接
        keepalive 32;
        
        # 负载均衡策略
        least_conn;
    }

    # GPU服务（固定单实例）
    upstream gpu_service_backend {
        server gpu-service:8001 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }

    # 限流配置
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=health_limit:10m rate=1r/s;

    # 主服务器配置
    server {
        listen 80;
        server_name localhost;
        
        # 安全头
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Referrer-Policy "strict-origin-when-cross-origin";

        # 健康检查端点
        location /health {
            limit_req zone=health_limit burst=5 nodelay;
            
            # 动态代理到CPU服务
            set $backend "cpu_service_backend";
            proxy_pass http://$backend/health;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 10s;
        }

        # 负载均衡状态页面
        location /nginx-status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            allow **********/16;  # Docker网络
            deny all;
        }

        # CPU服务API（主要业务逻辑）
        location / {
            limit_req zone=api_limit burst=20 nodelay;
            
            # 动态代理到CPU服务集群
            set $backend "cpu_service_backend";
            proxy_pass http://$backend;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 超时设置
            proxy_connect_timeout 10s;
            proxy_send_timeout 60s;
            proxy_read_timeout 300s;
            
            # 缓冲设置
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            
            # WebSocket支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            # 故障转移
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 10s;
        }

        # GPU服务API（稀疏嵌入）
        location /gpu/ {
            limit_req zone=api_limit burst=10 nodelay;
            rewrite ^/gpu/(.*)$ /$1 break;
            
            set $backend "gpu_service_backend";
            proxy_pass http://$backend;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 10s;
            proxy_send_timeout 30s;
            proxy_read_timeout 60s;
        }

        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # 拒绝访问隐藏文件
        location ~ /\. {
            deny all;
        }
    }

    # 包含动态生成的配置
    include /etc/nginx/conf.d/*.conf;
}
