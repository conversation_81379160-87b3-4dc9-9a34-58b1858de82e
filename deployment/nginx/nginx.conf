# AI接诉即办助手 v3.0 - Nginx负载均衡配置
# 专业生产环境配置，支持CPU服务副本负载均衡

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# 优化配置
worker_rlimit_nofile 65535;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    access_log /var/log/nginx/access.log main;

    # 基础优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # CPU服务负载均衡池
    upstream cpu_service_backend {
        # 负载均衡策略：least_conn（最少连接）
        least_conn;
        
        # CPU服务副本
        server cpu-service-1:8000 max_fails=3 fail_timeout=30s weight=1;
        server cpu-service-2:8000 max_fails=3 fail_timeout=30s weight=1;
        # server cpu-service-3:8000 max_fails=3 fail_timeout=30s weight=1;  # 可选第三个副本
        
        # 健康检查（需要nginx-plus或第三方模块）
        # health_check interval=10s fails=3 passes=2;
        
        # 保持连接
        keepalive 32;
    }

    # GPU服务（直接代理，不负载均衡）
    upstream gpu_service_backend {
        server gpu-service:8001 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }

    # 限流配置
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=health_limit:10m rate=1r/s;

    # 主服务器配置
    server {
        listen 80;
        server_name localhost ai.example.com;  # 替换为实际域名
        
        # 安全头
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Referrer-Policy "strict-origin-when-cross-origin";

        # 健康检查端点（不限流）
        location /health {
            limit_req zone=health_limit burst=5 nodelay;
            proxy_pass http://cpu_service_backend/v2/system/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 健康检查超时设置
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 10s;
        }

        # CPU服务API（主要业务逻辑）
        location / {
            limit_req zone=api_limit burst=20 nodelay;
            proxy_pass http://cpu_service_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 超时设置
            proxy_connect_timeout 10s;
            proxy_send_timeout 60s;
            proxy_read_timeout 300s;  # 长时间处理请求
            
            # 缓冲设置
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            
            # WebSocket支持（如果需要）
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # GPU服务API（稀疏嵌入）
        location /gpu/ {
            limit_req zone=api_limit burst=10 nodelay;
            rewrite ^/gpu/(.*)$ /$1 break;
            proxy_pass http://gpu_service_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # GPU服务超时设置
            proxy_connect_timeout 10s;
            proxy_send_timeout 30s;
            proxy_read_timeout 60s;
        }

        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # 拒绝访问隐藏文件
        location ~ /\. {
            deny all;
        }
    }

    # HTTPS配置（生产环境推荐）
    # server {
    #     listen 443 ssl http2;
    #     server_name ai.example.com;
    #     
    #     ssl_certificate /etc/nginx/ssl/cert.pem;
    #     ssl_certificate_key /etc/nginx/ssl/key.pem;
    #     ssl_session_timeout 1d;
    #     ssl_session_cache shared:SSL:50m;
    #     ssl_session_tickets off;
    #     
    #     ssl_protocols TLSv1.2 TLSv1.3;
    #     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    #     ssl_prefer_server_ciphers off;
    #     
    #     # HSTS
    #     add_header Strict-Transport-Security "max-age=63072000" always;
    #     
    #     # 其他配置与HTTP相同...
    # }

    # 包含其他配置文件
    include /etc/nginx/conf.d/*.conf;
}
