# AI接诉即办助手 v3.0 - Scale方案部署配置
# 使用Docker Compose原生scale功能的替代方案

version: '3.8'

services:
  # GPU服务（单实例）
  gpu-service:
    image: ai-v3-gpu-service:prod
    container_name: ai-v3-gpu-service-prod
    restart: unless-stopped
    ports:
      - "8001:8001"
    volumes:
      - ai-models-cache:/app/models
      - ./logs/gpu:/app/logs
    environment:
      - ENVIRONMENT=production
      - GPU_SERVICE_HOST=0.0.0.0
      - GPU_SERVICE_PORT=8001
      - BGE_M3_MODEL_PATH=/app/models/hub/bge-m3
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - ai-network

  # CPU服务（可扩展）
  cpu-service:
    image: ai-v3-cpu-service:prod
    restart: unless-stopped
    # 注意：不能使用ports映射，会导致端口冲突
    expose:
      - "8000"
    environment:
      - ENVIRONMENT=production
      - CPU_SERVICE_HOST=0.0.0.0
      - CPU_SERVICE_PORT=8000
      - CPU_SERVICE_WORKERS=2
      - GPU_SERVICE_URL=http://gpu-service:8001
      # 动态日志目录配置
      - LOG_DIR=/app/logs
      - INSTANCE_ID=${HOSTNAME}
    depends_on:
      gpu-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - ai-network
    # Scale配置
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
        order: start-first
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  # Nginx负载均衡器（支持动态发现）
  nginx:
    image: nginx:alpine
    container_name: ai-v3-nginx-scale
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx-scale.conf:/etc/nginx/nginx.conf:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - cpu-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - ai-network

  # 服务发现助手（解决动态upstream问题）
  nginx-gen:
    image: nginxproxy/nginx-proxy:alpine
    container_name: ai-v3-nginx-gen
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/tmp/docker.sock:ro
      - ./nginx/templates:/etc/docker-gen/templates:ro
      - ./nginx/conf.d:/etc/nginx/conf.d
    environment:
      - DEFAULT_HOST=localhost
    networks:
      - ai-network
    profiles:
      - auto-discovery

  # 日志聚合服务（解决日志分离问题）
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: ai-v3-fluentd
    restart: unless-stopped
    ports:
      - "24224:24224"
    volumes:
      - ./fluentd/fluent.conf:/fluentd/etc/fluent.conf:ro
      - ./logs/aggregated:/fluentd/log
    networks:
      - ai-network
    profiles:
      - logging

# 网络配置
networks:
  ai-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  ai-models-cache:
    external: true

# 使用说明：
# 1. 启动基础服务：docker-compose -f docker-compose.scale.yml up -d
# 2. 扩展CPU服务：docker-compose -f docker-compose.scale.yml up -d --scale cpu-service=3
# 3. 启用服务发现：docker-compose -f docker-compose.scale.yml --profile auto-discovery up -d
# 4. 启用日志聚合：docker-compose -f docker-compose.scale.yml --profile logging up -d
