2025-06-08 06:52:10 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-08 06:52:10 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-08.log
2025-06-08 06:52:10 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-08.log
2025-06-08 06:52:10 [INFO] uvicorn.error [server.py:76] - Started server process [8]
2025-06-08 06:52:10 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-08 06:52:10 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-08 06:52:10 [INFO] main [main.py:83] - 环境: production
2025-06-08 06:52:10 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-08 06:52:10 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-08 06:52:10 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-08 06:52:10 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'deprecated'
2025-06-08 06:52:10 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-08 06:52:10 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-08 06:52:10 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-08 06:52:10 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-08.log
2025-06-08 06:52:10 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-08.log
2025-06-08 06:52:10 [INFO] uvicorn.error [server.py:76] - Started server process [9]
2025-06-08 06:52:10 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-08 06:52:10 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-08 06:52:10 [INFO] main [main.py:83] - 环境: production
2025-06-08 06:52:10 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-08 06:52:10 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-08 06:52:10 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-08 06:52:10 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'deprecated'
2025-06-08 06:52:10 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-08 06:52:10 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-08 06:52:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42580 - "GET /health HTTP/1.1" 200
2025-06-08 06:52:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54646 - "GET /health HTTP/1.1" 200
2025-06-08 06:52:46 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:38490 - "GET / HTTP/1.1" 200
2025-06-08 06:52:50 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:38490 - "GET /openapi.json HTTP/1.1" 200
2025-06-08 06:53:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60580 - "GET /health HTTP/1.1" 200
2025-06-08 06:53:18 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:51804 - "GET /v2/ HTTP/1.1" 200
2025-06-08 06:53:38 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:50304 - "GET /health HTTP/1.0" 200
2025-06-08 06:53:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34660 - "GET /health HTTP/1.1" 200
2025-06-08 06:54:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57694 - "GET /health HTTP/1.1" 200
2025-06-08 06:54:38 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:47004 - "GET /health HTTP/1.0" 200
2025-06-08 06:54:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50810 - "GET /health HTTP/1.1" 200
2025-06-08 06:55:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42354 - "GET /health HTTP/1.1" 200
2025-06-08 06:55:38 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:56544 - "GET /health HTTP/1.0" 200
2025-06-08 06:55:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53418 - "GET /health HTTP/1.1" 200
2025-06-08 06:56:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43736 - "GET /health HTTP/1.1" 200
2025-06-08 06:56:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:47860 - "GET /health HTTP/1.0" 200
2025-06-08 06:56:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49580 - "GET /health HTTP/1.1" 200
2025-06-08 06:57:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48076 - "GET /health HTTP/1.1" 200
2025-06-08 06:57:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:36660 - "GET /health HTTP/1.0" 200
2025-06-08 06:57:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41052 - "GET /health HTTP/1.1" 200
2025-06-08 06:58:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38454 - "GET /health HTTP/1.1" 200
2025-06-08 06:58:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:40500 - "GET /health HTTP/1.0" 200
2025-06-08 06:58:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33528 - "GET /health HTTP/1.1" 200
2025-06-08 06:59:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33772 - "GET /health HTTP/1.1" 200
2025-06-08 06:59:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:34878 - "GET /health HTTP/1.0" 200
2025-06-08 06:59:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45402 - "GET /health HTTP/1.1" 200
2025-06-08 07:00:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41260 - "GET /health HTTP/1.1" 200
2025-06-08 07:00:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:55764 - "GET /health HTTP/1.0" 200
2025-06-08 07:00:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45578 - "GET /health HTTP/1.1" 200
2025-06-08 07:01:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41438 - "GET /health HTTP/1.1" 200
2025-06-08 07:01:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:54726 - "GET /health HTTP/1.0" 200
2025-06-08 07:01:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41218 - "GET /health HTTP/1.1" 200
2025-06-08 07:02:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43124 - "GET /health HTTP/1.1" 200
2025-06-08 07:02:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:47490 - "GET /health HTTP/1.0" 200
2025-06-08 07:02:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55372 - "GET /health HTTP/1.1" 200
2025-06-08 07:03:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39610 - "GET /health HTTP/1.1" 200
2025-06-08 07:03:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:43532 - "GET /health HTTP/1.0" 200
2025-06-08 07:03:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48392 - "GET /health HTTP/1.1" 200
2025-06-08 07:04:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47222 - "GET /health HTTP/1.1" 200
2025-06-08 07:04:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:34542 - "GET /health HTTP/1.0" 200
2025-06-08 07:04:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45878 - "GET /health HTTP/1.1" 200
2025-06-08 07:05:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46314 - "GET /health HTTP/1.1" 200
2025-06-08 07:05:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:42942 - "GET /health HTTP/1.0" 200
2025-06-08 07:05:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37308 - "GET /health HTTP/1.1" 200
2025-06-08 07:06:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34962 - "GET /health HTTP/1.1" 200
2025-06-08 07:06:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:57162 - "GET /health HTTP/1.0" 200
2025-06-08 07:06:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56162 - "GET /health HTTP/1.1" 200
2025-06-08 07:07:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51128 - "GET /health HTTP/1.1" 200
2025-06-08 07:07:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:45010 - "GET /health HTTP/1.0" 200
2025-06-08 07:07:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42322 - "GET /health HTTP/1.1" 200
2025-06-08 07:08:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48528 - "GET /health HTTP/1.1" 200
2025-06-08 07:08:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:51186 - "GET /health HTTP/1.0" 200
2025-06-08 07:08:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33584 - "GET /health HTTP/1.1" 200
2025-06-08 07:09:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46596 - "GET /health HTTP/1.1" 200
2025-06-08 07:09:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:47428 - "GET /health HTTP/1.0" 200
2025-06-08 07:09:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59936 - "GET /health HTTP/1.1" 200
2025-06-08 07:10:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42376 - "GET /health HTTP/1.1" 200
2025-06-08 07:10:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:59302 - "GET /health HTTP/1.0" 200
2025-06-08 07:10:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41808 - "GET /health HTTP/1.1" 200
2025-06-08 07:11:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59444 - "GET /health HTTP/1.1" 200
2025-06-08 07:11:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:41698 - "GET /health HTTP/1.0" 200
2025-06-08 07:11:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59790 - "GET /health HTTP/1.1" 200
2025-06-08 07:12:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39582 - "GET /health HTTP/1.1" 200
2025-06-08 07:12:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:60226 - "GET /health HTTP/1.0" 200
2025-06-08 07:12:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43050 - "GET /health HTTP/1.1" 200
2025-06-08 07:13:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56666 - "GET /health HTTP/1.1" 200
2025-06-08 07:13:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:35112 - "GET /health HTTP/1.0" 200
2025-06-08 07:13:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57168 - "GET /health HTTP/1.1" 200
2025-06-08 07:14:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46960 - "GET /health HTTP/1.1" 200
2025-06-08 07:14:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:53882 - "GET /health HTTP/1.0" 200
2025-06-08 07:14:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52458 - "GET /health HTTP/1.1" 200
2025-06-08 07:15:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56020 - "GET /health HTTP/1.1" 200
2025-06-08 07:15:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:37134 - "GET /health HTTP/1.0" 200
2025-06-08 07:15:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35206 - "GET /health HTTP/1.1" 200
2025-06-08 07:16:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47634 - "GET /health HTTP/1.1" 200
2025-06-08 07:16:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:42710 - "GET /health HTTP/1.0" 200
2025-06-08 07:16:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38300 - "GET /health HTTP/1.1" 200
2025-06-08 07:17:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56162 - "GET /health HTTP/1.1" 200
2025-06-08 07:17:39 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:60192 - "GET /health HTTP/1.0" 200
2025-06-08 07:17:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54804 - "GET /health HTTP/1.1" 200
2025-06-08 07:18:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53924 - "GET /health HTTP/1.1" 200
2025-06-08 07:18:40 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:52304 - "GET /health HTTP/1.0" 200
2025-06-08 07:18:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39278 - "GET /health HTTP/1.1" 200
2025-06-08 07:19:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45414 - "GET /health HTTP/1.1" 200
2025-06-08 07:19:40 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:50174 - "GET /health HTTP/1.0" 200
2025-06-08 07:19:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38360 - "GET /health HTTP/1.1" 200
2025-06-08 07:20:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37584 - "GET /health HTTP/1.1" 200
2025-06-08 07:20:40 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:57666 - "GET /health HTTP/1.0" 200
2025-06-08 07:20:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45282 - "GET /health HTTP/1.1" 200
2025-06-08 07:21:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41880 - "GET /health HTTP/1.1" 200
2025-06-08 07:21:40 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:33956 - "GET /health HTTP/1.0" 200
2025-06-08 07:21:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56186 - "GET /health HTTP/1.1" 200
2025-06-08 07:22:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50056 - "GET /health HTTP/1.1" 200
2025-06-08 07:22:40 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:43850 - "GET /health HTTP/1.0" 200
2025-06-08 07:22:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47720 - "GET /health HTTP/1.1" 200
2025-06-08 07:23:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37352 - "GET /health HTTP/1.1" 200
2025-06-08 07:23:40 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:42636 - "GET /health HTTP/1.0" 200
2025-06-08 07:23:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46674 - "GET /health HTTP/1.1" 200
2025-06-08 07:24:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54726 - "GET /health HTTP/1.1" 200
2025-06-08 07:24:40 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:50546 - "GET /health HTTP/1.0" 200
2025-06-08 07:24:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52014 - "GET /health HTTP/1.1" 200
2025-06-08 07:25:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41270 - "GET /health HTTP/1.1" 200
2025-06-08 07:25:40 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:59240 - "GET /health HTTP/1.0" 200
2025-06-08 07:25:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44864 - "GET /health HTTP/1.1" 200
2025-06-08 07:26:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46798 - "GET /health HTTP/1.1" 200
2025-06-08 07:26:40 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:57196 - "GET /health HTTP/1.0" 200
2025-06-08 07:26:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49166 - "GET /health HTTP/1.1" 200
2025-06-08 07:27:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46510 - "GET /health HTTP/1.1" 200
2025-06-08 07:27:40 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:59796 - "GET /health HTTP/1.0" 200
2025-06-08 07:27:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42686 - "GET /health HTTP/1.1" 200
2025-06-08 07:28:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56362 - "GET /health HTTP/1.1" 200
2025-06-08 07:28:40 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:36904 - "GET /health HTTP/1.0" 200
2025-06-08 07:28:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52958 - "GET /health HTTP/1.1" 200
2025-06-08 07:29:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60022 - "GET /health HTTP/1.1" 200
2025-06-08 07:29:40 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:45492 - "GET /health HTTP/1.0" 200
2025-06-08 07:29:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50398 - "GET /health HTTP/1.1" 200
2025-06-08 07:30:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39322 - "GET /health HTTP/1.1" 200
2025-06-08 07:30:40 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:60282 - "GET /health HTTP/1.0" 200
2025-06-08 07:30:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56418 - "GET /health HTTP/1.1" 200
2025-06-08 07:31:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49844 - "GET /health HTTP/1.1" 200
2025-06-08 07:31:40 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:33636 - "GET /health HTTP/1.0" 200
2025-06-08 07:31:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44506 - "GET /health HTTP/1.1" 200
2025-06-08 07:32:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39946 - "GET /health HTTP/1.1" 200
2025-06-08 07:32:40 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:49740 - "GET /health HTTP/1.0" 200
2025-06-08 07:32:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48668 - "GET /health HTTP/1.1" 200
2025-06-08 07:33:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57710 - "GET /health HTTP/1.1" 200
2025-06-08 07:33:40 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:54504 - "GET /health HTTP/1.0" 200
2025-06-08 07:33:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35466 - "GET /health HTTP/1.1" 200
2025-06-08 07:34:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47380 - "GET /health HTTP/1.1" 200
2025-06-08 07:34:40 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:37414 - "GET /health HTTP/1.0" 200
2025-06-08 07:34:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43000 - "GET /health HTTP/1.1" 200
2025-06-08 07:35:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45640 - "GET /health HTTP/1.1" 200
2025-06-08 07:35:40 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:51014 - "GET /health HTTP/1.0" 200
2025-06-08 07:35:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34184 - "GET /health HTTP/1.1" 200
2025-06-08 07:36:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53620 - "GET /health HTTP/1.1" 200
2025-06-08 07:36:40 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:34272 - "GET /health HTTP/1.0" 200
2025-06-08 07:36:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46474 - "GET /health HTTP/1.1" 200
2025-06-08 07:37:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55930 - "GET /health HTTP/1.1" 200
2025-06-08 07:37:40 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:36972 - "GET /health HTTP/1.0" 200
2025-06-08 07:37:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33930 - "GET /health HTTP/1.1" 200
2025-06-08 07:38:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38978 - "GET /health HTTP/1.1" 200
2025-06-08 07:38:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:54102 - "GET /health HTTP/1.0" 200
2025-06-08 07:38:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59712 - "GET /health HTTP/1.1" 200
2025-06-08 07:39:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38352 - "GET /health HTTP/1.1" 200
2025-06-08 07:39:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:46906 - "GET /health HTTP/1.0" 200
2025-06-08 07:39:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44152 - "GET /health HTTP/1.1" 200
2025-06-08 07:40:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48094 - "GET /health HTTP/1.1" 200
2025-06-08 07:40:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:46874 - "GET /health HTTP/1.0" 200
2025-06-08 07:40:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55168 - "GET /health HTTP/1.1" 200
2025-06-08 07:41:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53596 - "GET /health HTTP/1.1" 200
2025-06-08 07:41:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:55240 - "GET /health HTTP/1.0" 200
2025-06-08 07:41:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40802 - "GET /health HTTP/1.1" 200
2025-06-08 07:42:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42612 - "GET /health HTTP/1.1" 200
2025-06-08 07:42:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:38518 - "GET /health HTTP/1.0" 200
2025-06-08 07:42:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38796 - "GET /health HTTP/1.1" 200
2025-06-08 07:43:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57564 - "GET /health HTTP/1.1" 200
2025-06-08 07:43:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:52036 - "GET /health HTTP/1.0" 200
2025-06-08 07:43:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55402 - "GET /health HTTP/1.1" 200
2025-06-08 07:44:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33278 - "GET /health HTTP/1.1" 200
2025-06-08 07:44:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:53002 - "GET /health HTTP/1.0" 200
2025-06-08 07:44:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33736 - "GET /health HTTP/1.1" 200
2025-06-08 07:45:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54784 - "GET /health HTTP/1.1" 200
2025-06-08 07:45:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:39840 - "GET /health HTTP/1.0" 200
2025-06-08 07:45:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57790 - "GET /health HTTP/1.1" 200
2025-06-08 07:46:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51962 - "GET /health HTTP/1.1" 200
2025-06-08 07:46:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:49648 - "GET /health HTTP/1.0" 200
2025-06-08 07:46:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52274 - "GET /health HTTP/1.1" 200
2025-06-08 07:47:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53746 - "GET /health HTTP/1.1" 200
2025-06-08 07:47:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:52610 - "GET /health HTTP/1.0" 200
2025-06-08 07:47:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60168 - "GET /health HTTP/1.1" 200
2025-06-08 07:48:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39346 - "GET /health HTTP/1.1" 200
2025-06-08 07:48:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:59328 - "GET /health HTTP/1.0" 200
2025-06-08 07:48:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60732 - "GET /health HTTP/1.1" 200
2025-06-08 07:49:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38114 - "GET /health HTTP/1.1" 200
2025-06-08 07:49:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:58412 - "GET /health HTTP/1.0" 200
2025-06-08 07:49:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43236 - "GET /health HTTP/1.1" 200
2025-06-08 07:50:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38090 - "GET /health HTTP/1.1" 200
2025-06-08 07:50:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:32892 - "GET /health HTTP/1.0" 200
2025-06-08 07:50:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39332 - "GET /health HTTP/1.1" 200
2025-06-08 07:51:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54762 - "GET /health HTTP/1.1" 200
2025-06-08 07:51:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:56362 - "GET /health HTTP/1.0" 200
2025-06-08 07:51:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39296 - "GET /health HTTP/1.1" 200
2025-06-08 07:52:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56204 - "GET /health HTTP/1.1" 200
2025-06-08 07:52:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:59168 - "GET /health HTTP/1.0" 200
2025-06-08 07:52:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49414 - "GET /health HTTP/1.1" 200
2025-06-08 07:53:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55814 - "GET /health HTTP/1.1" 200
2025-06-08 07:53:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:54124 - "GET /health HTTP/1.0" 200
2025-06-08 07:53:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57430 - "GET /health HTTP/1.1" 200
2025-06-08 07:54:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33260 - "GET /health HTTP/1.1" 200
2025-06-08 07:54:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:35344 - "GET /health HTTP/1.0" 200
2025-06-08 07:54:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48562 - "GET /health HTTP/1.1" 200
2025-06-08 07:55:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48702 - "GET /health HTTP/1.1" 200
2025-06-08 07:55:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:39522 - "GET /health HTTP/1.0" 200
2025-06-08 07:55:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40420 - "GET /health HTTP/1.1" 200
2025-06-08 07:56:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59154 - "GET /health HTTP/1.1" 200
2025-06-08 07:56:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:37752 - "GET /health HTTP/1.0" 200
2025-06-08 07:56:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45784 - "GET /health HTTP/1.1" 200
2025-06-08 07:57:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34582 - "GET /health HTTP/1.1" 200
2025-06-08 07:57:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:50454 - "GET /health HTTP/1.0" 200
2025-06-08 07:57:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56770 - "GET /health HTTP/1.1" 200
2025-06-08 07:58:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42980 - "GET /health HTTP/1.1" 200
2025-06-08 07:58:41 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:60640 - "GET /health HTTP/1.0" 200
2025-06-08 07:58:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39052 - "GET /health HTTP/1.1" 200
2025-06-08 07:59:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41030 - "GET /health HTTP/1.1" 200
2025-06-08 07:59:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:36880 - "GET /health HTTP/1.0" 200
2025-06-08 07:59:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52224 - "GET /health HTTP/1.1" 200
2025-06-08 08:00:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39158 - "GET /health HTTP/1.1" 200
2025-06-08 08:00:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:51132 - "GET /health HTTP/1.0" 200
2025-06-08 08:00:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39870 - "GET /health HTTP/1.1" 200
2025-06-08 08:01:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60684 - "GET /health HTTP/1.1" 200
2025-06-08 08:01:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:36746 - "GET /health HTTP/1.0" 200
2025-06-08 08:01:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33916 - "GET /health HTTP/1.1" 200
2025-06-08 08:02:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40906 - "GET /health HTTP/1.1" 200
2025-06-08 08:02:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:35866 - "GET /health HTTP/1.0" 200
2025-06-08 08:02:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60120 - "GET /health HTTP/1.1" 200
2025-06-08 08:03:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51844 - "GET /health HTTP/1.1" 200
2025-06-08 08:03:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:44756 - "GET /health HTTP/1.0" 200
2025-06-08 08:03:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42214 - "GET /health HTTP/1.1" 200
2025-06-08 08:04:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56618 - "GET /health HTTP/1.1" 200
2025-06-08 08:04:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:56006 - "GET /health HTTP/1.0" 200
2025-06-08 08:04:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49198 - "GET /health HTTP/1.1" 200
2025-06-08 08:05:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35192 - "GET /health HTTP/1.1" 200
2025-06-08 08:05:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:58164 - "GET /health HTTP/1.0" 200
2025-06-08 08:05:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47498 - "GET /health HTTP/1.1" 200
2025-06-08 08:06:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44776 - "GET /health HTTP/1.1" 200
2025-06-08 08:06:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:34736 - "GET /health HTTP/1.0" 200
2025-06-08 08:06:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44976 - "GET /health HTTP/1.1" 200
2025-06-08 08:07:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53600 - "GET /health HTTP/1.1" 200
2025-06-08 08:07:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:41550 - "GET /health HTTP/1.0" 200
2025-06-08 08:07:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40826 - "GET /health HTTP/1.1" 200
2025-06-08 08:08:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50556 - "GET /health HTTP/1.1" 200
2025-06-08 08:08:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:54356 - "GET /health HTTP/1.0" 200
2025-06-08 08:08:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51072 - "GET /health HTTP/1.1" 200
2025-06-08 08:09:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35604 - "GET /health HTTP/1.1" 200
2025-06-08 08:09:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:55896 - "GET /health HTTP/1.0" 200
2025-06-08 08:09:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35566 - "GET /health HTTP/1.1" 200
2025-06-08 08:10:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44804 - "GET /health HTTP/1.1" 200
2025-06-08 08:10:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:56576 - "GET /health HTTP/1.0" 200
2025-06-08 08:10:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35630 - "GET /health HTTP/1.1" 200
2025-06-08 08:11:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45502 - "GET /health HTTP/1.1" 200
2025-06-08 08:11:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:40764 - "GET /health HTTP/1.0" 200
2025-06-08 08:11:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38612 - "GET /health HTTP/1.1" 200
2025-06-08 08:12:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37334 - "GET /health HTTP/1.1" 200
2025-06-08 08:12:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:54928 - "GET /health HTTP/1.0" 200
2025-06-08 08:12:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49350 - "GET /health HTTP/1.1" 200
2025-06-08 08:13:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54308 - "GET /health HTTP/1.1" 200
2025-06-08 08:13:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:52856 - "GET /health HTTP/1.0" 200
2025-06-08 08:13:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47642 - "GET /health HTTP/1.1" 200
2025-06-08 08:14:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58530 - "GET /health HTTP/1.1" 200
2025-06-08 08:14:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:57412 - "GET /health HTTP/1.0" 200
2025-06-08 08:14:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45372 - "GET /health HTTP/1.1" 200
2025-06-08 08:15:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35180 - "GET /health HTTP/1.1" 200
2025-06-08 08:15:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:40670 - "GET /health HTTP/1.0" 200
2025-06-08 08:15:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40008 - "GET /health HTTP/1.1" 200
2025-06-08 08:16:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46568 - "GET /health HTTP/1.1" 200
2025-06-08 08:16:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:60316 - "GET /health HTTP/1.0" 200
2025-06-08 08:16:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39752 - "GET /health HTTP/1.1" 200
2025-06-08 08:17:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:32906 - "GET /health HTTP/1.1" 200
2025-06-08 08:17:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:41446 - "GET /health HTTP/1.0" 200
2025-06-08 08:17:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59996 - "GET /health HTTP/1.1" 200
2025-06-08 08:18:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44604 - "GET /health HTTP/1.1" 200
2025-06-08 08:18:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:35246 - "GET /health HTTP/1.0" 200
2025-06-08 08:18:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42586 - "GET /health HTTP/1.1" 200
2025-06-08 08:19:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46580 - "GET /health HTTP/1.1" 200
2025-06-08 08:19:42 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:53846 - "GET /health HTTP/1.0" 200
2025-06-08 08:19:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38466 - "GET /health HTTP/1.1" 200
2025-06-08 08:20:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59454 - "GET /health HTTP/1.1" 200
2025-06-08 08:20:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:59962 - "GET /health HTTP/1.0" 200
2025-06-08 08:20:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60948 - "GET /health HTTP/1.1" 200
2025-06-08 08:21:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47658 - "GET /health HTTP/1.1" 200
2025-06-08 08:21:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:43994 - "GET /health HTTP/1.0" 200
2025-06-08 08:21:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55878 - "GET /health HTTP/1.1" 200
2025-06-08 08:22:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47076 - "GET /health HTTP/1.1" 200
2025-06-08 08:22:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:53988 - "GET /health HTTP/1.0" 200
2025-06-08 08:22:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49192 - "GET /health HTTP/1.1" 200
2025-06-08 08:23:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56132 - "GET /health HTTP/1.1" 200
2025-06-08 08:23:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:45560 - "GET /health HTTP/1.0" 200
2025-06-08 08:23:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36996 - "GET /health HTTP/1.1" 200
2025-06-08 08:24:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58844 - "GET /health HTTP/1.1" 200
2025-06-08 08:24:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:49148 - "GET /health HTTP/1.0" 200
2025-06-08 08:24:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59950 - "GET /health HTTP/1.1" 200
2025-06-08 08:25:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60576 - "GET /health HTTP/1.1" 200
2025-06-08 08:25:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:58660 - "GET /health HTTP/1.0" 200
2025-06-08 08:25:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44984 - "GET /health HTTP/1.1" 200
2025-06-08 08:26:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48084 - "GET /health HTTP/1.1" 200
2025-06-08 08:26:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:40552 - "GET /health HTTP/1.0" 200
2025-06-08 08:26:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60106 - "GET /health HTTP/1.1" 200
2025-06-08 08:27:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39976 - "GET /health HTTP/1.1" 200
2025-06-08 08:27:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:59516 - "GET /health HTTP/1.0" 200
2025-06-08 08:27:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56054 - "GET /health HTTP/1.1" 200
2025-06-08 08:28:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52280 - "GET /health HTTP/1.1" 200
2025-06-08 08:28:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:43894 - "GET /health HTTP/1.0" 200
2025-06-08 08:28:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38178 - "GET /health HTTP/1.1" 200
2025-06-08 08:29:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45366 - "GET /health HTTP/1.1" 200
2025-06-08 08:29:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:35222 - "GET /health HTTP/1.0" 200
2025-06-08 08:29:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55102 - "GET /health HTTP/1.1" 200
2025-06-08 08:30:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43904 - "GET /health HTTP/1.1" 200
2025-06-08 08:30:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:58764 - "GET /health HTTP/1.0" 200
2025-06-08 08:30:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39586 - "GET /health HTTP/1.1" 200
2025-06-08 08:31:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45412 - "GET /health HTTP/1.1" 200
2025-06-08 08:31:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:40996 - "GET /health HTTP/1.0" 200
2025-06-08 08:31:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57690 - "GET /health HTTP/1.1" 200
2025-06-08 08:32:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56512 - "GET /health HTTP/1.1" 200
2025-06-08 08:32:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:52306 - "GET /health HTTP/1.0" 200
2025-06-08 08:32:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34144 - "GET /health HTTP/1.1" 200
2025-06-08 08:33:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38932 - "GET /health HTTP/1.1" 200
2025-06-08 08:33:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:43612 - "GET /health HTTP/1.0" 200
2025-06-08 08:33:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47946 - "GET /health HTTP/1.1" 200
2025-06-08 08:34:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59020 - "GET /health HTTP/1.1" 200
2025-06-08 08:34:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:46872 - "GET /health HTTP/1.0" 200
2025-06-08 08:34:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54752 - "GET /health HTTP/1.1" 200
2025-06-08 08:35:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59536 - "GET /health HTTP/1.1" 200
2025-06-08 08:35:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:55238 - "GET /health HTTP/1.0" 200
2025-06-08 08:35:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47638 - "GET /health HTTP/1.1" 200
2025-06-08 08:36:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51092 - "GET /health HTTP/1.1" 200
2025-06-08 08:36:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:50204 - "GET /health HTTP/1.0" 200
2025-06-08 08:36:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36766 - "GET /health HTTP/1.1" 200
2025-06-08 08:37:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47018 - "GET /health HTTP/1.1" 200
2025-06-08 08:37:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:41892 - "GET /health HTTP/1.0" 200
2025-06-08 08:37:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42172 - "GET /health HTTP/1.1" 200
2025-06-08 08:38:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49712 - "GET /health HTTP/1.1" 200
2025-06-08 08:38:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:40190 - "GET /health HTTP/1.0" 200
2025-06-08 08:38:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47630 - "GET /health HTTP/1.1" 200
2025-06-08 08:39:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55554 - "GET /health HTTP/1.1" 200
2025-06-08 08:39:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:41764 - "GET /health HTTP/1.0" 200
2025-06-08 08:39:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59678 - "GET /health HTTP/1.1" 200
2025-06-08 08:40:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51634 - "GET /health HTTP/1.1" 200
2025-06-08 08:40:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:53152 - "GET /health HTTP/1.0" 200
2025-06-08 08:40:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49830 - "GET /health HTTP/1.1" 200
2025-06-08 08:41:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43828 - "GET /health HTTP/1.1" 200
2025-06-08 08:41:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:42004 - "GET /health HTTP/1.0" 200
2025-06-08 08:41:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38784 - "GET /health HTTP/1.1" 200
2025-06-08 08:42:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54984 - "GET /health HTTP/1.1" 200
2025-06-08 08:42:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:45540 - "GET /health HTTP/1.0" 200
2025-06-08 08:42:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60308 - "GET /health HTTP/1.1" 200
2025-06-08 08:43:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33278 - "GET /health HTTP/1.1" 200
2025-06-08 08:43:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:43474 - "GET /health HTTP/1.0" 200
2025-06-08 08:43:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50162 - "GET /health HTTP/1.1" 200
2025-06-08 08:44:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51384 - "GET /health HTTP/1.1" 200
2025-06-08 08:44:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:33198 - "GET /health HTTP/1.0" 200
2025-06-08 08:44:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41574 - "GET /health HTTP/1.1" 200
2025-06-08 08:45:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44872 - "GET /health HTTP/1.1" 200
2025-06-08 08:45:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:47008 - "GET /health HTTP/1.0" 200
2025-06-08 08:45:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57422 - "GET /health HTTP/1.1" 200
2025-06-08 08:46:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44042 - "GET /health HTTP/1.1" 200
2025-06-08 08:46:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:38534 - "GET /health HTTP/1.0" 200
2025-06-08 08:46:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44014 - "GET /health HTTP/1.1" 200
2025-06-08 08:47:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39598 - "GET /health HTTP/1.1" 200
2025-06-08 08:47:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:50616 - "GET /health HTTP/1.0" 200
2025-06-08 08:47:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59664 - "GET /health HTTP/1.1" 200
2025-06-08 08:48:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46980 - "GET /health HTTP/1.1" 200
2025-06-08 08:48:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:49068 - "GET /health HTTP/1.0" 200
2025-06-08 08:48:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42534 - "GET /health HTTP/1.1" 200
2025-06-08 08:49:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41180 - "GET /health HTTP/1.1" 200
2025-06-08 08:49:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:39206 - "GET /health HTTP/1.0" 200
2025-06-08 08:49:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52670 - "GET /health HTTP/1.1" 200
2025-06-08 08:50:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49354 - "GET /health HTTP/1.1" 200
2025-06-08 08:50:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:59530 - "GET /health HTTP/1.0" 200
2025-06-08 08:50:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33184 - "GET /health HTTP/1.1" 200
2025-06-08 08:51:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34524 - "GET /health HTTP/1.1" 200
2025-06-08 08:51:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:54654 - "GET /health HTTP/1.0" 200
2025-06-08 08:51:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47084 - "GET /health HTTP/1.1" 200
2025-06-08 08:52:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46528 - "GET /health HTTP/1.1" 200
2025-06-08 08:52:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:58680 - "GET /health HTTP/1.0" 200
2025-06-08 08:52:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34086 - "GET /health HTTP/1.1" 200
2025-06-08 08:53:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41586 - "GET /health HTTP/1.1" 200
2025-06-08 08:53:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:58780 - "GET /health HTTP/1.0" 200
2025-06-08 08:53:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39446 - "GET /health HTTP/1.1" 200
2025-06-08 08:54:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40084 - "GET /health HTTP/1.1" 200
2025-06-08 08:54:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:53330 - "GET /health HTTP/1.0" 200
2025-06-08 08:54:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50268 - "GET /health HTTP/1.1" 200
2025-06-08 08:55:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50990 - "GET /health HTTP/1.1" 200
2025-06-08 08:55:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:50128 - "GET /health HTTP/1.0" 200
2025-06-08 08:55:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45774 - "GET /health HTTP/1.1" 200
2025-06-08 08:56:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56488 - "GET /health HTTP/1.1" 200
2025-06-08 08:56:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:41978 - "GET /health HTTP/1.0" 200
2025-06-08 08:56:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38672 - "GET /health HTTP/1.1" 200
2025-06-08 08:57:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38488 - "GET /health HTTP/1.1" 200
2025-06-08 08:57:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:38120 - "GET /health HTTP/1.0" 200
2025-06-08 08:57:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49454 - "GET /health HTTP/1.1" 200
2025-06-08 08:58:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50004 - "GET /health HTTP/1.1" 200
2025-06-08 08:58:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:43358 - "GET /health HTTP/1.0" 200
2025-06-08 08:58:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41594 - "GET /health HTTP/1.1" 200
2025-06-08 08:59:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39978 - "GET /health HTTP/1.1" 200
2025-06-08 08:59:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:35592 - "GET /health HTTP/1.0" 200
2025-06-08 08:59:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46516 - "GET /health HTTP/1.1" 200
2025-06-08 09:00:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60016 - "GET /health HTTP/1.1" 200
2025-06-08 09:00:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:49528 - "GET /health HTTP/1.0" 200
2025-06-08 09:00:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37930 - "GET /health HTTP/1.1" 200
2025-06-08 09:01:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44638 - "GET /health HTTP/1.1" 200
2025-06-08 09:01:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:33632 - "GET /health HTTP/1.0" 200
2025-06-08 09:01:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47954 - "GET /health HTTP/1.1" 200
2025-06-08 09:02:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47692 - "GET /health HTTP/1.1" 200
2025-06-08 09:02:44 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:35918 - "GET /health HTTP/1.0" 200
2025-06-08 09:02:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39760 - "GET /health HTTP/1.1" 200
2025-06-08 09:03:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36554 - "GET /health HTTP/1.1" 200
2025-06-08 09:03:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:42120 - "GET /health HTTP/1.0" 200
2025-06-08 09:03:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44304 - "GET /health HTTP/1.1" 200
2025-06-08 09:04:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42438 - "GET /health HTTP/1.1" 200
2025-06-08 09:04:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:52730 - "GET /health HTTP/1.0" 200
2025-06-08 09:04:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36610 - "GET /health HTTP/1.1" 200
2025-06-08 09:05:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43356 - "GET /health HTTP/1.1" 200
2025-06-08 09:05:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:47562 - "GET /health HTTP/1.0" 200
2025-06-08 09:05:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39958 - "GET /health HTTP/1.1" 200
2025-06-08 09:06:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57376 - "GET /health HTTP/1.1" 200
2025-06-08 09:06:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:36258 - "GET /health HTTP/1.0" 200
2025-06-08 09:06:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43354 - "GET /health HTTP/1.1" 200
2025-06-08 09:07:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45188 - "GET /health HTTP/1.1" 200
2025-06-08 09:07:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:52648 - "GET /health HTTP/1.0" 200
2025-06-08 09:07:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45852 - "GET /health HTTP/1.1" 200
2025-06-08 09:08:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43142 - "GET /health HTTP/1.1" 200
2025-06-08 09:08:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:39756 - "GET /health HTTP/1.0" 200
2025-06-08 09:08:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45718 - "GET /health HTTP/1.1" 200
2025-06-08 09:09:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52926 - "GET /health HTTP/1.1" 200
2025-06-08 09:09:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:43522 - "GET /health HTTP/1.0" 200
2025-06-08 09:09:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53466 - "GET /health HTTP/1.1" 200
2025-06-08 09:10:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34352 - "GET /health HTTP/1.1" 200
2025-06-08 09:10:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:41572 - "GET /health HTTP/1.0" 200
2025-06-08 09:10:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56234 - "GET /health HTTP/1.1" 200
2025-06-08 09:11:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53904 - "GET /health HTTP/1.1" 200
2025-06-08 09:11:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:54414 - "GET /health HTTP/1.0" 200
2025-06-08 09:11:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34460 - "GET /health HTTP/1.1" 200
2025-06-08 09:12:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42376 - "GET /health HTTP/1.1" 200
2025-06-08 09:12:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:51022 - "GET /health HTTP/1.0" 200
2025-06-08 09:12:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45802 - "GET /health HTTP/1.1" 200
2025-06-08 09:13:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35414 - "GET /health HTTP/1.1" 200
2025-06-08 09:13:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:37008 - "GET /health HTTP/1.0" 200
2025-06-08 09:13:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56378 - "GET /health HTTP/1.1" 200
2025-06-08 09:14:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45438 - "GET /health HTTP/1.1" 200
2025-06-08 09:14:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:59680 - "GET /health HTTP/1.0" 200
2025-06-08 09:14:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38040 - "GET /health HTTP/1.1" 200
2025-06-08 09:15:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34742 - "GET /health HTTP/1.1" 200
2025-06-08 09:15:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:35210 - "GET /health HTTP/1.0" 200
2025-06-08 09:15:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35946 - "GET /health HTTP/1.1" 200
2025-06-08 09:16:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52010 - "GET /health HTTP/1.1" 200
2025-06-08 09:16:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:45942 - "GET /health HTTP/1.0" 200
2025-06-08 09:16:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56328 - "GET /health HTTP/1.1" 200
2025-06-08 09:17:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52342 - "GET /health HTTP/1.1" 200
2025-06-08 09:17:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:41066 - "GET /health HTTP/1.0" 200
2025-06-08 09:17:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38772 - "GET /health HTTP/1.1" 200
2025-06-08 09:18:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39482 - "GET /health HTTP/1.1" 200
2025-06-08 09:18:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:35724 - "GET /health HTTP/1.0" 200
2025-06-08 09:18:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60682 - "GET /health HTTP/1.1" 200
2025-06-08 09:19:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59718 - "GET /health HTTP/1.1" 200
2025-06-08 09:19:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:37094 - "GET /health HTTP/1.0" 200
2025-06-08 09:19:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60288 - "GET /health HTTP/1.1" 200
2025-06-08 09:20:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51536 - "GET /health HTTP/1.1" 200
2025-06-08 09:20:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:49624 - "GET /health HTTP/1.0" 200
2025-06-08 09:20:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34440 - "GET /health HTTP/1.1" 200
2025-06-08 09:21:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56368 - "GET /health HTTP/1.1" 200
2025-06-08 09:21:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:59394 - "GET /health HTTP/1.0" 200
2025-06-08 09:21:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34172 - "GET /health HTTP/1.1" 200
2025-06-08 09:22:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47528 - "GET /health HTTP/1.1" 200
2025-06-08 09:22:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:42662 - "GET /health HTTP/1.0" 200
2025-06-08 09:22:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34648 - "GET /health HTTP/1.1" 200
2025-06-08 09:23:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37290 - "GET /health HTTP/1.1" 200
2025-06-08 09:23:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:52916 - "GET /health HTTP/1.0" 200
2025-06-08 09:23:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49268 - "GET /health HTTP/1.1" 200
2025-06-08 09:24:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48590 - "GET /health HTTP/1.1" 200
2025-06-08 09:24:45 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:53098 - "GET /health HTTP/1.0" 200
2025-06-08 09:24:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56850 - "GET /health HTTP/1.1" 200
2025-06-08 09:25:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33176 - "GET /health HTTP/1.1" 200
2025-06-08 09:25:46 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:39534 - "GET /health HTTP/1.0" 200
2025-06-08 09:25:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39068 - "GET /health HTTP/1.1" 200
2025-06-08 09:26:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47906 - "GET /health HTTP/1.1" 200
2025-06-08 09:26:46 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:55528 - "GET /health HTTP/1.0" 200
2025-06-08 09:26:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39022 - "GET /health HTTP/1.1" 200
2025-06-08 09:27:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44638 - "GET /health HTTP/1.1" 200
2025-06-08 09:27:46 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:54208 - "GET /health HTTP/1.0" 200
2025-06-08 09:27:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60460 - "GET /health HTTP/1.1" 200
2025-06-08 09:28:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44100 - "GET /health HTTP/1.1" 200
2025-06-08 09:28:46 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:33282 - "GET /health HTTP/1.0" 200
2025-06-08 09:28:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38278 - "GET /health HTTP/1.1" 200
2025-06-08 09:29:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53438 - "GET /health HTTP/1.1" 200
2025-06-08 09:29:46 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:58706 - "GET /health HTTP/1.0" 200
2025-06-08 09:29:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33140 - "GET /health HTTP/1.1" 200
2025-06-08 09:30:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55002 - "GET /health HTTP/1.1" 200
2025-06-08 09:30:46 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:42666 - "GET /health HTTP/1.0" 200
2025-06-08 09:30:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59158 - "GET /health HTTP/1.1" 200
2025-06-08 09:31:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52692 - "GET /health HTTP/1.1" 200
2025-06-08 09:31:46 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:44832 - "GET /health HTTP/1.0" 200
2025-06-08 09:31:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49960 - "GET /health HTTP/1.1" 200
2025-06-08 09:32:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53782 - "GET /health HTTP/1.1" 200
2025-06-08 09:32:46 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:50648 - "GET /health HTTP/1.0" 200
2025-06-08 09:32:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54950 - "GET /health HTTP/1.1" 200
2025-06-08 09:33:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55666 - "GET /health HTTP/1.1" 200
2025-06-08 09:33:46 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:56920 - "GET /health HTTP/1.0" 200
2025-06-08 09:33:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37366 - "GET /health HTTP/1.1" 200
2025-06-08 09:34:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57794 - "GET /health HTTP/1.1" 200
2025-06-08 09:34:46 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:55708 - "GET /health HTTP/1.0" 200
2025-06-08 09:34:53 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42880 - "GET /health HTTP/1.1" 200
2025-06-08 09:35:23 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37368 - "GET /health HTTP/1.1" 200
2025-06-08 09:35:46 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:33420 - "GET /health HTTP/1.0" 200
2025-06-08 09:35:53 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59868 - "GET /health HTTP/1.1" 200
2025-06-08 09:36:23 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41394 - "GET /health HTTP/1.1" 200
2025-06-08 09:36:46 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:55118 - "GET /health HTTP/1.0" 200
2025-06-08 09:36:53 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58858 - "GET /health HTTP/1.1" 200
2025-06-08 09:37:23 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51686 - "GET /health HTTP/1.1" 200
2025-06-08 09:37:46 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:35092 - "GET /health HTTP/1.0" 200
2025-06-08 09:37:53 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52640 - "GET /health HTTP/1.1" 200
2025-06-08 09:38:23 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40418 - "GET /health HTTP/1.1" 200
2025-06-08 09:38:46 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:44272 - "GET /health HTTP/1.0" 200
2025-06-08 09:38:53 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50064 - "GET /health HTTP/1.1" 200
2025-06-08 09:39:23 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54500 - "GET /health HTTP/1.1" 200
2025-06-08 09:39:46 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:43112 - "GET /health HTTP/1.0" 200
2025-06-08 09:39:53 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39040 - "GET /health HTTP/1.1" 200
2025-06-08 09:40:23 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53498 - "GET /health HTTP/1.1" 200
2025-06-08 09:40:46 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:44112 - "GET /health HTTP/1.0" 200
2025-06-08 09:40:53 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41350 - "GET /health HTTP/1.1" 200
2025-06-08 09:41:23 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41412 - "GET /health HTTP/1.1" 200
2025-06-08 09:41:46 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:53168 - "GET /health HTTP/1.0" 200
2025-06-08 09:41:53 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46720 - "GET /health HTTP/1.1" 200
2025-06-08 09:42:23 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38652 - "GET /health HTTP/1.1" 200
2025-06-08 09:42:46 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:49128 - "GET /health HTTP/1.0" 200
2025-06-08 09:42:53 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59378 - "GET /health HTTP/1.1" 200
2025-06-08 09:43:23 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44594 - "GET /health HTTP/1.1" 200
2025-06-08 09:43:29 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-08 09:43:29 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-08 09:43:29 [INFO] main [main.py:121] - CPU服务关闭
2025-06-08 09:43:29 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-08 09:43:29 [INFO] uvicorn.error [server.py:86] - Finished server process [8]
2025-06-08 09:43:29 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-08 09:43:29 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-08 09:43:29 [INFO] main [main.py:121] - CPU服务关闭
2025-06-08 09:43:29 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-08 09:43:29 [INFO] uvicorn.error [server.py:86] - Finished server process [9]
