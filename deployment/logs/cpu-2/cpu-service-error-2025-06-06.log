2025-06-06 07:12:48 [ERROR] shared.clients.milvus_client [milvus_client.py:219] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-06 07:12:48 [ERROR] shared.clients.milvus_client [milvus_client.py:219] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-06 07:12:49 [ERROR] shared.clients.milvus_client [milvus_client.py:219] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-06 07:13:04 [ERROR] shared.clients.milvus_client [milvus_client.py:219] - 连接Milvus失败: <MilvusException: (code=2, message=<PERSON>ail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-06 07:15:50 [ERROR] api.simple_test [simple_test.py:85] - GPU连接测试失败: HTTPConnectionPool(host='***********', port=8001): Max retries exceeded with url: /health (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x7feb7478e790>, 'Connection to *********** timed out. (connect timeout=10)'))
2025-06-06 07:18:32 [ERROR] shared.clients.milvus_client [milvus_client.py:219] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-06 07:18:32 [ERROR] shared.clients.milvus_client [milvus_client.py:219] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-06 07:18:34 [ERROR] shared.clients.milvus_client [milvus_client.py:219] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-06 07:18:49 [ERROR] shared.clients.milvus_client [milvus_client.py:219] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-06 07:27:40 [ERROR] shared.clients.embedding_client [embedding_client.py:161] - 密集嵌入API请求失败: 401, "Invalid token"
2025-06-06 07:28:15 [ERROR] cores.indexing_service [indexing_service.py:174] - 混合索引 department 创建失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-06 07:28:15 [ERROR] cores.indexing_service [indexing_service.py:233] - 索引追加构建失败: department, 错误: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>, 耗时: 10.158秒
2025-06-06 07:28:15 [ERROR] cores.indexing_service [indexing_service.py:314] - 异步索引追加构建任务失败: cafb13ce-14b6-4005-9f9e-90bbfb066635, 错误: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-06 07:31:43 [ERROR] shared.clients.embedding_client [embedding_client.py:161] - 密集嵌入API请求失败: 401, "Invalid token"
2025-06-06 07:31:43 [ERROR] cores.indexing_service [indexing_service.py:233] - 索引追加构建失败: department, 错误: <DataNotMatchException: (code=1, message=Insert missed an field `text` to collection without set nullable==true or set default_value)>, 耗时: 0.390秒
2025-06-06 07:31:43 [ERROR] cores.indexing_service [indexing_service.py:314] - 异步索引追加构建任务失败: cbaca4cd-8088-4abd-8fb5-f94a0941e37e, 错误: <DataNotMatchException: (code=1, message=Insert missed an field `text` to collection without set nullable==true or set default_value)>
2025-06-06 07:32:15 [ERROR] shared.clients.embedding_client [embedding_client.py:161] - 密集嵌入API请求失败: 401, "Invalid token"
2025-06-06 07:32:16 [ERROR] cores.indexing_service [indexing_service.py:233] - 索引追加构建失败: department, 错误: <DataNotMatchException: (code=1, message=Insert missed an field `text` to collection without set nullable==true or set default_value)>, 耗时: 0.986秒
2025-06-06 07:32:16 [ERROR] cores.indexing_service [indexing_service.py:314] - 异步索引追加构建任务失败: 7e298f57-a32c-4fa8-b32f-7cb25bac6b1c, 错误: <DataNotMatchException: (code=1, message=Insert missed an field `text` to collection without set nullable==true or set default_value)>
