2025-06-08 06:52:10 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-08 06:52:10 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-08.log
2025-06-08 06:52:10 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-08.log
2025-06-08 06:52:10 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-08 06:52:10 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-08.log
2025-06-08 06:52:10 [INFO] uvicorn.error [server.py:76] - Started server process [9]
2025-06-08 06:52:10 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-08.log
2025-06-08 06:52:10 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-08 06:52:10 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-08 06:52:10 [INFO] main [main.py:83] - 环境: production
2025-06-08 06:52:10 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-08 06:52:10 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-08 06:52:10 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-08 06:52:10 [INFO] uvicorn.error [server.py:76] - Started server process [8]
2025-06-08 06:52:10 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-08 06:52:10 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-08 06:52:10 [INFO] main [main.py:83] - 环境: production
2025-06-08 06:52:10 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-08 06:52:10 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-08 06:52:10 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'deprecated'
2025-06-08 06:52:10 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-08 06:52:10 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-08 06:52:10 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-08 06:52:10 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'deprecated'
2025-06-08 06:52:10 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-08 06:52:10 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-08 06:52:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42578 - "GET /health HTTP/1.1" 200
2025-06-08 06:52:38 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:58108 - "GET /health HTTP/1.0" 200
2025-06-08 06:52:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54642 - "GET /health HTTP/1.1" 200
2025-06-08 06:52:50 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:35812 - "GET /docs HTTP/1.1" 200
2025-06-08 06:53:08 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:42046 - "GET /health HTTP/1.0" 200
2025-06-08 06:53:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60596 - "GET /health HTTP/1.1" 200
2025-06-08 06:53:24 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:38352 - "GET /health HTTP/1.0" 200
2025-06-08 06:53:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34658 - "GET /health HTTP/1.1" 200
2025-06-08 06:54:08 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:55378 - "GET /health HTTP/1.0" 200
2025-06-08 06:54:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57700 - "GET /health HTTP/1.1" 200
2025-06-08 06:54:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50806 - "GET /health HTTP/1.1" 200
2025-06-08 06:55:08 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:57772 - "GET /health HTTP/1.0" 200
2025-06-08 06:55:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42370 - "GET /health HTTP/1.1" 200
2025-06-08 06:55:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53432 - "GET /health HTTP/1.1" 200
2025-06-08 06:56:08 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:50530 - "GET /health HTTP/1.0" 200
2025-06-08 06:56:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43726 - "GET /health HTTP/1.1" 200
2025-06-08 06:56:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49568 - "GET /health HTTP/1.1" 200
2025-06-08 06:57:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:33218 - "GET /health HTTP/1.0" 200
2025-06-08 06:57:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48070 - "GET /health HTTP/1.1" 200
2025-06-08 06:57:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41042 - "GET /health HTTP/1.1" 200
2025-06-08 06:58:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:44422 - "GET /health HTTP/1.0" 200
2025-06-08 06:58:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38452 - "GET /health HTTP/1.1" 200
2025-06-08 06:58:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33526 - "GET /health HTTP/1.1" 200
2025-06-08 06:59:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:42490 - "GET /health HTTP/1.0" 200
2025-06-08 06:59:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33756 - "GET /health HTTP/1.1" 200
2025-06-08 06:59:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45400 - "GET /health HTTP/1.1" 200
2025-06-08 07:00:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:60080 - "GET /health HTTP/1.0" 200
2025-06-08 07:00:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41256 - "GET /health HTTP/1.1" 200
2025-06-08 07:00:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45590 - "GET /health HTTP/1.1" 200
2025-06-08 07:01:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:44532 - "GET /health HTTP/1.0" 200
2025-06-08 07:01:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41448 - "GET /health HTTP/1.1" 200
2025-06-08 07:01:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41220 - "GET /health HTTP/1.1" 200
2025-06-08 07:02:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:60804 - "GET /health HTTP/1.0" 200
2025-06-08 07:02:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43126 - "GET /health HTTP/1.1" 200
2025-06-08 07:02:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55386 - "GET /health HTTP/1.1" 200
2025-06-08 07:03:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:48952 - "GET /health HTTP/1.0" 200
2025-06-08 07:03:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39624 - "GET /health HTTP/1.1" 200
2025-06-08 07:03:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48398 - "GET /health HTTP/1.1" 200
2025-06-08 07:04:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:39398 - "GET /health HTTP/1.0" 200
2025-06-08 07:04:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47228 - "GET /health HTTP/1.1" 200
2025-06-08 07:04:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45886 - "GET /health HTTP/1.1" 200
2025-06-08 07:05:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:47986 - "GET /health HTTP/1.0" 200
2025-06-08 07:05:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46324 - "GET /health HTTP/1.1" 200
2025-06-08 07:05:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37294 - "GET /health HTTP/1.1" 200
2025-06-08 07:06:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:40294 - "GET /health HTTP/1.0" 200
2025-06-08 07:06:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34950 - "GET /health HTTP/1.1" 200
2025-06-08 07:06:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56170 - "GET /health HTTP/1.1" 200
2025-06-08 07:07:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:59478 - "GET /health HTTP/1.0" 200
2025-06-08 07:07:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51132 - "GET /health HTTP/1.1" 200
2025-06-08 07:07:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42328 - "GET /health HTTP/1.1" 200
2025-06-08 07:08:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:47214 - "GET /health HTTP/1.0" 200
2025-06-08 07:08:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48544 - "GET /health HTTP/1.1" 200
2025-06-08 07:08:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33592 - "GET /health HTTP/1.1" 200
2025-06-08 07:09:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:52520 - "GET /health HTTP/1.0" 200
2025-06-08 07:09:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46606 - "GET /health HTTP/1.1" 200
2025-06-08 07:09:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59932 - "GET /health HTTP/1.1" 200
2025-06-08 07:10:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:50060 - "GET /health HTTP/1.0" 200
2025-06-08 07:10:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42376 - "GET /health HTTP/1.1" 200
2025-06-08 07:10:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41808 - "GET /health HTTP/1.1" 200
2025-06-08 07:11:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:52264 - "GET /health HTTP/1.0" 200
2025-06-08 07:11:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59448 - "GET /health HTTP/1.1" 200
2025-06-08 07:11:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59802 - "GET /health HTTP/1.1" 200
2025-06-08 07:12:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:33252 - "GET /health HTTP/1.0" 200
2025-06-08 07:12:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39582 - "GET /health HTTP/1.1" 200
2025-06-08 07:12:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43064 - "GET /health HTTP/1.1" 200
2025-06-08 07:13:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:42174 - "GET /health HTTP/1.0" 200
2025-06-08 07:13:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56678 - "GET /health HTTP/1.1" 200
2025-06-08 07:13:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57180 - "GET /health HTTP/1.1" 200
2025-06-08 07:14:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:39988 - "GET /health HTTP/1.0" 200
2025-06-08 07:14:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46956 - "GET /health HTTP/1.1" 200
2025-06-08 07:14:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52442 - "GET /health HTTP/1.1" 200
2025-06-08 07:15:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:39104 - "GET /health HTTP/1.0" 200
2025-06-08 07:15:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56028 - "GET /health HTTP/1.1" 200
2025-06-08 07:15:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35222 - "GET /health HTTP/1.1" 200
2025-06-08 07:16:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:33730 - "GET /health HTTP/1.0" 200
2025-06-08 07:16:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47640 - "GET /health HTTP/1.1" 200
2025-06-08 07:16:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38316 - "GET /health HTTP/1.1" 200
2025-06-08 07:17:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:48658 - "GET /health HTTP/1.0" 200
2025-06-08 07:17:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56164 - "GET /health HTTP/1.1" 200
2025-06-08 07:17:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54800 - "GET /health HTTP/1.1" 200
2025-06-08 07:18:09 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:40410 - "GET /health HTTP/1.0" 200
2025-06-08 07:18:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53908 - "GET /health HTTP/1.1" 200
2025-06-08 07:18:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39274 - "GET /health HTTP/1.1" 200
2025-06-08 07:19:10 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:41202 - "GET /health HTTP/1.0" 200
2025-06-08 07:19:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45412 - "GET /health HTTP/1.1" 200
2025-06-08 07:19:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38366 - "GET /health HTTP/1.1" 200
2025-06-08 07:20:10 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:43878 - "GET /health HTTP/1.0" 200
2025-06-08 07:20:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37596 - "GET /health HTTP/1.1" 200
2025-06-08 07:20:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45284 - "GET /health HTTP/1.1" 200
2025-06-08 07:21:10 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:48320 - "GET /health HTTP/1.0" 200
2025-06-08 07:21:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41866 - "GET /health HTTP/1.1" 200
2025-06-08 07:21:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56202 - "GET /health HTTP/1.1" 200
2025-06-08 07:22:10 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:37404 - "GET /health HTTP/1.0" 200
2025-06-08 07:22:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50050 - "GET /health HTTP/1.1" 200
2025-06-08 07:22:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47718 - "GET /health HTTP/1.1" 200
2025-06-08 07:23:10 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:40134 - "GET /health HTTP/1.0" 200
2025-06-08 07:23:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37350 - "GET /health HTTP/1.1" 200
2025-06-08 07:23:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46672 - "GET /health HTTP/1.1" 200
2025-06-08 07:24:10 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:49952 - "GET /health HTTP/1.0" 200
2025-06-08 07:24:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54714 - "GET /health HTTP/1.1" 200
2025-06-08 07:24:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52018 - "GET /health HTTP/1.1" 200
2025-06-08 07:25:10 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:36512 - "GET /health HTTP/1.0" 200
2025-06-08 07:25:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41258 - "GET /health HTTP/1.1" 200
2025-06-08 07:25:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44856 - "GET /health HTTP/1.1" 200
2025-06-08 07:26:10 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:51918 - "GET /health HTTP/1.0" 200
2025-06-08 07:26:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46794 - "GET /health HTTP/1.1" 200
2025-06-08 07:26:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49174 - "GET /health HTTP/1.1" 200
2025-06-08 07:27:10 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:38062 - "GET /health HTTP/1.0" 200
2025-06-08 07:27:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46520 - "GET /health HTTP/1.1" 200
2025-06-08 07:27:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42676 - "GET /health HTTP/1.1" 200
2025-06-08 07:28:10 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:58584 - "GET /health HTTP/1.0" 200
2025-06-08 07:28:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56374 - "GET /health HTTP/1.1" 200
2025-06-08 07:28:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52962 - "GET /health HTTP/1.1" 200
2025-06-08 07:29:10 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:35448 - "GET /health HTTP/1.0" 200
2025-06-08 07:29:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60036 - "GET /health HTTP/1.1" 200
2025-06-08 07:29:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50412 - "GET /health HTTP/1.1" 200
2025-06-08 07:30:10 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:44894 - "GET /health HTTP/1.0" 200
2025-06-08 07:30:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39328 - "GET /health HTTP/1.1" 200
2025-06-08 07:30:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56402 - "GET /health HTTP/1.1" 200
2025-06-08 07:31:10 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:43000 - "GET /health HTTP/1.0" 200
2025-06-08 07:31:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49846 - "GET /health HTTP/1.1" 200
2025-06-08 07:31:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44516 - "GET /health HTTP/1.1" 200
2025-06-08 07:32:10 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:33484 - "GET /health HTTP/1.0" 200
2025-06-08 07:32:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39954 - "GET /health HTTP/1.1" 200
2025-06-08 07:32:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48666 - "GET /health HTTP/1.1" 200
2025-06-08 07:33:10 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:38276 - "GET /health HTTP/1.0" 200
2025-06-08 07:33:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57714 - "GET /health HTTP/1.1" 200
2025-06-08 07:33:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35458 - "GET /health HTTP/1.1" 200
2025-06-08 07:34:10 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:50126 - "GET /health HTTP/1.0" 200
2025-06-08 07:34:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47372 - "GET /health HTTP/1.1" 200
2025-06-08 07:34:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43004 - "GET /health HTTP/1.1" 200
2025-06-08 07:35:10 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:41596 - "GET /health HTTP/1.0" 200
2025-06-08 07:35:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45650 - "GET /health HTTP/1.1" 200
2025-06-08 07:35:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34198 - "GET /health HTTP/1.1" 200
2025-06-08 07:36:10 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:35778 - "GET /health HTTP/1.0" 200
2025-06-08 07:36:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53628 - "GET /health HTTP/1.1" 200
2025-06-08 07:36:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46490 - "GET /health HTTP/1.1" 200
2025-06-08 07:37:10 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:52448 - "GET /health HTTP/1.0" 200
2025-06-08 07:37:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55938 - "GET /health HTTP/1.1" 200
2025-06-08 07:37:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33920 - "GET /health HTTP/1.1" 200
2025-06-08 07:38:10 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:40428 - "GET /health HTTP/1.0" 200
2025-06-08 07:38:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38986 - "GET /health HTTP/1.1" 200
2025-06-08 07:38:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59726 - "GET /health HTTP/1.1" 200
2025-06-08 07:39:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:36846 - "GET /health HTTP/1.0" 200
2025-06-08 07:39:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38368 - "GET /health HTTP/1.1" 200
2025-06-08 07:39:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44158 - "GET /health HTTP/1.1" 200
2025-06-08 07:40:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:38012 - "GET /health HTTP/1.0" 200
2025-06-08 07:40:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48080 - "GET /health HTTP/1.1" 200
2025-06-08 07:40:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55154 - "GET /health HTTP/1.1" 200
2025-06-08 07:41:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:43100 - "GET /health HTTP/1.0" 200
2025-06-08 07:41:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53590 - "GET /health HTTP/1.1" 200
2025-06-08 07:41:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40800 - "GET /health HTTP/1.1" 200
2025-06-08 07:42:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:57234 - "GET /health HTTP/1.0" 200
2025-06-08 07:42:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42624 - "GET /health HTTP/1.1" 200
2025-06-08 07:42:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38780 - "GET /health HTTP/1.1" 200
2025-06-08 07:43:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:33694 - "GET /health HTTP/1.0" 200
2025-06-08 07:43:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57570 - "GET /health HTTP/1.1" 200
2025-06-08 07:43:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55402 - "GET /health HTTP/1.1" 200
2025-06-08 07:44:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:50208 - "GET /health HTTP/1.0" 200
2025-06-08 07:44:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33288 - "GET /health HTTP/1.1" 200
2025-06-08 07:44:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33742 - "GET /health HTTP/1.1" 200
2025-06-08 07:45:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:45314 - "GET /health HTTP/1.0" 200
2025-06-08 07:45:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54778 - "GET /health HTTP/1.1" 200
2025-06-08 07:45:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57782 - "GET /health HTTP/1.1" 200
2025-06-08 07:46:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:45776 - "GET /health HTTP/1.0" 200
2025-06-08 07:46:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51954 - "GET /health HTTP/1.1" 200
2025-06-08 07:46:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52266 - "GET /health HTTP/1.1" 200
2025-06-08 07:47:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:34172 - "GET /health HTTP/1.0" 200
2025-06-08 07:47:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53740 - "GET /health HTTP/1.1" 200
2025-06-08 07:47:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60184 - "GET /health HTTP/1.1" 200
2025-06-08 07:48:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:60122 - "GET /health HTTP/1.0" 200
2025-06-08 07:48:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39340 - "GET /health HTTP/1.1" 200
2025-06-08 07:48:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60722 - "GET /health HTTP/1.1" 200
2025-06-08 07:49:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:50818 - "GET /health HTTP/1.0" 200
2025-06-08 07:49:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38110 - "GET /health HTTP/1.1" 200
2025-06-08 07:49:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43232 - "GET /health HTTP/1.1" 200
2025-06-08 07:50:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:56246 - "GET /health HTTP/1.0" 200
2025-06-08 07:50:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38088 - "GET /health HTTP/1.1" 200
2025-06-08 07:50:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39338 - "GET /health HTTP/1.1" 200
2025-06-08 07:51:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:59836 - "GET /health HTTP/1.0" 200
2025-06-08 07:51:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54770 - "GET /health HTTP/1.1" 200
2025-06-08 07:51:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39308 - "GET /health HTTP/1.1" 200
2025-06-08 07:52:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:36456 - "GET /health HTTP/1.0" 200
2025-06-08 07:52:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56216 - "GET /health HTTP/1.1" 200
2025-06-08 07:52:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49406 - "GET /health HTTP/1.1" 200
2025-06-08 07:53:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:59292 - "GET /health HTTP/1.0" 200
2025-06-08 07:53:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55800 - "GET /health HTTP/1.1" 200
2025-06-08 07:53:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57422 - "GET /health HTTP/1.1" 200
2025-06-08 07:54:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:47636 - "GET /health HTTP/1.0" 200
2025-06-08 07:54:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33252 - "GET /health HTTP/1.1" 200
2025-06-08 07:54:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48576 - "GET /health HTTP/1.1" 200
2025-06-08 07:55:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:47956 - "GET /health HTTP/1.0" 200
2025-06-08 07:55:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48712 - "GET /health HTTP/1.1" 200
2025-06-08 07:55:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40406 - "GET /health HTTP/1.1" 200
2025-06-08 07:56:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:55612 - "GET /health HTTP/1.0" 200
2025-06-08 07:56:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59144 - "GET /health HTTP/1.1" 200
2025-06-08 07:56:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45772 - "GET /health HTTP/1.1" 200
2025-06-08 07:57:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:40010 - "GET /health HTTP/1.0" 200
2025-06-08 07:57:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34584 - "GET /health HTTP/1.1" 200
2025-06-08 07:57:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56786 - "GET /health HTTP/1.1" 200
2025-06-08 07:58:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:50194 - "GET /health HTTP/1.0" 200
2025-06-08 07:58:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42972 - "GET /health HTTP/1.1" 200
2025-06-08 07:58:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39040 - "GET /health HTTP/1.1" 200
2025-06-08 07:59:11 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:58934 - "GET /health HTTP/1.0" 200
2025-06-08 07:59:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41042 - "GET /health HTTP/1.1" 200
2025-06-08 07:59:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52228 - "GET /health HTTP/1.1" 200
2025-06-08 08:00:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:53668 - "GET /health HTTP/1.0" 200
2025-06-08 08:00:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39168 - "GET /health HTTP/1.1" 200
2025-06-08 08:00:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39872 - "GET /health HTTP/1.1" 200
2025-06-08 08:01:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:47934 - "GET /health HTTP/1.0" 200
2025-06-08 08:01:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60672 - "GET /health HTTP/1.1" 200
2025-06-08 08:01:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33914 - "GET /health HTTP/1.1" 200
2025-06-08 08:02:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:40732 - "GET /health HTTP/1.0" 200
2025-06-08 08:02:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40918 - "GET /health HTTP/1.1" 200
2025-06-08 08:02:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60126 - "GET /health HTTP/1.1" 200
2025-06-08 08:03:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:53512 - "GET /health HTTP/1.0" 200
2025-06-08 08:03:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51856 - "GET /health HTTP/1.1" 200
2025-06-08 08:03:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42216 - "GET /health HTTP/1.1" 200
2025-06-08 08:04:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:55450 - "GET /health HTTP/1.0" 200
2025-06-08 08:04:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56602 - "GET /health HTTP/1.1" 200
2025-06-08 08:04:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49192 - "GET /health HTTP/1.1" 200
2025-06-08 08:05:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:58680 - "GET /health HTTP/1.0" 200
2025-06-08 08:05:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35186 - "GET /health HTTP/1.1" 200
2025-06-08 08:05:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47490 - "GET /health HTTP/1.1" 200
2025-06-08 08:06:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:40812 - "GET /health HTTP/1.0" 200
2025-06-08 08:06:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44776 - "GET /health HTTP/1.1" 200
2025-06-08 08:06:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44978 - "GET /health HTTP/1.1" 200
2025-06-08 08:07:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:33806 - "GET /health HTTP/1.0" 200
2025-06-08 08:07:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53598 - "GET /health HTTP/1.1" 200
2025-06-08 08:07:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40826 - "GET /health HTTP/1.1" 200
2025-06-08 08:08:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:38146 - "GET /health HTTP/1.0" 200
2025-06-08 08:08:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50568 - "GET /health HTTP/1.1" 200
2025-06-08 08:08:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51082 - "GET /health HTTP/1.1" 200
2025-06-08 08:09:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:60414 - "GET /health HTTP/1.0" 200
2025-06-08 08:09:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35618 - "GET /health HTTP/1.1" 200
2025-06-08 08:09:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35574 - "GET /health HTTP/1.1" 200
2025-06-08 08:10:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:53324 - "GET /health HTTP/1.0" 200
2025-06-08 08:10:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44812 - "GET /health HTTP/1.1" 200
2025-06-08 08:10:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35614 - "GET /health HTTP/1.1" 200
2025-06-08 08:11:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:49434 - "GET /health HTTP/1.0" 200
2025-06-08 08:11:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45514 - "GET /health HTTP/1.1" 200
2025-06-08 08:11:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38620 - "GET /health HTTP/1.1" 200
2025-06-08 08:12:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:49462 - "GET /health HTTP/1.0" 200
2025-06-08 08:12:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37328 - "GET /health HTTP/1.1" 200
2025-06-08 08:12:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49358 - "GET /health HTTP/1.1" 200
2025-06-08 08:13:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:57032 - "GET /health HTTP/1.0" 200
2025-06-08 08:13:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54302 - "GET /health HTTP/1.1" 200
2025-06-08 08:13:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47634 - "GET /health HTTP/1.1" 200
2025-06-08 08:14:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:38892 - "GET /health HTTP/1.0" 200
2025-06-08 08:14:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58536 - "GET /health HTTP/1.1" 200
2025-06-08 08:14:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45358 - "GET /health HTTP/1.1" 200
2025-06-08 08:15:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:56000 - "GET /health HTTP/1.0" 200
2025-06-08 08:15:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35174 - "GET /health HTTP/1.1" 200
2025-06-08 08:15:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40020 - "GET /health HTTP/1.1" 200
2025-06-08 08:16:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:45620 - "GET /health HTTP/1.0" 200
2025-06-08 08:16:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46578 - "GET /health HTTP/1.1" 200
2025-06-08 08:16:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39764 - "GET /health HTTP/1.1" 200
2025-06-08 08:17:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:47810 - "GET /health HTTP/1.0" 200
2025-06-08 08:17:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:32902 - "GET /health HTTP/1.1" 200
2025-06-08 08:17:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59982 - "GET /health HTTP/1.1" 200
2025-06-08 08:18:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:55330 - "GET /health HTTP/1.0" 200
2025-06-08 08:18:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44588 - "GET /health HTTP/1.1" 200
2025-06-08 08:18:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42584 - "GET /health HTTP/1.1" 200
2025-06-08 08:19:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:36054 - "GET /health HTTP/1.0" 200
2025-06-08 08:19:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46564 - "GET /health HTTP/1.1" 200
2025-06-08 08:19:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38460 - "GET /health HTTP/1.1" 200
2025-06-08 08:20:12 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:41888 - "GET /health HTTP/1.0" 200
2025-06-08 08:20:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59438 - "GET /health HTTP/1.1" 200
2025-06-08 08:20:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60946 - "GET /health HTTP/1.1" 200
2025-06-08 08:21:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:33440 - "GET /health HTTP/1.0" 200
2025-06-08 08:21:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47646 - "GET /health HTTP/1.1" 200
2025-06-08 08:21:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55882 - "GET /health HTTP/1.1" 200
2025-06-08 08:22:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:59644 - "GET /health HTTP/1.0" 200
2025-06-08 08:22:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47076 - "GET /health HTTP/1.1" 200
2025-06-08 08:22:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49190 - "GET /health HTTP/1.1" 200
2025-06-08 08:23:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:37568 - "GET /health HTTP/1.0" 200
2025-06-08 08:23:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56118 - "GET /health HTTP/1.1" 200
2025-06-08 08:23:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36980 - "GET /health HTTP/1.1" 200
2025-06-08 08:24:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:43834 - "GET /health HTTP/1.0" 200
2025-06-08 08:24:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58846 - "GET /health HTTP/1.1" 200
2025-06-08 08:24:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59952 - "GET /health HTTP/1.1" 200
2025-06-08 08:25:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:45926 - "GET /health HTTP/1.0" 200
2025-06-08 08:25:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60586 - "GET /health HTTP/1.1" 200
2025-06-08 08:25:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44994 - "GET /health HTTP/1.1" 200
2025-06-08 08:26:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:49992 - "GET /health HTTP/1.0" 200
2025-06-08 08:26:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48070 - "GET /health HTTP/1.1" 200
2025-06-08 08:26:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60110 - "GET /health HTTP/1.1" 200
2025-06-08 08:27:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:54942 - "GET /health HTTP/1.0" 200
2025-06-08 08:27:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39980 - "GET /health HTTP/1.1" 200
2025-06-08 08:27:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56062 - "GET /health HTTP/1.1" 200
2025-06-08 08:28:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:47242 - "GET /health HTTP/1.0" 200
2025-06-08 08:28:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52270 - "GET /health HTTP/1.1" 200
2025-06-08 08:28:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38182 - "GET /health HTTP/1.1" 200
2025-06-08 08:29:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:54592 - "GET /health HTTP/1.0" 200
2025-06-08 08:29:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45380 - "GET /health HTTP/1.1" 200
2025-06-08 08:29:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55110 - "GET /health HTTP/1.1" 200
2025-06-08 08:30:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:33744 - "GET /health HTTP/1.0" 200
2025-06-08 08:30:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43890 - "GET /health HTTP/1.1" 200
2025-06-08 08:30:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39584 - "GET /health HTTP/1.1" 200
2025-06-08 08:31:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:38176 - "GET /health HTTP/1.0" 200
2025-06-08 08:31:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45424 - "GET /health HTTP/1.1" 200
2025-06-08 08:31:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57698 - "GET /health HTTP/1.1" 200
2025-06-08 08:32:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:39666 - "GET /health HTTP/1.0" 200
2025-06-08 08:32:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56522 - "GET /health HTTP/1.1" 200
2025-06-08 08:32:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34156 - "GET /health HTTP/1.1" 200
2025-06-08 08:33:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:47748 - "GET /health HTTP/1.0" 200
2025-06-08 08:33:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38944 - "GET /health HTTP/1.1" 200
2025-06-08 08:33:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47952 - "GET /health HTTP/1.1" 200
2025-06-08 08:34:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:56310 - "GET /health HTTP/1.0" 200
2025-06-08 08:34:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59036 - "GET /health HTTP/1.1" 200
2025-06-08 08:34:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54754 - "GET /health HTTP/1.1" 200
2025-06-08 08:35:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:56156 - "GET /health HTTP/1.0" 200
2025-06-08 08:35:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59544 - "GET /health HTTP/1.1" 200
2025-06-08 08:35:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47636 - "GET /health HTTP/1.1" 200
2025-06-08 08:36:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:53952 - "GET /health HTTP/1.0" 200
2025-06-08 08:36:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51104 - "GET /health HTTP/1.1" 200
2025-06-08 08:36:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36762 - "GET /health HTTP/1.1" 200
2025-06-08 08:37:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:52102 - "GET /health HTTP/1.0" 200
2025-06-08 08:37:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47026 - "GET /health HTTP/1.1" 200
2025-06-08 08:37:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42186 - "GET /health HTTP/1.1" 200
2025-06-08 08:38:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:53226 - "GET /health HTTP/1.0" 200
2025-06-08 08:38:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49718 - "GET /health HTTP/1.1" 200
2025-06-08 08:38:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47632 - "GET /health HTTP/1.1" 200
2025-06-08 08:39:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:44112 - "GET /health HTTP/1.0" 200
2025-06-08 08:39:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55562 - "GET /health HTTP/1.1" 200
2025-06-08 08:39:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59688 - "GET /health HTTP/1.1" 200
2025-06-08 08:40:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:38458 - "GET /health HTTP/1.0" 200
2025-06-08 08:40:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51618 - "GET /health HTTP/1.1" 200
2025-06-08 08:40:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49832 - "GET /health HTTP/1.1" 200
2025-06-08 08:41:13 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:60282 - "GET /health HTTP/1.0" 200
2025-06-08 08:41:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43840 - "GET /health HTTP/1.1" 200
2025-06-08 08:41:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38786 - "GET /health HTTP/1.1" 200
2025-06-08 08:42:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:50312 - "GET /health HTTP/1.0" 200
2025-06-08 08:42:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54998 - "GET /health HTTP/1.1" 200
2025-06-08 08:42:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60302 - "GET /health HTTP/1.1" 200
2025-06-08 08:43:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:59160 - "GET /health HTTP/1.0" 200
2025-06-08 08:43:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33282 - "GET /health HTTP/1.1" 200
2025-06-08 08:43:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50172 - "GET /health HTTP/1.1" 200
2025-06-08 08:44:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:56970 - "GET /health HTTP/1.0" 200
2025-06-08 08:44:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51400 - "GET /health HTTP/1.1" 200
2025-06-08 08:44:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41586 - "GET /health HTTP/1.1" 200
2025-06-08 08:45:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:51374 - "GET /health HTTP/1.0" 200
2025-06-08 08:45:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44886 - "GET /health HTTP/1.1" 200
2025-06-08 08:45:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57436 - "GET /health HTTP/1.1" 200
2025-06-08 08:46:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:40518 - "GET /health HTTP/1.0" 200
2025-06-08 08:46:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44046 - "GET /health HTTP/1.1" 200
2025-06-08 08:46:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44008 - "GET /health HTTP/1.1" 200
2025-06-08 08:47:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:48622 - "GET /health HTTP/1.0" 200
2025-06-08 08:47:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39594 - "GET /health HTTP/1.1" 200
2025-06-08 08:47:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59664 - "GET /health HTTP/1.1" 200
2025-06-08 08:48:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:35098 - "GET /health HTTP/1.0" 200
2025-06-08 08:48:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46984 - "GET /health HTTP/1.1" 200
2025-06-08 08:48:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42518 - "GET /health HTTP/1.1" 200
2025-06-08 08:49:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:33992 - "GET /health HTTP/1.0" 200
2025-06-08 08:49:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41176 - "GET /health HTTP/1.1" 200
2025-06-08 08:49:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52660 - "GET /health HTTP/1.1" 200
2025-06-08 08:50:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:59724 - "GET /health HTTP/1.0" 200
2025-06-08 08:50:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49342 - "GET /health HTTP/1.1" 200
2025-06-08 08:50:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33178 - "GET /health HTTP/1.1" 200
2025-06-08 08:51:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:39920 - "GET /health HTTP/1.0" 200
2025-06-08 08:51:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34528 - "GET /health HTTP/1.1" 200
2025-06-08 08:51:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47094 - "GET /health HTTP/1.1" 200
2025-06-08 08:52:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:38556 - "GET /health HTTP/1.0" 200
2025-06-08 08:52:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46538 - "GET /health HTTP/1.1" 200
2025-06-08 08:52:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34088 - "GET /health HTTP/1.1" 200
2025-06-08 08:53:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:34638 - "GET /health HTTP/1.0" 200
2025-06-08 08:53:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41586 - "GET /health HTTP/1.1" 200
2025-06-08 08:53:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39458 - "GET /health HTTP/1.1" 200
2025-06-08 08:54:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:51846 - "GET /health HTTP/1.0" 200
2025-06-08 08:54:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40080 - "GET /health HTTP/1.1" 200
2025-06-08 08:54:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50256 - "GET /health HTTP/1.1" 200
2025-06-08 08:55:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:42452 - "GET /health HTTP/1.0" 200
2025-06-08 08:55:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50986 - "GET /health HTTP/1.1" 200
2025-06-08 08:55:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45762 - "GET /health HTTP/1.1" 200
2025-06-08 08:56:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:40800 - "GET /health HTTP/1.0" 200
2025-06-08 08:56:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56480 - "GET /health HTTP/1.1" 200
2025-06-08 08:56:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38660 - "GET /health HTTP/1.1" 200
2025-06-08 08:57:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:51482 - "GET /health HTTP/1.0" 200
2025-06-08 08:57:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38482 - "GET /health HTTP/1.1" 200
2025-06-08 08:57:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49440 - "GET /health HTTP/1.1" 200
2025-06-08 08:58:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:50394 - "GET /health HTTP/1.0" 200
2025-06-08 08:58:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50000 - "GET /health HTTP/1.1" 200
2025-06-08 08:58:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41582 - "GET /health HTTP/1.1" 200
2025-06-08 08:59:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:56242 - "GET /health HTTP/1.0" 200
2025-06-08 08:59:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39976 - "GET /health HTTP/1.1" 200
2025-06-08 08:59:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46524 - "GET /health HTTP/1.1" 200
2025-06-08 09:00:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:58294 - "GET /health HTTP/1.0" 200
2025-06-08 09:00:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60032 - "GET /health HTTP/1.1" 200
2025-06-08 09:00:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37932 - "GET /health HTTP/1.1" 200
2025-06-08 09:01:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:50500 - "GET /health HTTP/1.0" 200
2025-06-08 09:01:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44636 - "GET /health HTTP/1.1" 200
2025-06-08 09:01:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47958 - "GET /health HTTP/1.1" 200
2025-06-08 09:02:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:49816 - "GET /health HTTP/1.0" 200
2025-06-08 09:02:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47684 - "GET /health HTTP/1.1" 200
2025-06-08 09:02:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39766 - "GET /health HTTP/1.1" 200
2025-06-08 09:03:14 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:48468 - "GET /health HTTP/1.0" 200
2025-06-08 09:03:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36558 - "GET /health HTTP/1.1" 200
2025-06-08 09:03:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44302 - "GET /health HTTP/1.1" 200
2025-06-08 09:04:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:46828 - "GET /health HTTP/1.0" 200
2025-06-08 09:04:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42448 - "GET /health HTTP/1.1" 200
2025-06-08 09:04:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36616 - "GET /health HTTP/1.1" 200
2025-06-08 09:05:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:46492 - "GET /health HTTP/1.0" 200
2025-06-08 09:05:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43364 - "GET /health HTTP/1.1" 200
2025-06-08 09:05:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39962 - "GET /health HTTP/1.1" 200
2025-06-08 09:06:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:36790 - "GET /health HTTP/1.0" 200
2025-06-08 09:06:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57380 - "GET /health HTTP/1.1" 200
2025-06-08 09:06:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43338 - "GET /health HTTP/1.1" 200
2025-06-08 09:07:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:47668 - "GET /health HTTP/1.0" 200
2025-06-08 09:07:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45204 - "GET /health HTTP/1.1" 200
2025-06-08 09:07:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45864 - "GET /health HTTP/1.1" 200
2025-06-08 09:08:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:37102 - "GET /health HTTP/1.0" 200
2025-06-08 09:08:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43148 - "GET /health HTTP/1.1" 200
2025-06-08 09:08:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45710 - "GET /health HTTP/1.1" 200
2025-06-08 09:09:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:55366 - "GET /health HTTP/1.0" 200
2025-06-08 09:09:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52922 - "GET /health HTTP/1.1" 200
2025-06-08 09:09:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53450 - "GET /health HTTP/1.1" 200
2025-06-08 09:10:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:38306 - "GET /health HTTP/1.0" 200
2025-06-08 09:10:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34342 - "GET /health HTTP/1.1" 200
2025-06-08 09:10:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56232 - "GET /health HTTP/1.1" 200
2025-06-08 09:11:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:59344 - "GET /health HTTP/1.0" 200
2025-06-08 09:11:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53896 - "GET /health HTTP/1.1" 200
2025-06-08 09:11:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34470 - "GET /health HTTP/1.1" 200
2025-06-08 09:12:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:51946 - "GET /health HTTP/1.0" 200
2025-06-08 09:12:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42388 - "GET /health HTTP/1.1" 200
2025-06-08 09:12:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45814 - "GET /health HTTP/1.1" 200
2025-06-08 09:13:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:50210 - "GET /health HTTP/1.0" 200
2025-06-08 09:13:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35424 - "GET /health HTTP/1.1" 200
2025-06-08 09:13:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56364 - "GET /health HTTP/1.1" 200
2025-06-08 09:14:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:59814 - "GET /health HTTP/1.0" 200
2025-06-08 09:14:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45428 - "GET /health HTTP/1.1" 200
2025-06-08 09:14:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38052 - "GET /health HTTP/1.1" 200
2025-06-08 09:15:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:60118 - "GET /health HTTP/1.0" 200
2025-06-08 09:15:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34756 - "GET /health HTTP/1.1" 200
2025-06-08 09:15:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35932 - "GET /health HTTP/1.1" 200
2025-06-08 09:16:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:55944 - "GET /health HTTP/1.0" 200
2025-06-08 09:16:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52000 - "GET /health HTTP/1.1" 200
2025-06-08 09:16:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56316 - "GET /health HTTP/1.1" 200
2025-06-08 09:17:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:44106 - "GET /health HTTP/1.0" 200
2025-06-08 09:17:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52332 - "GET /health HTTP/1.1" 200
2025-06-08 09:17:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38788 - "GET /health HTTP/1.1" 200
2025-06-08 09:18:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:38106 - "GET /health HTTP/1.0" 200
2025-06-08 09:18:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39482 - "GET /health HTTP/1.1" 200
2025-06-08 09:18:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60682 - "GET /health HTTP/1.1" 200
2025-06-08 09:19:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:51942 - "GET /health HTTP/1.0" 200
2025-06-08 09:19:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59706 - "GET /health HTTP/1.1" 200
2025-06-08 09:19:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60298 - "GET /health HTTP/1.1" 200
2025-06-08 09:20:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:45118 - "GET /health HTTP/1.0" 200
2025-06-08 09:20:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51528 - "GET /health HTTP/1.1" 200
2025-06-08 09:20:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34454 - "GET /health HTTP/1.1" 200
2025-06-08 09:21:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:49216 - "GET /health HTTP/1.0" 200
2025-06-08 09:21:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56356 - "GET /health HTTP/1.1" 200
2025-06-08 09:21:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34168 - "GET /health HTTP/1.1" 200
2025-06-08 09:22:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:60594 - "GET /health HTTP/1.0" 200
2025-06-08 09:22:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47538 - "GET /health HTTP/1.1" 200
2025-06-08 09:22:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34656 - "GET /health HTTP/1.1" 200
2025-06-08 09:23:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:41744 - "GET /health HTTP/1.0" 200
2025-06-08 09:23:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37298 - "GET /health HTTP/1.1" 200
2025-06-08 09:23:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49278 - "GET /health HTTP/1.1" 200
2025-06-08 09:24:15 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:42796 - "GET /health HTTP/1.0" 200
2025-06-08 09:24:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48588 - "GET /health HTTP/1.1" 200
2025-06-08 09:24:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56862 - "GET /health HTTP/1.1" 200
2025-06-08 09:25:16 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:43034 - "GET /health HTTP/1.0" 200
2025-06-08 09:25:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33186 - "GET /health HTTP/1.1" 200
2025-06-08 09:25:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39078 - "GET /health HTTP/1.1" 200
2025-06-08 09:26:16 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:56060 - "GET /health HTTP/1.0" 200
2025-06-08 09:26:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47890 - "GET /health HTTP/1.1" 200
2025-06-08 09:26:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39022 - "GET /health HTTP/1.1" 200
2025-06-08 09:27:16 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:54786 - "GET /health HTTP/1.0" 200
2025-06-08 09:27:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44634 - "GET /health HTTP/1.1" 200
2025-06-08 09:27:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60456 - "GET /health HTTP/1.1" 200
2025-06-08 09:28:16 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:40490 - "GET /health HTTP/1.0" 200
2025-06-08 09:28:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44100 - "GET /health HTTP/1.1" 200
2025-06-08 09:28:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38266 - "GET /health HTTP/1.1" 200
2025-06-08 09:29:16 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:43398 - "GET /health HTTP/1.0" 200
2025-06-08 09:29:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53442 - "GET /health HTTP/1.1" 200
2025-06-08 09:29:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33128 - "GET /health HTTP/1.1" 200
2025-06-08 09:30:16 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:47346 - "GET /health HTTP/1.0" 200
2025-06-08 09:30:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55018 - "GET /health HTTP/1.1" 200
2025-06-08 09:30:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59156 - "GET /health HTTP/1.1" 200
2025-06-08 09:31:16 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:60522 - "GET /health HTTP/1.0" 200
2025-06-08 09:31:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52694 - "GET /health HTTP/1.1" 200
2025-06-08 09:31:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49964 - "GET /health HTTP/1.1" 200
2025-06-08 09:32:16 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:36518 - "GET /health HTTP/1.0" 200
2025-06-08 09:32:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53798 - "GET /health HTTP/1.1" 200
2025-06-08 09:32:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54952 - "GET /health HTTP/1.1" 200
2025-06-08 09:33:16 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:38444 - "GET /health HTTP/1.0" 200
2025-06-08 09:33:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55668 - "GET /health HTTP/1.1" 200
2025-06-08 09:33:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37354 - "GET /health HTTP/1.1" 200
2025-06-08 09:34:16 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:45272 - "GET /health HTTP/1.0" 200
2025-06-08 09:34:22 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57778 - "GET /health HTTP/1.1" 200
2025-06-08 09:34:53 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42876 - "GET /health HTTP/1.1" 200
2025-06-08 09:35:16 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:51290 - "GET /health HTTP/1.0" 200
2025-06-08 09:35:23 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37362 - "GET /health HTTP/1.1" 200
2025-06-08 09:35:53 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59856 - "GET /health HTTP/1.1" 200
2025-06-08 09:36:16 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:33262 - "GET /health HTTP/1.0" 200
2025-06-08 09:36:23 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41388 - "GET /health HTTP/1.1" 200
2025-06-08 09:36:53 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58864 - "GET /health HTTP/1.1" 200
2025-06-08 09:37:16 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:43870 - "GET /health HTTP/1.0" 200
2025-06-08 09:37:23 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51686 - "GET /health HTTP/1.1" 200
2025-06-08 09:37:53 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52654 - "GET /health HTTP/1.1" 200
2025-06-08 09:38:16 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:55620 - "GET /health HTTP/1.0" 200
2025-06-08 09:38:23 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40422 - "GET /health HTTP/1.1" 200
2025-06-08 09:38:53 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50064 - "GET /health HTTP/1.1" 200
2025-06-08 09:39:16 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:39240 - "GET /health HTTP/1.0" 200
2025-06-08 09:39:23 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54506 - "GET /health HTTP/1.1" 200
2025-06-08 09:39:53 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39024 - "GET /health HTTP/1.1" 200
2025-06-08 09:40:16 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:57086 - "GET /health HTTP/1.0" 200
2025-06-08 09:40:23 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53496 - "GET /health HTTP/1.1" 200
2025-06-08 09:40:53 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41346 - "GET /health HTTP/1.1" 200
2025-06-08 09:41:16 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:43600 - "GET /health HTTP/1.0" 200
2025-06-08 09:41:23 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41396 - "GET /health HTTP/1.1" 200
2025-06-08 09:41:53 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46706 - "GET /health HTTP/1.1" 200
2025-06-08 09:42:16 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:59344 - "GET /health HTTP/1.0" 200
2025-06-08 09:42:23 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38642 - "GET /health HTTP/1.1" 200
2025-06-08 09:42:53 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59368 - "GET /health HTTP/1.1" 200
2025-06-08 09:43:16 [INFO] uvicorn.access [httptools_impl.py:496] - 172.30.0.9:56434 - "GET /health HTTP/1.0" 200
2025-06-08 09:43:23 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44584 - "GET /health HTTP/1.1" 200
2025-06-08 09:43:27 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-08 09:43:27 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-08 09:43:27 [INFO] main [main.py:121] - CPU服务关闭
2025-06-08 09:43:27 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-08 09:43:27 [INFO] uvicorn.error [server.py:86] - Finished server process [8]
2025-06-08 09:43:28 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-08 09:43:28 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-08 09:43:28 [INFO] main [main.py:121] - CPU服务关闭
2025-06-08 09:43:28 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-08 09:43:28 [INFO] uvicorn.error [server.py:86] - Finished server process [9]
