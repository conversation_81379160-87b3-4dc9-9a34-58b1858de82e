2025-06-06 06:25:18 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 06:25:18 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 06:25:18 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 06:25:18 [INFO] uvicorn.error [server.py:76] - Started server process [11]
2025-06-06 06:25:18 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 06:25:18 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 06:25:18 [INFO] main [main.py:83] - 环境: production
2025-06-06 06:25:18 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 06:25:18 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 06:25:18 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 06:25:18 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'scipy'
2025-06-06 06:25:18 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 06:25:18 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 06:25:18 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 06:25:18 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 06:25:18 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 06:25:18 [INFO] uvicorn.error [server.py:76] - Started server process [12]
2025-06-06 06:25:18 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 06:25:18 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 06:25:18 [INFO] main [main.py:83] - 环境: production
2025-06-06 06:25:18 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 06:25:18 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 06:25:18 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 06:25:18 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'scipy'
2025-06-06 06:25:18 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 06:25:18 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 06:25:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50872 - "GET /health HTTP/1.1" 200
2025-06-06 06:25:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57542 - "GET /health HTTP/1.1" 200
2025-06-06 06:25:53 [INFO] uvicorn.access [httptools_impl.py:496] - **********:55134 - "GET /health HTTP/1.0" 200
2025-06-06 06:26:02 [INFO] uvicorn.access [httptools_impl.py:496] - **********:38862 - "POST /v2/simple_test/gpu_connection HTTP/1.1" 404
2025-06-06 06:26:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58156 - "GET /health HTTP/1.1" 200
2025-06-06 06:26:23 [INFO] uvicorn.access [httptools_impl.py:496] - **********:57888 - "GET /health HTTP/1.0" 200
2025-06-06 06:26:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50506 - "GET /health HTTP/1.1" 200
2025-06-06 06:27:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60644 - "GET /health HTTP/1.1" 200
2025-06-06 06:27:23 [INFO] uvicorn.access [httptools_impl.py:496] - **********:59884 - "GET /health HTTP/1.0" 200
2025-06-06 06:27:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52668 - "GET /health HTTP/1.1" 200
2025-06-06 06:28:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38318 - "GET /health HTTP/1.1" 200
2025-06-06 06:28:23 [INFO] uvicorn.access [httptools_impl.py:496] - **********:48222 - "GET /health HTTP/1.0" 200
2025-06-06 06:28:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47282 - "GET /health HTTP/1.1" 200
2025-06-06 06:29:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58142 - "GET /health HTTP/1.1" 200
2025-06-06 06:29:23 [INFO] uvicorn.access [httptools_impl.py:496] - **********:41548 - "GET /health HTTP/1.0" 200
2025-06-06 06:29:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51256 - "GET /health HTTP/1.1" 200
2025-06-06 06:30:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52608 - "GET /health HTTP/1.1" 200
2025-06-06 06:30:23 [INFO] uvicorn.access [httptools_impl.py:496] - **********:35132 - "GET /health HTTP/1.0" 200
2025-06-06 06:30:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45782 - "GET /health HTTP/1.1" 200
2025-06-06 06:31:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47694 - "GET /health HTTP/1.1" 200
2025-06-06 06:31:23 [INFO] uvicorn.access [httptools_impl.py:496] - **********:59738 - "GET /health HTTP/1.0" 200
2025-06-06 06:31:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49736 - "GET /health HTTP/1.1" 200
2025-06-06 06:32:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51682 - "GET /health HTTP/1.1" 200
2025-06-06 06:32:23 [INFO] uvicorn.access [httptools_impl.py:496] - **********:41418 - "GET /health HTTP/1.0" 200
2025-06-06 06:32:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60834 - "GET /health HTTP/1.1" 200
2025-06-06 06:33:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57226 - "GET /health HTTP/1.1" 200
2025-06-06 06:33:23 [INFO] uvicorn.access [httptools_impl.py:496] - **********:60924 - "GET /health HTTP/1.0" 200
2025-06-06 06:33:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54042 - "GET /health HTTP/1.1" 200
2025-06-06 06:34:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60368 - "GET /health HTTP/1.1" 200
2025-06-06 06:34:23 [INFO] uvicorn.access [httptools_impl.py:496] - **********:45700 - "GET /health HTTP/1.0" 200
2025-06-06 06:34:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42100 - "GET /health HTTP/1.1" 200
2025-06-06 06:35:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36892 - "GET /health HTTP/1.1" 200
2025-06-06 06:35:23 [INFO] uvicorn.access [httptools_impl.py:496] - **********:55882 - "GET /health HTTP/1.0" 200
2025-06-06 06:35:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42420 - "GET /health HTTP/1.1" 200
2025-06-06 06:36:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33174 - "GET /health HTTP/1.1" 200
2025-06-06 06:36:23 [INFO] uvicorn.access [httptools_impl.py:496] - **********:40874 - "GET /health HTTP/1.0" 200
2025-06-06 06:36:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55406 - "GET /health HTTP/1.1" 200
2025-06-06 06:37:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42594 - "GET /health HTTP/1.1" 200
2025-06-06 06:37:23 [INFO] uvicorn.access [httptools_impl.py:496] - **********:34868 - "GET /health HTTP/1.0" 200
2025-06-06 06:37:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41022 - "GET /health HTTP/1.1" 200
2025-06-06 06:38:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55606 - "GET /health HTTP/1.1" 200
2025-06-06 06:38:23 [INFO] uvicorn.access [httptools_impl.py:496] - **********:50878 - "GET /health HTTP/1.0" 200
2025-06-06 06:38:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60518 - "GET /health HTTP/1.1" 200
2025-06-06 06:39:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40162 - "GET /health HTTP/1.1" 200
2025-06-06 06:39:23 [INFO] uvicorn.access [httptools_impl.py:496] - **********:39108 - "GET /health HTTP/1.0" 200
2025-06-06 06:39:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56592 - "GET /health HTTP/1.1" 200
2025-06-06 06:40:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48306 - "GET /health HTTP/1.1" 200
2025-06-06 06:40:23 [INFO] uvicorn.access [httptools_impl.py:496] - **********:49846 - "GET /health HTTP/1.0" 200
2025-06-06 06:40:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40528 - "GET /health HTTP/1.1" 200
2025-06-06 06:41:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43676 - "GET /health HTTP/1.1" 200
2025-06-06 06:41:23 [INFO] uvicorn.access [httptools_impl.py:496] - **********:50834 - "GET /health HTTP/1.0" 200
2025-06-06 06:41:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46360 - "GET /health HTTP/1.1" 200
2025-06-06 06:42:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51576 - "GET /health HTTP/1.1" 200
2025-06-06 06:42:24 [INFO] uvicorn.access [httptools_impl.py:496] - **********:37502 - "GET /health HTTP/1.0" 200
2025-06-06 06:42:37 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53262 - "GET /v2/ HTTP/1.1" 200
2025-06-06 06:42:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52074 - "GET /v2/status HTTP/1.1" 404
2025-06-06 06:42:51 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52082 - "GET /health HTTP/1.1" 200
2025-06-06 06:46:09 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 06:46:09 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 06:46:09 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 06:46:09 [INFO] uvicorn.error [server.py:76] - Started server process [11]
2025-06-06 06:46:09 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 06:46:09 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 06:46:09 [INFO] main [main.py:83] - 环境: production
2025-06-06 06:46:09 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 06:46:09 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 06:46:09 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 06:46:09 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'scipy'
2025-06-06 06:46:09 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 06:46:09 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 06:46:09 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 06:46:09 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 06:46:09 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 06:46:09 [INFO] uvicorn.error [server.py:76] - Started server process [12]
2025-06-06 06:46:09 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 06:46:09 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 06:46:09 [INFO] main [main.py:83] - 环境: production
2025-06-06 06:46:09 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 06:46:09 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 06:46:09 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 06:46:09 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'scipy'
2025-06-06 06:46:09 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 06:46:09 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 06:46:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35588 - "GET /health HTTP/1.1" 200
2025-06-06 06:46:32 [INFO] uvicorn.access [httptools_impl.py:496] - **********:50954 - "GET /health HTTP/1.0" 200
2025-06-06 06:46:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50702 - "GET /health HTTP/1.1" 200
2025-06-06 06:46:59 [INFO] uvicorn.access [httptools_impl.py:496] - **********:35618 - "GET /v2/status HTTP/1.1" 404
2025-06-06 06:47:07 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57230 - "GET /v2/status HTTP/1.1" 404
2025-06-06 06:47:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57236 - "GET /health HTTP/1.1" 200
2025-06-06 06:47:27 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47146 - "GET / HTTP/1.1" 200
2025-06-06 06:47:32 [INFO] uvicorn.access [httptools_impl.py:496] - **********:34524 - "GET /health HTTP/1.0" 200
2025-06-06 06:47:38 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53472 - "GET /v2/ HTTP/1.1" 200
2025-06-06 06:47:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53484 - "GET /health HTTP/1.1" 200
2025-06-06 06:48:10 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48456 - "GET /docs HTTP/1.1" 200
2025-06-06 06:48:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48466 - "GET /health HTTP/1.1" 200
2025-06-06 06:48:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36792 - "GET /v2/retrieval/ HTTP/1.1" 404
2025-06-06 06:48:32 [INFO] uvicorn.access [httptools_impl.py:496] - **********:41332 - "GET /health HTTP/1.0" 200
2025-06-06 06:48:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58714 - "GET /health HTTP/1.1" 200
2025-06-06 06:49:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42666 - "GET /health HTTP/1.1" 200
2025-06-06 06:49:32 [INFO] uvicorn.access [httptools_impl.py:496] - **********:39048 - "GET /health HTTP/1.0" 200
2025-06-06 06:49:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36202 - "GET /health HTTP/1.1" 200
2025-06-06 06:50:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48124 - "GET /health HTTP/1.1" 200
2025-06-06 06:50:14 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 06:50:14 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 06:50:14 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 06:50:14 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 06:50:14 [INFO] uvicorn.error [server.py:86] - Finished server process [11]
2025-06-06 06:50:14 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 06:50:15 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 06:50:15 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 06:50:15 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 06:50:15 [INFO] uvicorn.error [server.py:86] - Finished server process [12]
2025-06-06 06:50:23 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 06:50:23 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 06:50:23 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 06:50:23 [INFO] uvicorn.error [server.py:76] - Started server process [12]
2025-06-06 06:50:23 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 06:50:23 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 06:50:23 [INFO] main [main.py:83] - 环境: production
2025-06-06 06:50:23 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 06:50:23 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 06:50:23 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 06:50:23 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'scipy'
2025-06-06 06:50:23 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 06:50:23 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 06:50:24 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 06:50:24 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 06:50:24 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 06:50:24 [INFO] uvicorn.error [server.py:76] - Started server process [11]
2025-06-06 06:50:24 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 06:50:24 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 06:50:24 [INFO] main [main.py:83] - 环境: production
2025-06-06 06:50:24 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 06:50:24 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 06:50:24 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 06:50:24 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'scipy'
2025-06-06 06:50:24 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 06:50:24 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 06:50:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49570 - "GET /health HTTP/1.1" 200
2025-06-06 06:50:32 [INFO] uvicorn.access [httptools_impl.py:496] - **********:35380 - "GET /health HTTP/1.0" 200
2025-06-06 06:50:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49178 - "GET /v2/status HTTP/1.1" 404
2025-06-06 06:50:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54510 - "GET /health HTTP/1.1" 200
2025-06-06 06:51:02 [INFO] uvicorn.access [httptools_impl.py:496] - **********:54402 - "GET /health HTTP/1.0" 200
2025-06-06 06:51:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55548 - "GET /health HTTP/1.1" 200
2025-06-06 06:51:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44960 - "GET /health HTTP/1.1" 200
2025-06-06 06:52:02 [INFO] uvicorn.access [httptools_impl.py:496] - **********:41486 - "GET /health HTTP/1.0" 200
2025-06-06 06:52:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49304 - "GET /health HTTP/1.1" 200
2025-06-06 06:52:52 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 06:52:52 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 06:52:52 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 06:52:52 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 06:52:52 [INFO] uvicorn.error [server.py:86] - Finished server process [11]
2025-06-06 06:52:52 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 06:52:52 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 06:52:52 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 06:52:52 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 06:52:52 [INFO] uvicorn.error [server.py:86] - Finished server process [12]
2025-06-06 06:53:00 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 06:53:00 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 06:53:00 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 06:53:00 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 06:53:00 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 06:53:00 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 06:53:00 [INFO] uvicorn.error [server.py:76] - Started server process [12]
2025-06-06 06:53:00 [INFO] uvicorn.error [server.py:76] - Started server process [11]
2025-06-06 06:53:00 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 06:53:00 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 06:53:00 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 06:53:00 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 06:53:00 [INFO] main [main.py:83] - 环境: production
2025-06-06 06:53:00 [INFO] main [main.py:83] - 环境: production
2025-06-06 06:53:00 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 06:53:00 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 06:53:00 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 06:53:00 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 06:53:00 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 06:53:00 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 06:53:00 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'scipy'
2025-06-06 06:53:00 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'scipy'
2025-06-06 06:53:00 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 06:53:00 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 06:53:00 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 06:53:00 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 06:53:02 [INFO] uvicorn.access [httptools_impl.py:496] - **********:46248 - "GET /health HTTP/1.0" 200
2025-06-06 06:53:03 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57890 - "GET /health HTTP/1.1" 200
2025-06-06 06:53:29 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33504 - "GET /v2/status HTTP/1.1" 404
2025-06-06 06:53:33 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:32848 - "GET /health HTTP/1.1" 200
2025-06-06 06:54:02 [INFO] uvicorn.access [httptools_impl.py:496] - **********:59308 - "GET /health HTTP/1.0" 200
2025-06-06 06:54:03 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59352 - "GET /health HTTP/1.1" 200
2025-06-06 06:54:33 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39076 - "GET /health HTTP/1.1" 200
2025-06-06 06:55:02 [INFO] uvicorn.access [httptools_impl.py:496] - **********:58012 - "GET /health HTTP/1.0" 200
2025-06-06 06:55:03 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57162 - "GET /health HTTP/1.1" 200
2025-06-06 06:55:33 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56122 - "GET /health HTTP/1.1" 200
2025-06-06 06:56:02 [INFO] uvicorn.access [httptools_impl.py:496] - **********:60832 - "GET /health HTTP/1.0" 200
2025-06-06 06:56:03 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51208 - "GET /health HTTP/1.1" 200
2025-06-06 06:56:33 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45258 - "GET /health HTTP/1.1" 200
2025-06-06 06:57:02 [INFO] uvicorn.access [httptools_impl.py:496] - **********:59346 - "GET /health HTTP/1.0" 200
2025-06-06 06:57:03 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52938 - "GET /health HTTP/1.1" 200
2025-06-06 06:57:33 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40928 - "GET /health HTTP/1.1" 200
2025-06-06 06:58:03 [INFO] uvicorn.access [httptools_impl.py:496] - **********:53948 - "GET /health HTTP/1.0" 200
2025-06-06 06:58:03 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51946 - "GET /health HTTP/1.1" 200
2025-06-06 06:58:33 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39070 - "GET /health HTTP/1.1" 200
2025-06-06 06:59:03 [INFO] uvicorn.access [httptools_impl.py:496] - **********:55380 - "GET /health HTTP/1.0" 200
2025-06-06 06:59:03 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52084 - "GET /health HTTP/1.1" 200
2025-06-06 06:59:33 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45598 - "GET /health HTTP/1.1" 200
2025-06-06 07:00:03 [INFO] uvicorn.access [httptools_impl.py:496] - **********:35786 - "GET /health HTTP/1.0" 200
2025-06-06 07:00:03 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41886 - "GET /health HTTP/1.1" 200
2025-06-06 07:00:34 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40714 - "GET /health HTTP/1.1" 200
2025-06-06 07:01:03 [INFO] uvicorn.access [httptools_impl.py:496] - **********:56150 - "GET /health HTTP/1.0" 200
2025-06-06 07:01:04 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49818 - "GET /health HTTP/1.1" 200
2025-06-06 07:01:34 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38018 - "GET /health HTTP/1.1" 200
2025-06-06 07:02:03 [INFO] uvicorn.access [httptools_impl.py:496] - **********:55486 - "GET /health HTTP/1.0" 200
2025-06-06 07:02:04 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44884 - "GET /health HTTP/1.1" 200
2025-06-06 07:02:34 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41416 - "GET /health HTTP/1.1" 200
2025-06-06 07:02:37 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 07:02:37 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 07:02:37 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 07:02:37 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 07:02:37 [INFO] uvicorn.error [server.py:86] - Finished server process [11]
2025-06-06 07:02:37 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 07:02:37 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 07:02:37 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 07:02:37 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 07:02:37 [INFO] uvicorn.error [server.py:86] - Finished server process [12]
2025-06-06 07:02:45 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 07:02:45 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 07:02:45 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 07:02:45 [INFO] uvicorn.error [server.py:76] - Started server process [11]
2025-06-06 07:02:45 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 07:02:45 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 07:02:45 [INFO] main [main.py:83] - 环境: production
2025-06-06 07:02:45 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 07:02:45 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 07:02:45 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 07:02:45 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 07:02:45 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'scipy'
2025-06-06 07:02:45 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 07:02:45 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 07:02:45 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 07:02:45 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 07:02:45 [INFO] uvicorn.error [server.py:76] - Started server process [12]
2025-06-06 07:02:45 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 07:02:45 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 07:02:45 [INFO] main [main.py:83] - 环境: production
2025-06-06 07:02:45 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 07:02:45 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 07:02:45 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 07:02:45 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'scipy'
2025-06-06 07:02:45 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 07:02:45 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 07:02:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50780 - "GET /health HTTP/1.1" 200
2025-06-06 07:03:03 [INFO] uvicorn.access [httptools_impl.py:496] - **********:35714 - "GET /health HTTP/1.0" 200
2025-06-06 07:03:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52128 - "GET /health HTTP/1.1" 200
2025-06-06 07:03:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54152 - "GET /health HTTP/1.1" 200
2025-06-06 07:04:03 [INFO] uvicorn.access [httptools_impl.py:496] - **********:49600 - "GET /health HTTP/1.0" 200
2025-06-06 07:04:19 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34082 - "GET /health HTTP/1.1" 200
2025-06-06 07:04:31 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 07:04:31 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 07:04:31 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 07:04:31 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 07:04:31 [INFO] uvicorn.error [server.py:86] - Finished server process [11]
2025-06-06 07:04:31 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 07:04:31 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 07:04:31 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 07:04:31 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 07:04:31 [INFO] uvicorn.error [server.py:86] - Finished server process [12]
2025-06-06 07:04:39 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 07:04:39 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 07:04:39 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 07:04:39 [INFO] uvicorn.error [server.py:76] - Started server process [11]
2025-06-06 07:04:39 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 07:04:39 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 07:04:39 [INFO] main [main.py:83] - 环境: production
2025-06-06 07:04:39 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 07:04:39 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 07:04:39 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 07:04:39 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'scipy'
2025-06-06 07:04:39 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 07:04:39 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 07:04:39 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 07:04:39 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 07:04:39 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 07:04:39 [INFO] uvicorn.error [server.py:76] - Started server process [12]
2025-06-06 07:04:39 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 07:04:39 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 07:04:39 [INFO] main [main.py:83] - 环境: production
2025-06-06 07:04:39 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 07:04:39 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 07:04:39 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 07:04:39 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'scipy'
2025-06-06 07:04:39 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 07:04:39 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 07:04:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45052 - "GET /health HTTP/1.1" 200
2025-06-06 07:05:04 [INFO] uvicorn.access [httptools_impl.py:496] - **********:46376 - "GET /health HTTP/1.0" 200
2025-06-06 07:05:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33016 - "GET /health HTTP/1.1" 200
2025-06-06 07:05:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48610 - "GET /health HTTP/1.1" 200
2025-06-06 07:06:04 [INFO] uvicorn.access [httptools_impl.py:496] - **********:38930 - "GET /health HTTP/1.0" 200
2025-06-06 07:06:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52366 - "GET /health HTTP/1.1" 200
2025-06-06 07:06:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35880 - "GET /health HTTP/1.1" 200
2025-06-06 07:07:04 [INFO] uvicorn.access [httptools_impl.py:496] - **********:46594 - "GET /health HTTP/1.0" 200
2025-06-06 07:07:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47666 - "GET /health HTTP/1.1" 200
2025-06-06 07:07:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44468 - "GET /health HTTP/1.1" 200
2025-06-06 07:08:04 [INFO] uvicorn.access [httptools_impl.py:496] - **********:48406 - "GET /health HTTP/1.0" 200
2025-06-06 07:08:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39186 - "GET /health HTTP/1.1" 200
2025-06-06 07:08:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41474 - "GET /health HTTP/1.1" 200
2025-06-06 07:09:04 [INFO] uvicorn.access [httptools_impl.py:496] - **********:44128 - "GET /health HTTP/1.0" 200
2025-06-06 07:09:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33258 - "GET /health HTTP/1.1" 200
2025-06-06 07:09:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38502 - "GET /health HTTP/1.1" 200
2025-06-06 07:10:04 [INFO] uvicorn.access [httptools_impl.py:496] - **********:39196 - "GET /health HTTP/1.0" 200
2025-06-06 07:10:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47456 - "GET /health HTTP/1.1" 200
2025-06-06 07:10:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39406 - "GET /health HTTP/1.1" 200
2025-06-06 07:11:04 [INFO] uvicorn.access [httptools_impl.py:496] - **********:40312 - "GET /health HTTP/1.0" 200
2025-06-06 07:11:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36170 - "GET /health HTTP/1.1" 200
2025-06-06 07:11:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42128 - "GET /health HTTP/1.1" 200
2025-06-06 07:11:54 [INFO] uvicorn.access [httptools_impl.py:496] - **********:32966 - "GET /docs HTTP/1.1" 200
2025-06-06 07:12:04 [INFO] uvicorn.access [httptools_impl.py:496] - **********:34524 - "GET /health HTTP/1.0" 200
2025-06-06 07:12:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54412 - "GET /health HTTP/1.1" 200
2025-06-06 07:12:25 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 07:12:25 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 07:12:25 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 07:12:25 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 07:12:25 [INFO] uvicorn.error [server.py:86] - Finished server process [11]
2025-06-06 07:12:26 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 07:12:26 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 07:12:26 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 07:12:26 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 07:12:26 [INFO] uvicorn.error [server.py:86] - Finished server process [12]
2025-06-06 07:12:37 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 07:12:37 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 07:12:37 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 07:12:37 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 07:12:37 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 07:12:37 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 07:12:37 [INFO] uvicorn.error [server.py:76] - Started server process [15]
2025-06-06 07:12:37 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 07:12:37 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 07:12:37 [INFO] uvicorn.error [server.py:76] - Started server process [14]
2025-06-06 07:12:37 [INFO] main [main.py:83] - 环境: production
2025-06-06 07:12:37 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 07:12:37 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 07:12:37 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 07:12:37 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 07:12:37 [INFO] main [main.py:83] - 环境: production
2025-06-06 07:12:37 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 07:12:37 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 07:12:37 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-06 07:12:37 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 07:12:37 [INFO] cores.retrieval_service [retrieval_service.py:625] - 创建检索服务实例
2025-06-06 07:12:37 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 07:12:37 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-06 07:12:37 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-06 07:12:37 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-06 07:12:37 [INFO] cores.retrieval_service [retrieval_service.py:625] - 创建检索服务实例
2025-06-06 07:12:37 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:12:37 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-06 07:12:37 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 07:12:37 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-06 07:12:37 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:12:37 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:12:37 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 07:12:37 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 07:12:37 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:12:37 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:12:37 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 07:12:37 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:12:37 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '27%', 'memory_utilization': '15%', 'temperature': '51°C', 'power_usage': '9.4W'}}
2025-06-06 07:12:37 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:12:37 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:12:37 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '27%', 'memory_utilization': '15%', 'temperature': '51°C', 'power_usage': '9.4W'}}
2025-06-06 07:12:37 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:12:37 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:12:38 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:12:38 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:12:38 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:12:38 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '20%', 'memory_utilization': '12%', 'temperature': '51°C', 'power_usage': '9.1W'}}
2025-06-06 07:12:38 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:12:38 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:12:48 [ERROR] shared.clients.milvus_client [milvus_client.py:219] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-06 07:12:48 [ERROR] shared.clients.milvus_client [milvus_client.py:219] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-06 07:12:48 [WARNING] shared.clients.milvus_client [milvus_client.py:223] - 尝试连接本地Milvus作为回退...
2025-06-06 07:12:48 [WARNING] shared.clients.milvus_client [milvus_client.py:223] - 尝试连接本地Milvus作为回退...
2025-06-06 07:12:49 [ERROR] shared.clients.milvus_client [milvus_client.py:219] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-06 07:12:49 [WARNING] shared.clients.milvus_client [milvus_client.py:223] - 尝试连接本地Milvus作为回退...
2025-06-06 07:12:53 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:12:53 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:12:53 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:12:53 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '33%', 'memory_utilization': '34%', 'temperature': '50°C', 'power_usage': '7.5W'}}
2025-06-06 07:12:53 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:12:53 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:12:58 [WARNING] shared.clients.milvus_client [milvus_client.py:235] - 本地Milvus连接也失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-06 07:12:58 [WARNING] shared.clients.milvus_client [milvus_client.py:235] - 本地Milvus连接也失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-06 07:12:58 [WARNING] shared.clients.milvus_client [milvus_client.py:238] - Milvus连接失败，将在离线模式下运行
2025-06-06 07:12:58 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:12:58 [WARNING] shared.clients.milvus_client [milvus_client.py:238] - Milvus连接失败，将在离线模式下运行
2025-06-06 07:12:58 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:12:58 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:12:58 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:12:58 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:12:58 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-06 07:12:58 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:12:58 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-06 07:12:59 [WARNING] shared.clients.milvus_client [milvus_client.py:235] - 本地Milvus连接也失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-06 07:12:59 [WARNING] shared.clients.milvus_client [milvus_client.py:238] - Milvus连接失败，将在离线模式下运行
2025-06-06 07:12:59 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:12:59 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:12:59 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:12:59 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46542 - "GET /health HTTP/1.1" 200
2025-06-06 07:13:04 [ERROR] shared.clients.milvus_client [milvus_client.py:219] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-06 07:13:04 [WARNING] shared.clients.milvus_client [milvus_client.py:223] - 尝试连接本地Milvus作为回退...
2025-06-06 07:13:04 [INFO] uvicorn.access [httptools_impl.py:496] - **********:49138 - "GET /health HTTP/1.0" 200
2025-06-06 07:13:14 [WARNING] shared.clients.milvus_client [milvus_client.py:235] - 本地Milvus连接也失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-06 07:13:14 [WARNING] shared.clients.milvus_client [milvus_client.py:238] - Milvus连接失败，将在离线模式下运行
2025-06-06 07:13:14 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:13:14 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:13:14 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:13:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58846 - "GET /health HTTP/1.1" 200
2025-06-06 07:13:33 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44998 - "GET /health HTTP/1.1" 200
2025-06-06 07:13:52 [WARNING] api.simple_routes [simple_routes.py:54] - GPU服务连接检查失败: HTTPConnectionPool(host='***********', port=8001): Max retries exceeded with url: /health (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x7f062c70ce50>, 'Connection to *********** timed out. (connect timeout=5)'))
2025-06-06 07:13:52 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50778 - "GET /v2/status HTTP/1.1" 200
2025-06-06 07:14:03 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40132 - "GET /health HTTP/1.1" 200
2025-06-06 07:14:07 [WARNING] api.simple_routes [simple_routes.py:54] - GPU服务连接检查失败: HTTPConnectionPool(host='***********', port=8001): Max retries exceeded with url: /health (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x7fa6315e8a90>, 'Connection to *********** timed out. (connect timeout=5)'))
2025-06-06 07:14:07 [INFO] uvicorn.access [httptools_impl.py:496] - **********:34460 - "GET /v2/status HTTP/1.1" 200
2025-06-06 07:14:34 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36930 - "GET /health HTTP/1.1" 200
2025-06-06 07:14:34 [INFO] uvicorn.access [httptools_impl.py:496] - **********:45970 - "GET /health HTTP/1.0" 200
2025-06-06 07:15:04 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55082 - "GET /health HTTP/1.1" 200
2025-06-06 07:15:34 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40564 - "GET /health HTTP/1.1" 200
2025-06-06 07:15:34 [INFO] uvicorn.access [httptools_impl.py:496] - **********:40170 - "GET /health HTTP/1.0" 200
2025-06-06 07:16:04 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53002 - "GET /health HTTP/1.1" 200
2025-06-06 07:16:04 [INFO] uvicorn.access [httptools_impl.py:496] - **********:46650 - "GET /health HTTP/1.0" 200
2025-06-06 07:16:34 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39924 - "GET /health HTTP/1.1" 200
2025-06-06 07:17:04 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35478 - "GET /health HTTP/1.1" 200
2025-06-06 07:17:05 [INFO] uvicorn.access [httptools_impl.py:496] - **********:38134 - "GET /health HTTP/1.0" 200
2025-06-06 07:17:34 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58576 - "GET /health HTTP/1.1" 200
2025-06-06 07:18:04 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41682 - "GET /health HTTP/1.1" 200
2025-06-06 07:18:05 [INFO] uvicorn.access [httptools_impl.py:496] - **********:50112 - "GET /health HTTP/1.0" 200
2025-06-06 07:18:11 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 07:18:11 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 07:18:11 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 07:18:11 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 07:18:11 [INFO] uvicorn.error [server.py:86] - Finished server process [14]
2025-06-06 07:18:11 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 07:18:11 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 07:18:11 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 07:18:11 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 07:18:11 [INFO] uvicorn.error [server.py:86] - Finished server process [15]
2025-06-06 07:18:22 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 07:18:22 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 07:18:22 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 07:18:22 [INFO] uvicorn.error [server.py:76] - Started server process [15]
2025-06-06 07:18:22 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 07:18:22 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 07:18:22 [INFO] main [main.py:83] - 环境: production
2025-06-06 07:18:22 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 07:18:22 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 07:18:22 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 07:18:22 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-06 07:18:22 [INFO] cores.retrieval_service [retrieval_service.py:625] - 创建检索服务实例
2025-06-06 07:18:22 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-06 07:18:22 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-06 07:18:22 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:18:22 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 07:18:22 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:18:22 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 07:18:22 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:18:22 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '0%', 'memory_utilization': '22%', 'temperature': '49°C', 'power_usage': '5.7W'}}
2025-06-06 07:18:22 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:18:22 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:18:22 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 07:18:22 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 07:18:22 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 07:18:22 [INFO] uvicorn.error [server.py:76] - Started server process [14]
2025-06-06 07:18:22 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 07:18:22 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 07:18:22 [INFO] main [main.py:83] - 环境: production
2025-06-06 07:18:22 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 07:18:22 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 07:18:22 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 07:18:22 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-06 07:18:22 [INFO] cores.retrieval_service [retrieval_service.py:625] - 创建检索服务实例
2025-06-06 07:18:22 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-06 07:18:22 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-06 07:18:22 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:18:22 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 07:18:22 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:18:22 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 07:18:22 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:18:22 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '0%', 'memory_utilization': '29%', 'temperature': '49°C', 'power_usage': '5.7W'}}
2025-06-06 07:18:22 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:18:22 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:18:24 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:18:24 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:18:24 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:18:24 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '0%', 'memory_utilization': '19%', 'temperature': '49°C', 'power_usage': '4.7W'}}
2025-06-06 07:18:24 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:18:24 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:18:32 [ERROR] shared.clients.milvus_client [milvus_client.py:219] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-06 07:18:32 [WARNING] shared.clients.milvus_client [milvus_client.py:223] - 尝试连接本地Milvus作为回退...
2025-06-06 07:18:32 [ERROR] shared.clients.milvus_client [milvus_client.py:219] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-06 07:18:32 [WARNING] shared.clients.milvus_client [milvus_client.py:223] - 尝试连接本地Milvus作为回退...
2025-06-06 07:18:34 [ERROR] shared.clients.milvus_client [milvus_client.py:219] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-06 07:18:34 [WARNING] shared.clients.milvus_client [milvus_client.py:223] - 尝试连接本地Milvus作为回退...
2025-06-06 07:18:35 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:18:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:18:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:18:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '0%', 'memory_utilization': '6%', 'temperature': '47°C', 'power_usage': '6.1W'}}
2025-06-06 07:18:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:18:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:18:42 [WARNING] shared.clients.milvus_client [milvus_client.py:235] - 本地Milvus连接也失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-06 07:18:42 [WARNING] shared.clients.milvus_client [milvus_client.py:238] - Milvus连接失败，将在离线模式下运行
2025-06-06 07:18:42 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:18:42 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:18:42 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:18:42 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-06 07:18:42 [WARNING] shared.clients.milvus_client [milvus_client.py:235] - 本地Milvus连接也失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-06 07:18:42 [WARNING] shared.clients.milvus_client [milvus_client.py:238] - Milvus连接失败，将在离线模式下运行
2025-06-06 07:18:42 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:18:42 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:18:42 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:18:42 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-06 07:18:44 [WARNING] shared.clients.milvus_client [milvus_client.py:235] - 本地Milvus连接也失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-06 07:18:44 [WARNING] shared.clients.milvus_client [milvus_client.py:238] - Milvus连接失败，将在离线模式下运行
2025-06-06 07:18:44 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:18:44 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:18:44 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:18:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59326 - "GET /health HTTP/1.1" 200
2025-06-06 07:18:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37084 - "GET /health HTTP/1.1" 200
2025-06-06 07:18:45 [ERROR] shared.clients.milvus_client [milvus_client.py:219] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-06 07:18:45 [WARNING] shared.clients.milvus_client [milvus_client.py:223] - 尝试连接本地Milvus作为回退...
2025-06-06 07:18:55 [WARNING] shared.clients.milvus_client [milvus_client.py:235] - 本地Milvus连接也失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-06 07:18:55 [WARNING] shared.clients.milvus_client [milvus_client.py:238] - Milvus连接失败，将在离线模式下运行
2025-06-06 07:18:55 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:18:55 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:18:55 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:18:55 [INFO] uvicorn.access [httptools_impl.py:496] - **********:57456 - "GET /health HTTP/1.0" 200
2025-06-06 07:19:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59654 - "GET /health HTTP/1.1" 200
2025-06-06 07:19:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40954 - "GET /health HTTP/1.1" 200
2025-06-06 07:19:45 [INFO] uvicorn.access [httptools_impl.py:496] - **********:56660 - "GET /health HTTP/1.0" 200
2025-06-06 07:20:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45964 - "GET /health HTTP/1.1" 200
2025-06-06 07:20:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56070 - "GET /health HTTP/1.1" 200
2025-06-06 07:20:45 [INFO] uvicorn.access [httptools_impl.py:496] - **********:58830 - "GET /health HTTP/1.0" 200
2025-06-06 07:21:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54174 - "GET /health HTTP/1.1" 200
2025-06-06 07:21:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43708 - "GET /health HTTP/1.1" 200
2025-06-06 07:21:45 [INFO] uvicorn.access [httptools_impl.py:496] - **********:36784 - "GET /health HTTP/1.0" 200
2025-06-06 07:22:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59872 - "GET /health HTTP/1.1" 200
2025-06-06 07:22:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59300 - "GET /health HTTP/1.1" 200
2025-06-06 07:22:45 [INFO] uvicorn.access [httptools_impl.py:496] - **********:35180 - "GET /health HTTP/1.0" 200
2025-06-06 07:23:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54384 - "GET /health HTTP/1.1" 200
2025-06-06 07:23:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39968 - "GET /health HTTP/1.1" 200
2025-06-06 07:23:45 [INFO] uvicorn.access [httptools_impl.py:496] - **********:52640 - "GET /health HTTP/1.0" 200
2025-06-06 07:24:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51042 - "GET /health HTTP/1.1" 200
2025-06-06 07:24:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57984 - "GET /health HTTP/1.1" 200
2025-06-06 07:24:45 [INFO] uvicorn.access [httptools_impl.py:496] - **********:35538 - "GET /health HTTP/1.0" 200
2025-06-06 07:25:09 [INFO] uvicorn.access [httptools_impl.py:496] - **********:41876 - "GET /v2/simple_test/system_info HTTP/1.1" 200
2025-06-06 07:25:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35252 - "GET /health HTTP/1.1" 200
2025-06-06 07:25:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56444 - "GET /health HTTP/1.1" 200
2025-06-06 07:25:45 [INFO] uvicorn.access [httptools_impl.py:496] - **********:44130 - "GET /health HTTP/1.0" 200
2025-06-06 07:26:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46746 - "GET /health HTTP/1.1" 200
2025-06-06 07:26:16 [INFO] uvicorn.access [httptools_impl.py:496] - **********:57730 - "GET / HTTP/1.1" 200
2025-06-06 07:26:21 [INFO] uvicorn.access [httptools_impl.py:496] - **********:57730 - "GET /openapi.json HTTP/1.1" 200
2025-06-06 07:26:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60650 - "GET /health HTTP/1.1" 200
2025-06-06 07:27:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33558 - "GET /health HTTP/1.1" 200
2025-06-06 07:27:15 [INFO] uvicorn.access [httptools_impl.py:496] - **********:57354 - "GET /health HTTP/1.0" 200
2025-06-06 07:27:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53986 - "GET /health HTTP/1.1" 200
2025-06-06 07:27:45 [INFO] uvicorn.access [httptools_impl.py:496] - **********:34776 - "GET /health HTTP/1.0" 200
2025-06-06 07:28:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42432 - "GET /health HTTP/1.1" 200
2025-06-06 07:28:15 [INFO] uvicorn.access [httptools_impl.py:496] - **********:40582 - "GET /health HTTP/1.0" 200
2025-06-06 07:28:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45440 - "GET /health HTTP/1.1" 200
2025-06-06 07:29:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54938 - "GET /health HTTP/1.1" 200
2025-06-06 07:29:15 [INFO] uvicorn.access [httptools_impl.py:496] - **********:33354 - "GET /health HTTP/1.0" 200
2025-06-06 07:29:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39374 - "GET /health HTTP/1.1" 200
2025-06-06 07:30:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55322 - "GET /health HTTP/1.1" 200
2025-06-06 07:30:15 [INFO] uvicorn.access [httptools_impl.py:496] - **********:54728 - "GET /health HTTP/1.0" 200
2025-06-06 07:30:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47470 - "GET /health HTTP/1.1" 200
2025-06-06 07:31:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38850 - "GET /health HTTP/1.1" 200
2025-06-06 07:31:16 [INFO] uvicorn.access [httptools_impl.py:496] - **********:55504 - "GET /health HTTP/1.0" 200
2025-06-06 07:31:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40042 - "GET /health HTTP/1.1" 200
2025-06-06 07:31:46 [INFO] uvicorn.access [httptools_impl.py:496] - **********:52688 - "GET /health HTTP/1.0" 200
2025-06-06 07:32:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57292 - "GET /health HTTP/1.1" 200
2025-06-06 07:32:16 [INFO] uvicorn.access [httptools_impl.py:496] - **********:38164 - "GET /health HTTP/1.0" 200
2025-06-06 07:32:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35806 - "GET /health HTTP/1.1" 200
2025-06-06 07:33:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33458 - "GET /health HTTP/1.1" 200
2025-06-06 07:33:16 [INFO] uvicorn.access [httptools_impl.py:496] - **********:46020 - "GET /health HTTP/1.0" 200
2025-06-06 07:33:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42954 - "GET /health HTTP/1.1" 200
2025-06-06 07:34:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37322 - "GET /health HTTP/1.1" 200
2025-06-06 07:34:16 [INFO] uvicorn.access [httptools_impl.py:496] - **********:57340 - "GET /health HTTP/1.0" 200
2025-06-06 07:34:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48784 - "GET /health HTTP/1.1" 200
2025-06-06 07:35:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40966 - "GET /health HTTP/1.1" 200
2025-06-06 07:35:16 [INFO] uvicorn.access [httptools_impl.py:496] - **********:36910 - "GET /health HTTP/1.0" 200
2025-06-06 07:35:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55976 - "GET /health HTTP/1.1" 200
2025-06-06 07:36:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50114 - "GET /health HTTP/1.1" 200
2025-06-06 07:36:16 [INFO] uvicorn.access [httptools_impl.py:496] - **********:37690 - "GET /health HTTP/1.0" 200
2025-06-06 07:36:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51402 - "GET /health HTTP/1.1" 200
2025-06-06 07:37:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43244 - "GET /health HTTP/1.1" 200
2025-06-06 07:37:16 [INFO] uvicorn.access [httptools_impl.py:496] - **********:38238 - "GET /health HTTP/1.0" 200
2025-06-06 07:37:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37308 - "GET /health HTTP/1.1" 200
2025-06-06 07:38:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33328 - "GET /health HTTP/1.1" 200
2025-06-06 07:38:16 [INFO] uvicorn.access [httptools_impl.py:496] - **********:36386 - "GET /health HTTP/1.0" 200
2025-06-06 07:38:36 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 07:38:36 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 07:38:36 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 07:38:36 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 07:38:36 [INFO] uvicorn.error [server.py:86] - Finished server process [14]
2025-06-06 07:38:37 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 07:38:37 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 07:38:37 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 07:38:37 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 07:38:37 [INFO] uvicorn.error [server.py:86] - Finished server process [15]
2025-06-06 07:38:49 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 07:38:49 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 07:38:49 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 07:38:49 [INFO] uvicorn.error [server.py:76] - Started server process [14]
2025-06-06 07:38:49 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 07:38:49 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 07:38:49 [INFO] main [main.py:83] - 环境: production
2025-06-06 07:38:49 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 07:38:49 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 07:38:49 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 07:38:49 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-06 07:38:49 [INFO] cores.retrieval_service [retrieval_service.py:625] - 创建检索服务实例
2025-06-06 07:38:49 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-06 07:38:49 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-06 07:38:49 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:38:49 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 07:38:49 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:38:49 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 07:38:49 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:38:49 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '40%', 'memory_utilization': '10%', 'temperature': '51°C', 'power_usage': '8.9W'}}
2025-06-06 07:38:49 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:38:49 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:38:49 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 07:38:49 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 07:38:49 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 07:38:49 [INFO] shared.clients.milvus_client [milvus_client.py:216] - 成功连接到Milvus: http://************:19530
2025-06-06 07:38:49 [INFO] uvicorn.error [server.py:76] - Started server process [15]
2025-06-06 07:38:49 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:38:49 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 07:38:49 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:38:49 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:38:49 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 07:38:49 [INFO] main [main.py:83] - 环境: production
2025-06-06 07:38:49 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-06 07:38:49 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 07:38:49 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 07:38:49 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 07:38:49 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-06 07:38:49 [INFO] cores.retrieval_service [retrieval_service.py:625] - 创建检索服务实例
2025-06-06 07:38:49 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-06 07:38:49 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-06 07:38:49 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:38:49 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 07:38:49 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:38:49 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 07:38:49 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:38:49 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '27%', 'memory_utilization': '10%', 'temperature': '51°C', 'power_usage': '9.0W'}}
2025-06-06 07:38:49 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:38:49 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:38:49 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:38:49 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:38:49 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:38:49 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '24%', 'memory_utilization': '11%', 'temperature': '51°C', 'power_usage': '8.9W'}}
2025-06-06 07:38:49 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:38:49 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:38:49 [INFO] shared.clients.milvus_client [milvus_client.py:216] - 成功连接到Milvus: http://************:19530
2025-06-06 07:38:49 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:38:49 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:38:49 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:38:49 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37542 - "GET /health HTTP/1.1" 200
2025-06-06 07:38:49 [INFO] shared.clients.milvus_client [milvus_client.py:216] - 成功连接到Milvus: http://************:19530
2025-06-06 07:38:49 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:38:49 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:38:49 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:38:49 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-06 07:39:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38012 - "GET /health HTTP/1.1" 200
2025-06-06 07:39:46 [INFO] uvicorn.access [httptools_impl.py:496] - **********:34698 - "GET /health HTTP/1.0" 200
2025-06-06 07:39:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48130 - "GET /health HTTP/1.1" 200
2025-06-06 07:40:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47944 - "GET /health HTTP/1.1" 200
2025-06-06 07:40:46 [INFO] uvicorn.access [httptools_impl.py:496] - **********:53380 - "GET /health HTTP/1.0" 200
2025-06-06 07:40:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59630 - "GET /health HTTP/1.1" 200
2025-06-06 07:41:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50136 - "GET /health HTTP/1.1" 200
2025-06-06 07:41:46 [INFO] uvicorn.access [httptools_impl.py:496] - **********:37088 - "GET /health HTTP/1.0" 200
2025-06-06 07:41:47 [INFO] uvicorn.access [httptools_impl.py:496] - **********:37092 - "GET /docs HTTP/1.1" 200
2025-06-06 07:41:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40658 - "GET /health HTTP/1.1" 200
2025-06-06 07:42:16 [INFO] uvicorn.access [httptools_impl.py:496] - **********:35576 - "GET /health HTTP/1.0" 200
2025-06-06 07:42:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54894 - "GET /health HTTP/1.1" 200
2025-06-06 07:42:50 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34998 - "GET /health HTTP/1.1" 200
2025-06-06 07:43:16 [INFO] uvicorn.access [httptools_impl.py:496] - **********:44670 - "GET /health HTTP/1.0" 200
2025-06-06 07:43:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58114 - "GET /health HTTP/1.1" 200
2025-06-06 07:43:45 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 07:43:45 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 07:43:45 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 07:43:45 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 07:43:45 [INFO] uvicorn.error [server.py:86] - Finished server process [14]
2025-06-06 07:43:46 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 07:43:46 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 07:43:46 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 07:43:46 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 07:43:46 [INFO] uvicorn.error [server.py:86] - Finished server process [15]
2025-06-06 07:43:57 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 07:43:57 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 07:43:57 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 07:43:57 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 07:43:57 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 07:43:57 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 07:43:57 [INFO] uvicorn.error [server.py:76] - Started server process [14]
2025-06-06 07:43:57 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 07:43:57 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 07:43:57 [INFO] main [main.py:83] - 环境: production
2025-06-06 07:43:57 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 07:43:57 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 07:43:57 [INFO] uvicorn.error [server.py:76] - Started server process [15]
2025-06-06 07:43:57 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 07:43:57 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 07:43:57 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-06 07:43:57 [INFO] cores.retrieval_service [retrieval_service.py:625] - 创建检索服务实例
2025-06-06 07:43:57 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 07:43:57 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-06 07:43:57 [INFO] main [main.py:83] - 环境: production
2025-06-06 07:43:57 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-06 07:43:57 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 07:43:57 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:43:57 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 07:43:57 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 07:43:57 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 07:43:57 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:43:57 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-06 07:43:57 [INFO] cores.retrieval_service [retrieval_service.py:625] - 创建检索服务实例
2025-06-06 07:43:57 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 07:43:57 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-06 07:43:57 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:43:57 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-06 07:43:57 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:43:57 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 07:43:57 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:43:57 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 07:43:57 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:43:57 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '21%', 'memory_utilization': '14%', 'temperature': '51°C', 'power_usage': '9.3W'}}
2025-06-06 07:43:57 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:43:57 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:43:57 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '21%', 'memory_utilization': '14%', 'temperature': '51°C', 'power_usage': '9.3W'}}
2025-06-06 07:43:57 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:43:57 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:43:57 [INFO] shared.clients.milvus_client [milvus_client.py:216] - 成功连接到Milvus: http://************:19530
2025-06-06 07:43:57 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:43:57 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:43:57 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:43:57 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-06 07:43:57 [INFO] shared.clients.milvus_client [milvus_client.py:216] - 成功连接到Milvus: http://************:19530
2025-06-06 07:43:57 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:43:57 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:43:57 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:43:57 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-06 07:43:57 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59292 - "GET /health HTTP/1.1" 200
2025-06-06 07:44:16 [INFO] uvicorn.access [httptools_impl.py:496] - **********:49866 - "GET /health HTTP/1.0" 200
2025-06-06 07:44:27 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48000 - "GET /health HTTP/1.1" 200
2025-06-06 07:44:58 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53242 - "GET /health HTTP/1.1" 200
2025-06-06 07:45:16 [INFO] uvicorn.access [httptools_impl.py:496] - **********:56666 - "GET /health HTTP/1.0" 200
2025-06-06 07:45:28 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42240 - "GET /health HTTP/1.1" 200
2025-06-06 07:45:58 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41410 - "GET /health HTTP/1.1" 200
2025-06-06 07:46:17 [INFO] uvicorn.access [httptools_impl.py:496] - **********:34874 - "GET /health HTTP/1.0" 200
2025-06-06 07:46:28 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37336 - "GET /health HTTP/1.1" 200
2025-06-06 07:46:58 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48416 - "GET /health HTTP/1.1" 200
2025-06-06 07:47:04 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 07:47:04 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 07:47:04 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 07:47:04 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 07:47:04 [INFO] uvicorn.error [server.py:86] - Finished server process [14]
2025-06-06 07:47:05 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 07:47:05 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 07:47:05 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 07:47:05 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 07:47:05 [INFO] uvicorn.error [server.py:86] - Finished server process [15]
2025-06-06 07:47:16 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 07:47:16 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 07:47:16 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 07:47:16 [INFO] uvicorn.error [server.py:76] - Started server process [20]
2025-06-06 07:47:16 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 07:47:16 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 07:47:16 [INFO] main [main.py:83] - 环境: production
2025-06-06 07:47:16 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 07:47:16 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 07:47:16 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 07:47:16 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-06 07:47:16 [INFO] cores.retrieval_service [retrieval_service.py:625] - 创建检索服务实例
2025-06-06 07:47:16 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-06 07:47:16 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-06 07:47:16 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:47:16 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 07:47:16 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:47:16 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 07:47:16 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:47:16 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '49%', 'memory_utilization': '31%', 'temperature': '51°C', 'power_usage': '8.2W'}}
2025-06-06 07:47:16 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:47:16 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:47:16 [INFO] shared.clients.milvus_client [milvus_client.py:216] - 成功连接到Milvus: http://************:19530
2025-06-06 07:47:16 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:47:16 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:47:16 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:47:16 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-06 07:47:16 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 07:47:16 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 07:47:16 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 07:47:16 [INFO] uvicorn.error [server.py:76] - Started server process [21]
2025-06-06 07:47:16 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 07:47:16 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 07:47:16 [INFO] main [main.py:83] - 环境: production
2025-06-06 07:47:16 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 07:47:16 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 07:47:16 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 07:47:16 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-06 07:47:16 [INFO] cores.retrieval_service [retrieval_service.py:625] - 创建检索服务实例
2025-06-06 07:47:16 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-06 07:47:16 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-06 07:47:16 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:47:16 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 07:47:16 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:47:16 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 07:47:16 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:47:16 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '28%', 'memory_utilization': '12%', 'temperature': '51°C', 'power_usage': '8.3W'}}
2025-06-06 07:47:16 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:47:16 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:47:16 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:47:16 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:47:16 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:47:16 [INFO] shared.clients.milvus_client [milvus_client.py:216] - 成功连接到Milvus: http://************:19530
2025-06-06 07:47:16 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:47:16 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:47:16 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '28%', 'memory_utilization': '12%', 'temperature': '51°C', 'power_usage': '8.3W'}}
2025-06-06 07:47:16 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:47:16 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:47:16 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-06 07:47:16 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:47:16 [INFO] shared.clients.milvus_client [milvus_client.py:216] - 成功连接到Milvus: http://************:19530
2025-06-06 07:47:16 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:47:16 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:47:16 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:47:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52046 - "GET /health HTTP/1.1" 200
2025-06-06 07:47:17 [INFO] uvicorn.access [httptools_impl.py:496] - **********:56702 - "GET /health HTTP/1.0" 200
2025-06-06 07:47:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34084 - "GET /health HTTP/1.1" 200
2025-06-06 07:48:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59604 - "GET /health HTTP/1.1" 200
2025-06-06 07:48:17 [INFO] uvicorn.access [httptools_impl.py:496] - **********:34216 - "GET /health HTTP/1.0" 200
2025-06-06 07:48:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40116 - "GET /health HTTP/1.1" 200
2025-06-06 07:49:12 [INFO] cores.indexing_service [indexing_service.py:55] - 优化版索引服务初始化完成（GPU+CPU分离架构）
2025-06-06 07:49:12 [INFO] cores.indexing_service [indexing_service.py:570] - 创建索引服务实例
2025-06-06 07:49:12 [INFO] cores.indexing_service [indexing_service.py:446] - 开始索引功能测试...
2025-06-06 07:49:12 [INFO] cores.indexing_service [indexing_service.py:449] - 测试嵌入模型初始化...
2025-06-06 07:49:12 [INFO] shared.clients.embedding_client [embedding_client.py:61] - 初始化自定义API嵌入模型: BAAI/bge-m3
2025-06-06 07:49:12 [INFO] cores.indexing_service [indexing_service.py:451] - ✅ 嵌入模型初始化成功: BAAI/bge-m3
2025-06-06 07:49:12 [INFO] cores.indexing_service [indexing_service.py:454] - 测试GPU稀疏嵌入...
2025-06-06 07:49:12 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:49:12 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:49:12 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '32%', 'memory_utilization': '10%', 'temperature': '50°C', 'power_usage': '9.2W'}}
2025-06-06 07:49:12 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:49:12 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:49:12 [INFO] cores.indexing_service [indexing_service.py:458] - ✅ GPU稀疏嵌入测试成功: 1 个结果
2025-06-06 07:49:12 [INFO] cores.indexing_service [indexing_service.py:461] - 测试密集嵌入...
2025-06-06 07:49:12 [INFO] cores.indexing_service [indexing_service.py:463] - ✅ 密集嵌入测试成功: 维度 1024
2025-06-06 07:49:12 [INFO] cores.indexing_service [indexing_service.py:466] - 创建测试文档...
2025-06-06 07:49:12 [INFO] cores.indexing_service [indexing_service.py:477] - ✅ 测试文档创建成功: 2 个文档
2025-06-06 07:49:12 [INFO] cores.indexing_service [indexing_service.py:480] - 测试文档解析...
2025-06-06 07:49:12 [INFO] cores.indexing_service [indexing_service.py:482] - ✅ 文档解析成功: 2 个节点
2025-06-06 07:49:12 [INFO] cores.indexing_service [indexing_service.py:485] - 测试索引构建...
2025-06-06 07:49:12 [ERROR] cores.indexing_service [indexing_service.py:233] - 索引追加构建失败: test_indexing_collection, 错误: 'test_indexing_collection', 耗时: 0.000秒
2025-06-06 07:49:12 [ERROR] cores.indexing_service [indexing_service.py:526] - 索引构建测试失败: 'test_indexing_collection'
2025-06-06 07:49:12 [INFO] uvicorn.access [httptools_impl.py:496] - **********:47764 - "POST /v2/indexing/test HTTP/1.1" 200
2025-06-06 07:49:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42382 - "GET /health HTTP/1.1" 200
2025-06-06 07:49:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51034 - "GET /health HTTP/1.1" 200
2025-06-06 07:49:47 [INFO] uvicorn.access [httptools_impl.py:496] - **********:38222 - "GET /health HTTP/1.0" 200
2025-06-06 07:49:53 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 07:49:53 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 07:49:53 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 07:49:53 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 07:49:53 [INFO] uvicorn.error [server.py:86] - Finished server process [20]
2025-06-06 07:49:53 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 07:49:53 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 07:49:53 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 07:49:53 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 07:49:53 [INFO] uvicorn.error [server.py:86] - Finished server process [21]
2025-06-06 07:50:04 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 07:50:04 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 07:50:04 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 07:50:04 [INFO] uvicorn.error [server.py:76] - Started server process [14]
2025-06-06 07:50:04 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 07:50:04 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 07:50:04 [INFO] main [main.py:83] - 环境: production
2025-06-06 07:50:04 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 07:50:04 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 07:50:04 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 07:50:04 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-06 07:50:04 [INFO] cores.retrieval_service [retrieval_service.py:625] - 创建检索服务实例
2025-06-06 07:50:04 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-06 07:50:04 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-06 07:50:04 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:50:04 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 07:50:04 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:50:04 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 07:50:04 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:50:04 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '36%', 'memory_utilization': '30%', 'temperature': '52°C', 'power_usage': '7.5W'}}
2025-06-06 07:50:04 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:50:04 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:50:04 [INFO] shared.clients.milvus_client [milvus_client.py:216] - 成功连接到Milvus: http://************:19530
2025-06-06 07:50:04 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:50:04 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:50:04 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:50:04 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-06 07:50:05 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 07:50:05 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 07:50:05 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 07:50:05 [INFO] uvicorn.error [server.py:76] - Started server process [15]
2025-06-06 07:50:05 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 07:50:05 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 07:50:05 [INFO] main [main.py:83] - 环境: production
2025-06-06 07:50:05 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 07:50:05 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 07:50:05 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 07:50:05 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-06 07:50:05 [INFO] cores.retrieval_service [retrieval_service.py:625] - 创建检索服务实例
2025-06-06 07:50:05 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-06 07:50:05 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-06 07:50:05 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:50:05 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 07:50:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:50:05 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 07:50:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:50:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '34%', 'memory_utilization': '30%', 'temperature': '52°C', 'power_usage': '7.5W'}}
2025-06-06 07:50:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:50:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:50:05 [INFO] shared.clients.milvus_client [milvus_client.py:216] - 成功连接到Milvus: http://************:19530
2025-06-06 07:50:05 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:50:05 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:50:05 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:50:05 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-06 07:50:05 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43826 - "GET /health HTTP/1.1" 200
2025-06-06 07:50:35 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52982 - "GET /health HTTP/1.1" 200
2025-06-06 07:50:47 [INFO] uvicorn.access [httptools_impl.py:496] - **********:40874 - "GET /health HTTP/1.0" 200
2025-06-06 07:51:05 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56978 - "GET /health HTTP/1.1" 200
2025-06-06 07:51:35 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51122 - "GET /health HTTP/1.1" 200
2025-06-06 07:51:47 [INFO] uvicorn.access [httptools_impl.py:496] - **********:48638 - "GET /health HTTP/1.0" 200
2025-06-06 07:52:05 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53964 - "GET /health HTTP/1.1" 200
2025-06-06 07:52:35 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43664 - "GET /health HTTP/1.1" 200
2025-06-06 07:52:47 [INFO] uvicorn.access [httptools_impl.py:496] - **********:36592 - "GET /health HTTP/1.0" 200
2025-06-06 07:53:05 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45862 - "GET /health HTTP/1.1" 200
2025-06-06 07:53:32 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 07:53:32 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 07:53:32 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 07:53:32 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 07:53:32 [INFO] uvicorn.error [server.py:86] - Finished server process [14]
2025-06-06 07:53:33 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 07:53:33 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 07:53:33 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 07:53:33 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 07:53:33 [INFO] uvicorn.error [server.py:86] - Finished server process [15]
2025-06-06 07:53:42 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 07:53:42 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 07:53:42 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 07:53:42 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 07:53:42 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 07:53:42 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 07:53:42 [INFO] uvicorn.error [server.py:76] - Started server process [14]
2025-06-06 07:53:42 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 07:53:42 [INFO] uvicorn.error [server.py:76] - Started server process [15]
2025-06-06 07:53:42 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 07:53:42 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 07:53:42 [INFO] main [main.py:83] - 环境: production
2025-06-06 07:53:42 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 07:53:42 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 07:53:42 [INFO] main [main.py:83] - 环境: production
2025-06-06 07:53:42 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 07:53:42 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 07:53:42 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 07:53:42 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 07:53:42 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-06 07:53:42 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 07:53:42 [INFO] cores.retrieval_service [retrieval_service.py:625] - 创建检索服务实例
2025-06-06 07:53:42 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-06 07:53:42 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-06 07:53:42 [INFO] cores.retrieval_service [retrieval_service.py:625] - 创建检索服务实例
2025-06-06 07:53:42 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-06 07:53:42 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-06 07:53:42 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:53:42 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 07:53:42 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-06 07:53:42 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:53:42 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:53:42 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 07:53:42 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 07:53:42 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:53:42 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:53:42 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 07:53:42 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:53:42 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '0%', 'memory_utilization': '0%', 'temperature': '47°C', 'power_usage': '4.3W'}}
2025-06-06 07:53:42 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:53:42 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:53:42 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '0%', 'memory_utilization': '0%', 'temperature': '47°C', 'power_usage': '4.3W'}}
2025-06-06 07:53:42 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:53:42 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:53:42 [INFO] shared.clients.milvus_client [milvus_client.py:216] - 成功连接到Milvus: http://************:19530
2025-06-06 07:53:42 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:53:42 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:53:42 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:53:42 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-06 07:53:42 [INFO] shared.clients.milvus_client [milvus_client.py:216] - 成功连接到Milvus: http://************:19530
2025-06-06 07:53:42 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:53:42 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:53:42 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:53:42 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-06 07:53:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36760 - "GET /health HTTP/1.1" 200
2025-06-06 07:53:47 [INFO] uvicorn.access [httptools_impl.py:496] - **********:42400 - "GET /health HTTP/1.0" 200
2025-06-06 07:54:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55306 - "GET /health HTTP/1.1" 200
2025-06-06 07:54:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57210 - "GET /health HTTP/1.1" 200
2025-06-06 07:54:47 [INFO] uvicorn.access [httptools_impl.py:496] - **********:59672 - "GET /health HTTP/1.0" 200
2025-06-06 07:55:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43986 - "GET /health HTTP/1.1" 200
2025-06-06 07:55:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37874 - "GET /health HTTP/1.1" 200
2025-06-06 07:55:47 [INFO] uvicorn.access [httptools_impl.py:496] - **********:40924 - "GET /health HTTP/1.0" 200
2025-06-06 07:56:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42574 - "GET /health HTTP/1.1" 200
2025-06-06 07:56:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46552 - "GET /health HTTP/1.1" 200
2025-06-06 07:56:45 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 07:56:45 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 07:56:45 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 07:56:45 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 07:56:45 [INFO] uvicorn.error [server.py:86] - Finished server process [14]
2025-06-06 07:56:46 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 07:56:46 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 07:56:46 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 07:56:46 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 07:56:46 [INFO] uvicorn.error [server.py:86] - Finished server process [15]
2025-06-06 07:56:55 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 07:56:55 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 07:56:55 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 07:56:55 [INFO] uvicorn.error [server.py:76] - Started server process [15]
2025-06-06 07:56:55 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 07:56:55 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 07:56:55 [INFO] main [main.py:83] - 环境: production
2025-06-06 07:56:55 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 07:56:55 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 07:56:55 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 07:56:55 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-06 07:56:55 [INFO] cores.retrieval_service [retrieval_service.py:625] - 创建检索服务实例
2025-06-06 07:56:55 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-06 07:56:55 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-06 07:56:55 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 07:56:55 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:56:55 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 07:56:55 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:56:55 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:56:55 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 07:56:55 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 07:56:55 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 07:56:55 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '0%', 'memory_utilization': '0%', 'temperature': '45°C', 'power_usage': '2.9W'}}
2025-06-06 07:56:55 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:56:55 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:56:55 [INFO] uvicorn.error [server.py:76] - Started server process [14]
2025-06-06 07:56:55 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 07:56:55 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 07:56:55 [INFO] main [main.py:83] - 环境: production
2025-06-06 07:56:55 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 07:56:55 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 07:56:55 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 07:56:55 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-06 07:56:55 [INFO] cores.retrieval_service [retrieval_service.py:625] - 创建检索服务实例
2025-06-06 07:56:55 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-06 07:56:55 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-06 07:56:55 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 07:56:55 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 07:56:55 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 07:56:55 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 07:56:55 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 07:56:55 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '0%', 'memory_utilization': '0%', 'temperature': '45°C', 'power_usage': '2.9W'}}
2025-06-06 07:56:55 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 07:56:55 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 07:56:55 [INFO] shared.clients.milvus_client [milvus_client.py:216] - 成功连接到Milvus: http://************:19530
2025-06-06 07:56:55 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:56:55 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:56:55 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:56:55 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-06 07:56:55 [INFO] shared.clients.milvus_client [milvus_client.py:216] - 成功连接到Milvus: http://************:19530
2025-06-06 07:56:55 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 07:56:55 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 07:56:55 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 07:56:55 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-06 07:56:57 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47724 - "GET /health HTTP/1.1" 200
2025-06-06 07:57:27 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59268 - "GET /health HTTP/1.1" 200
2025-06-06 07:57:48 [INFO] uvicorn.access [httptools_impl.py:496] - **********:54204 - "GET /health HTTP/1.0" 200
2025-06-06 07:57:58 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39650 - "GET /health HTTP/1.1" 200
2025-06-06 07:58:28 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41970 - "GET /health HTTP/1.1" 200
2025-06-06 07:58:48 [INFO] uvicorn.access [httptools_impl.py:496] - **********:56352 - "GET /health HTTP/1.0" 200
2025-06-06 07:58:58 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37782 - "GET /health HTTP/1.1" 200
2025-06-06 07:59:28 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54944 - "GET /health HTTP/1.1" 200
2025-06-06 07:59:48 [INFO] uvicorn.access [httptools_impl.py:496] - **********:38998 - "GET /health HTTP/1.0" 200
2025-06-06 07:59:58 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49138 - "GET /health HTTP/1.1" 200
2025-06-06 08:00:28 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59564 - "GET /health HTTP/1.1" 200
2025-06-06 08:00:49 [INFO] uvicorn.access [httptools_impl.py:496] - **********:39734 - "GET /health HTTP/1.0" 200
2025-06-06 08:00:58 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52398 - "GET /health HTTP/1.1" 200
2025-06-06 08:01:28 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51814 - "GET /health HTTP/1.1" 200
2025-06-06 08:01:49 [INFO] uvicorn.access [httptools_impl.py:496] - **********:60630 - "GET /health HTTP/1.0" 200
2025-06-06 08:01:58 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52712 - "GET /health HTTP/1.1" 200
2025-06-06 08:02:28 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51192 - "GET /health HTTP/1.1" 200
2025-06-06 08:02:49 [INFO] uvicorn.access [httptools_impl.py:496] - **********:45462 - "GET /health HTTP/1.0" 200
2025-06-06 08:02:58 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47330 - "GET /health HTTP/1.1" 200
2025-06-06 08:03:28 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33332 - "GET /health HTTP/1.1" 200
2025-06-06 08:03:49 [INFO] uvicorn.access [httptools_impl.py:496] - **********:32814 - "GET /health HTTP/1.0" 200
2025-06-06 08:03:58 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57884 - "GET /health HTTP/1.1" 200
2025-06-06 08:04:28 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37224 - "GET /health HTTP/1.1" 200
2025-06-06 08:04:49 [INFO] uvicorn.access [httptools_impl.py:496] - **********:42502 - "GET /health HTTP/1.0" 200
2025-06-06 08:04:58 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50294 - "GET /health HTTP/1.1" 200
2025-06-06 08:05:28 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50044 - "GET /health HTTP/1.1" 200
2025-06-06 08:05:49 [INFO] uvicorn.access [httptools_impl.py:496] - **********:42712 - "GET /health HTTP/1.0" 200
2025-06-06 08:05:53 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 08:05:53 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 08:05:53 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 08:05:53 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 08:05:53 [INFO] uvicorn.error [server.py:86] - Finished server process [14]
2025-06-06 08:05:53 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 08:05:54 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 08:05:54 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 08:05:54 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 08:05:54 [INFO] uvicorn.error [server.py:86] - Finished server process [15]
2025-06-06 08:06:05 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 08:06:05 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 08:06:05 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 08:06:05 [INFO] uvicorn.error [server.py:76] - Started server process [21]
2025-06-06 08:06:05 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 08:06:05 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 08:06:05 [INFO] main [main.py:83] - 环境: production
2025-06-06 08:06:05 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 08:06:05 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 08:06:05 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 08:06:05 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-06 08:06:05 [INFO] cores.retrieval_service [retrieval_service.py:625] - 创建检索服务实例
2025-06-06 08:06:05 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-06 08:06:05 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-06 08:06:05 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 08:06:05 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 08:06:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 08:06:05 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 08:06:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 08:06:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '34%', 'memory_utilization': '31%', 'temperature': '50°C', 'power_usage': '7.5W'}}
2025-06-06 08:06:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 08:06:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 08:06:05 [INFO] shared.clients.milvus_client [milvus_client.py:216] - 成功连接到Milvus: http://************:19530
2025-06-06 08:06:05 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 08:06:05 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 08:06:05 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 08:06:05 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-06 08:06:05 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 08:06:05 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 08:06:05 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 08:06:05 [INFO] uvicorn.error [server.py:76] - Started server process [20]
2025-06-06 08:06:05 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 08:06:05 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 08:06:05 [INFO] main [main.py:83] - 环境: production
2025-06-06 08:06:05 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 08:06:05 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 08:06:05 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 08:06:05 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-06 08:06:05 [INFO] cores.retrieval_service [retrieval_service.py:625] - 创建检索服务实例
2025-06-06 08:06:05 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-06 08:06:05 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-06 08:06:05 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 08:06:05 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 08:06:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 08:06:05 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 08:06:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 08:06:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '35%', 'memory_utilization': '31%', 'temperature': '50°C', 'power_usage': '7.5W'}}
2025-06-06 08:06:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 08:06:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 08:06:05 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 08:06:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 08:06:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 08:06:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '35%', 'memory_utilization': '31%', 'temperature': '50°C', 'power_usage': '7.5W'}}
2025-06-06 08:06:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 08:06:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 08:06:05 [INFO] shared.clients.milvus_client [milvus_client.py:216] - 成功连接到Milvus: http://************:19530
2025-06-06 08:06:05 [INFO] shared.clients.milvus_client [milvus_client.py:216] - 成功连接到Milvus: http://************:19530
2025-06-06 08:06:05 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 08:06:05 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 08:06:05 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 08:06:05 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 08:06:05 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 08:06:05 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 08:06:05 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-06 08:06:05 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60426 - "GET /health HTTP/1.1" 200
2025-06-06 08:06:35 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51876 - "GET /health HTTP/1.1" 200
2025-06-06 08:06:49 [INFO] uvicorn.access [httptools_impl.py:496] - **********:34788 - "GET /health HTTP/1.0" 200
2025-06-06 08:07:05 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45776 - "GET /health HTTP/1.1" 200
2025-06-06 08:07:35 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52962 - "GET /health HTTP/1.1" 200
2025-06-06 08:07:49 [INFO] uvicorn.access [httptools_impl.py:496] - **********:34502 - "GET /health HTTP/1.0" 200
2025-06-06 08:08:05 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39418 - "GET /health HTTP/1.1" 200
2025-06-06 08:08:35 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34078 - "GET /health HTTP/1.1" 200
2025-06-06 08:08:49 [INFO] uvicorn.access [httptools_impl.py:496] - **********:44536 - "GET /health HTTP/1.0" 200
2025-06-06 08:09:05 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43878 - "GET /health HTTP/1.1" 200
2025-06-06 08:09:35 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59088 - "GET /health HTTP/1.1" 200
2025-06-06 08:09:49 [INFO] uvicorn.access [httptools_impl.py:496] - **********:57544 - "GET /health HTTP/1.0" 200
2025-06-06 08:10:05 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39964 - "GET /health HTTP/1.1" 200
2025-06-06 08:10:36 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40024 - "GET /health HTTP/1.1" 200
2025-06-06 08:10:49 [INFO] uvicorn.access [httptools_impl.py:496] - **********:53384 - "GET /health HTTP/1.0" 200
2025-06-06 08:11:06 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55292 - "GET /health HTTP/1.1" 200
2025-06-06 08:11:12 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 08:11:12 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 08:11:12 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 08:11:12 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 08:11:12 [INFO] uvicorn.error [server.py:86] - Finished server process [20]
2025-06-06 08:11:12 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 08:11:12 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 08:11:12 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 08:11:12 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 08:11:12 [INFO] uvicorn.error [server.py:86] - Finished server process [21]
2025-06-06 08:11:24 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 08:11:24 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 08:11:24 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 08:11:24 [INFO] uvicorn.error [server.py:76] - Started server process [14]
2025-06-06 08:11:24 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 08:11:24 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 08:11:24 [INFO] main [main.py:83] - 环境: production
2025-06-06 08:11:24 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 08:11:24 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 08:11:24 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 08:11:24 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-06 08:11:24 [INFO] cores.retrieval_service [retrieval_service.py:625] - 创建检索服务实例
2025-06-06 08:11:24 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-06 08:11:24 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-06 08:11:24 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 08:11:24 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 08:11:24 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 08:11:24 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 08:11:24 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 08:11:24 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '33%', 'memory_utilization': '32%', 'temperature': '50°C', 'power_usage': '7.5W'}}
2025-06-06 08:11:24 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 08:11:24 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 08:11:24 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 08:11:24 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 08:11:24 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 08:11:24 [INFO] uvicorn.error [server.py:76] - Started server process [15]
2025-06-06 08:11:24 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 08:11:24 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 08:11:24 [INFO] main [main.py:83] - 环境: production
2025-06-06 08:11:24 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 08:11:24 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 08:11:24 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 08:11:24 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-06 08:11:24 [INFO] cores.retrieval_service [retrieval_service.py:625] - 创建检索服务实例
2025-06-06 08:11:24 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-06 08:11:24 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-06 08:11:24 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-06 08:11:24 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 08:11:24 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-06 08:11:24 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 08:11:24 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-06 08:11:24 [INFO] shared.clients.milvus_client [milvus_client.py:216] - 成功连接到Milvus: http://************:19530
2025-06-06 08:11:24 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 08:11:24 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 08:11:24 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 08:11:24 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:79] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '33%', 'memory_utilization': '32%', 'temperature': '50°C', 'power_usage': '7.5W'}}
2025-06-06 08:11:24 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-06 08:11:24 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-06 08:11:24 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-06 08:11:24 [INFO] shared.clients.milvus_client [milvus_client.py:216] - 成功连接到Milvus: http://************:19530
2025-06-06 08:11:24 [INFO] shared.clients.milvus_client [milvus_client.py:187] - Milvus客户端初始化完成: http://************:19530
2025-06-06 08:11:24 [INFO] shared.clients.milvus_client [milvus_client.py:721] - 创建Milvus客户端实例
2025-06-06 08:11:24 [INFO] cores.retrieval_service [retrieval_service.py:75] - Milvus客户端初始化成功
2025-06-06 08:11:24 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-06 08:11:24 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38744 - "GET /health HTTP/1.1" 200
2025-06-06 08:11:49 [INFO] uvicorn.access [httptools_impl.py:496] - **********:53482 - "GET /health HTTP/1.0" 200
2025-06-06 08:11:54 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40054 - "GET /health HTTP/1.1" 200
2025-06-06 08:12:24 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39316 - "GET /health HTTP/1.1" 200
2025-06-06 08:12:47 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 08:12:47 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 08:12:47 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 08:12:47 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 08:12:47 [INFO] uvicorn.error [server.py:86] - Finished server process [14]
2025-06-06 08:12:48 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 08:12:48 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 08:12:48 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 08:12:48 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 08:12:48 [INFO] uvicorn.error [server.py:86] - Finished server process [15]
