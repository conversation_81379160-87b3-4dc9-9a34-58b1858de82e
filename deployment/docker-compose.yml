# AI接诉即办助手 v3.0 生产环境部署配置
# GPU+CPU分离架构，支持CPU服务副本和负载均衡

version: '3.8'

services:
  # GPU服务（单实例，绑定到GPU）
  gpu-service:
    image: ai-v3-gpu-service:prod
    container_name: ai-v3-gpu-service-prod
    restart: unless-stopped
    ports:
      - "8001:8001"
    volumes:
      - ai-models-cache:/app/models
      - ./logs/gpu:/app/logs
    environment:
      - ENVIRONMENT=production
      - GPU_SERVICE_HOST=0.0.0.0
      - GPU_SERVICE_PORT=8001
      - BGE_M3_MODEL_PATH=/app/models/hub/bge-m3
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - ai-network

  # CPU服务副本1
  cpu-service-1:
    image: ai-v3-cpu-service:prod
    container_name: ai-v3-cpu-service-1
    restart: unless-stopped
    expose:
      - "8000"
    volumes:
      - ./logs/cpu-1:/app/logs
    env_file:
      - ../configs/.env.prod
    depends_on:
      gpu-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - ai-network

  # CPU服务副本2
  cpu-service-2:
    image: ai-v3-cpu-service:prod
    container_name: ai-v3-cpu-service-2
    restart: unless-stopped
    expose:
      - "8000"
    volumes:
      - ./logs/cpu-2:/app/logs
    env_file:
      - ../configs/.env.prod
    depends_on:
      gpu-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - ai-network

  # etcd服务（Milvus依赖）
  etcd:
    image: quay.io/coreos/etcd:v3.5.5
    container_name: ai-v3-etcd
    restart: unless-stopped
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=**********
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - etcd-data:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    healthcheck:
      test: ["CMD", "etcdctl", "endpoint", "health"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - ai-network

  # MinIO对象存储（Milvus依赖）
  minio:
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    container_name: ai-v3-minio
    restart: unless-stopped
    ports:
      - "9001:9001"
      - "9000:9000"
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    volumes:
      - minio-data:/minio_data
    command: minio server /minio_data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - ai-network

  # Milvus向量数据库
  milvus-standalone:
    image: milvusdb/milvus:v2.3.3
    container_name: ai-v3-milvus-standalone
    restart: unless-stopped
    ports:
      - "19530:19530"
      - "9091:9091"
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:9000
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    volumes:
      - milvus-data:/var/lib/milvus
    depends_on:
      - etcd
      - minio
    networks:
      - ai-network
    command: ["milvus", "run", "standalone"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: ai-v3-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - ai-network

  # Nginx负载均衡器
  nginx:
    image: nginx:alpine
    container_name: ai-v3-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./logs/nginx:/var/log/nginx
      - ./ssl:/etc/nginx/ssl:ro  # SSL证书目录
    depends_on:
      - cpu-service-1
      - cpu-service-2
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - ai-network

  # 监控服务（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: ai-v3-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ai-network
    profiles:
      - monitoring

  # Grafana仪表板（可选）
  grafana:
    image: grafana/grafana:latest
    container_name: ai-v3-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - ai-network
    profiles:
      - monitoring

# 网络配置
networks:
  ai-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  ai-models-cache:
    external: true
  etcd-data:
  minio-data:
  milvus-data:
  redis-data:
  prometheus-data:
  grafana-data:
