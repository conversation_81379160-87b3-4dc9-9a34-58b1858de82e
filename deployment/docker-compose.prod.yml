# AI接诉即办助手 v3.0 生产环境部署配置
# GPU+CPU分离架构，支持CPU服务副本和负载均衡


services:
  # GPU服务（单实例，绑定到GPU）
  gpu-service:
    image: ai-v3-gpu-service:prod
    container_name: ai-v3-gpu-service-prod
    restart: unless-stopped
    ports:
      - "8001:8001"
    volumes:
      - ai-models-cache:/app/models
      - ./logs/gpu:/app/logs
    environment:
      - ENVIRONMENT=production
      - GPU_SERVICE_HOST=0.0.0.0
      - GPU_SERVICE_PORT=8001
      - BGE_M3_MODEL_PATH=/app/models/hub/bge-m3
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - ai-network

  # CPU服务副本1
  cpu-service-1:
    image: ai-v3-cpu-service:prod
    container_name: ai-v3-cpu-service-1
    restart: unless-stopped
    expose:
      - "8000"
    volumes:
      - ./logs/cpu-1:/app/logs
    environment:
      - ENVIRONMENT=production
      - CPU_SERVICE_HOST=0.0.0.0
      - CPU_SERVICE_PORT=8000
      - CPU_SERVICE_WORKERS=2
      - GPU_SERVICE_URL=http://gpu-service:8001
    depends_on:
      gpu-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - ai-network

  # CPU服务副本2
  cpu-service-2:
    image: ai-v3-cpu-service:prod
    container_name: ai-v3-cpu-service-2
    restart: unless-stopped
    expose:
      - "8000"
    volumes:
      - ./logs/cpu-2:/app/logs
    environment:
      - ENVIRONMENT=production
      - CPU_SERVICE_HOST=0.0.0.0
      - CPU_SERVICE_PORT=8000
      - CPU_SERVICE_WORKERS=2
      - GPU_SERVICE_URL=http://gpu-service:8001
    depends_on:
      gpu-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - ai-network


  # Nginx负载均衡器
  nginx:
    image: nginx:alpine
    container_name: ai-v3-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./logs/nginx:/var/log/nginx
      - ./ssl:/etc/nginx/ssl:ro  # SSL证书目录
    depends_on:
      - cpu-service-1
      - cpu-service-2
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - ai-network

  # 监控服务（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: ai-v3-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ai-network
    profiles:
      - monitoring

  # Grafana仪表板（可选）
  grafana:
    image: grafana/grafana:latest
    container_name: ai-v3-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - ai-network
    profiles:
      - monitoring

# 网络配置
networks:
  ai-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  ai-models-cache:
    external: true
  prometheus-data:
  grafana-data:
