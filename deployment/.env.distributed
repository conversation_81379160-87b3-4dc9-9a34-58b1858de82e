# AI接诉即办助手 v3.0 - 分布式部署配置
# 跨服务器部署专用配置文件

# ================================
# 基础配置
# ================================
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# ================================
# API嵌入配置（密集嵌入）
# ================================
API_EMBED_MODEL=BAAI/bge-m3
API_EMBED_BATCH_SIZE=8

# ================================
# BGE-M3模型配置（稀疏嵌入）
# ================================
BGE_M3_MODEL_PATH=BAAI/bge-m3

# ================================
# GPU服务配置（GPU服务器）
# ================================
GPU_SERVICE_HOST=0.0.0.0
GPU_SERVICE_PORT=8001
GPU_SERVICE_WORKERS=1
# 跨服务器部署：指向GPU服务器的实际IP
GPU_SERVICE_URL=http://*************:8001

# ================================
# CPU服务配置（CPU服务器）
# ================================
CPU_SERVICE_HOST=0.0.0.0
CPU_SERVICE_PORT=8000
CPU_SERVICE_WORKERS=4

# ================================
# 数据库配置（数据库服务器）
# ================================
MILVUS_URI=http://*************:19530
MILVUS_DIMENSION=1024

# ================================
# Redis配置（缓存服务器）
# ================================
REDIS_URL=redis://*************:6379/0

# ================================
# MinIO配置（存储服务器）
# ================================
MINIO_ENDPOINT=*************:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=ai-jiesujiban

# ================================
# 检索配置
# ================================
RETRIEVAL_TOP_K_DEPARTMENT=3
RETRIEVAL_TOP_K_GUIDELINE=3
RETRIEVAL_TOP_K_HISTORICAL=5
RETRIEVAL_TOP_K_DELEGATED=3

# ================================
# 安全配置
# ================================
ADMIN_API_KEY=sk-prod-secure-key-change-me

# ================================
# CORS配置
# ================================
CORS_ORIGINS=https://your-domain.com
CORS_METHODS=GET,POST,PUT,DELETE
CORS_HEADERS=*

# ================================
# 性能配置（生产环境优化）
# ================================
REQUEST_TIMEOUT=300
GPU_REQUEST_TIMEOUT=120
MAX_CONCURRENT_REQUESTS=100

# ================================
# 监控配置
# ================================
ENABLE_PERFORMANCE_LOGGING=true
PERFORMANCE_LOG_LEVEL=INFO
HEALTH_CHECK_INTERVAL=30

# ================================
# 网络配置
# ================================
# GPU服务器网络配置
GPU_SERVER_IP=*************
GPU_SERVER_PORT=8001

# CPU服务器网络配置
CPU_SERVER_IP=*************
CPU_SERVER_PORT=8000

# 数据库服务器网络配置
DB_SERVER_IP=*************
MILVUS_PORT=19530

# 缓存服务器网络配置
CACHE_SERVER_IP=*************
REDIS_PORT=6379

# 存储服务器网络配置
STORAGE_SERVER_IP=*************
MINIO_PORT=9000
