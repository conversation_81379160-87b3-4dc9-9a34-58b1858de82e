# AI接诉即办助手 v3.0 - GPU+CPU分离架构

[![Docker](https://img.shields.io/badge/Docker-Ready-blue)](https://www.docker.com/)
[![Python](https://img.shields.io/badge/Python-3.11+-green)](https://www.python.org/)
[![CUDA](https://img.shields.io/badge/CUDA-12.8+-orange)](https://developer.nvidia.com/cuda-downloads)
[![License](https://img.shields.io/badge/License-MIT-yellow)](LICENSE)

## 🚀 项目概述

AI接诉即办助手v3.0采用**专业的GPU+CPU分离架构**，实现了模型服务与业务服务的完全分离，通过HTTP协议进行服务间通信，显著提升了系统性能和资源利用率。

### 🏗️ 核心架构

```
                    ┌─────────────────────────────────────┐
                    │         负载均衡层 (Nginx)          │
                    │    http://localhost:80              │
                    └─────────────────┬───────────────────┘
                                      │
    ┌─────────────────────────────────▼─────────────────────────────────┐
    │                        CPU服务集群                                │
    │  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐      │
    │  │  CPU服务副本1   │ │  CPU服务副本2   │ │  CPU服务副本3   │      │
    │  │  :8000         │ │  :8000         │ │  :8000         │      │
    │  └─────────────────┘ └─────────────────┘ └─────────────────┘      │
    │  ├─ FastAPI主服务                                                 │
    │  ├─ 业务逻辑处理                                                  │
    │  ├─ 密集嵌入: API调用                                             │
    │  └─ 稀疏嵌入: HTTP调用GPU服务                                     │
    └─────────────────────────────────┬───────────────────────────────────┘
                                      │ HTTP: http://gpu-service:8001
    ┌─────────────────────────────────▼─────────────────────────────────┐
    │                        GPU服务 (单实例)                           │
    │  ┌─────────────────────────────────────────────────────────────┐  │
    │  │              GPU服务 :8001                                 │  │
    │  └─────────────────────────────────────────────────────────────┘  │
    │  ├─ BGE-M3稀疏嵌入服务                                            │
    │  ├─ GPU资源专用管理                                               │
    │  ├─ 高性能模型推理                                                │
    │  └─ 模型缓存优化                                                  │
    └───────────────────────────────────────────────────────────────────┘
```

## 📁 项目结构

```
ai-jiesujiban-v3/
├── gpu-service/              # GPU服务（BGE-M3稀疏嵌入）
│   ├── main.py              # GPU服务主程序
│   ├── models/              # 模型相关代码
│   ├── api/                 # API接口
│   ├── utils/               # 工具函数
│   ├── Dockerfile           # GPU容器镜像
│   └── requirements.txt     # GPU服务依赖
├── cpu-service/              # CPU服务（主业务服务）
│   ├── main.py              # CPU服务主程序
│   ├── api/                 # API路由
│   ├── cores/               # 核心业务逻辑
│   ├── utils/               # 工具函数
│   ├── Dockerfile           # CPU容器镜像
│   └── requirements.txt     # CPU服务依赖
├── shared/                   # 共享代码
│   ├── config/              # 配置管理
│   ├── models/              # 数据模型
│   ├── utils/               # 通用工具
│   └── clients/             # 服务客户端
├── build.bat                 # 主构建入口脚本
├── start-dev.bat             # 本地开发启动脚本
├── scripts/                  # 构建脚本目录
│   ├── build-simple.ps1     # PowerShell高级构建脚本
│   ├── windows-quick-build.bat # Windows快速构建脚本
│   ├── linux-docker-build.sh  # Linux Docker构建脚本
│   ├── linux-docker-start.sh  # Linux Docker容器管理脚本
│   └── build-optimized.sh   # Linux性能测试脚本
├── deployment/               # 部署配置
│   ├── docker-compose.yml   # 本地开发环境
│   ├── docker-compose.optimized.yml # 优化生产环境
│   ├── nginx/               # Nginx配置
│   └── start-local.bat      # 本地启动脚本
├── configs/                  # 配置文件
│   ├── .env.local           # 本地环境变量
│   ├── .env.prod            # 生产环境变量
│   └── logging.yaml         # 日志配置
├── data/                     # 数据文件
├── logs/                     # 日志文件
├── tests/                    # 测试代码
└── docs/                     # 文档
```

## 🎯 核心特性

### 1. 性能提升
- **QPS提升73%**：从45 req/s提升到78 req/s
- **响应时间改善14%**：从2.1s降低到1.8s
- **GPU利用率提升42%**：从65%提升到92%

### 2. 架构优势
- **专业化分工**：GPU专门处理模型推理，CPU专门处理业务逻辑
- **资源优化**：GPU资源100%用于计算，避免I/O等待浪费
- **成本控制**：混合架构（API密集嵌入+本地稀疏嵌入）降低16%成本

### 3. 技术特性
- **智能回退**：GPU服务 → 本地BGE-M3 → BM25内置函数
- **健康检查**：完整的服务健康监控
- **负载均衡**：支持多实例部署和自动扩展
- **故障隔离**：服务分离，故障不会相互影响

## 🛠️ 快速开始

### 📋 环境要求

- **Python**: 3.11+
- **Docker**: 20.10+ & Docker Compose v2
- **GPU**: NVIDIA GPU + CUDA 12.8+
- **内存**: 16GB+ (推荐32GB+)
- **存储**: 50GB+ 可用空间

### ⚡ 一键启动（推荐）

使用我们的统一启动脚本，支持跨平台、智能检测、自动配置：

```bash
# 启动所有服务（GPU + 2个CPU副本）
python scripts/start_services.py --service all --env prod --replicas 2

# 仅启动GPU服务
python scripts/start_services.py --service gpu --env prod

# 启动3个CPU服务副本
python scripts/start_services.py --service cpu --replicas 3 --env prod

# 查看帮助
python scripts/start_services.py --help
```

### 🔧 手动部署

#### 1. 构建镜像
```bash
# 构建GPU服务镜像（从项目根目录执行）
docker build -f gpu-service/dockerfile -t ai-v3-gpu-service:prod .

# 构建CPU服务镜像（从项目根目录执行）
docker build -f cpu-service/dockerfile -t ai-v3-cpu-service:prod .

# 批量构建（Windows）
scripts/build_all_services.bat

# 批量构建（Linux）
bash scripts/build_all_services.sh
```

#### 2. 启动服务
```bash
# 方案A：使用Docker Compose（推荐）
cd deployment
docker-compose -f docker-compose.yml up -d

# 方案B：生产环境部署
docker-compose -f docker-compose.prod.yml up -d

# 方案C：手动启动容器
# 1. 创建网络
docker network create ai-network --subnet=**********/16

# 2. 启动GPU服务
docker run -d --name ai-v3-gpu-service-prod \
  --gpus all --restart unless-stopped \
  -p 8001:8001 \
  -v ai-models-cache:/app/models \
  -e ENVIRONMENT=production \
  -e GPU_SERVICE_HOST=0.0.0.0 \
  -e GPU_SERVICE_PORT=8001 \
  -e BGE_M3_MODEL_PATH=/app/models/hub/bge-m3 \
  --network ai-network \
  ai-v3-gpu-service:prod

# 3. 启动CPU服务
docker run -d --name ai-v3-cpu-service-1 \
  --restart unless-stopped \
  -p 8000:8000 \
  -e ENVIRONMENT=production \
  -e GPU_SERVICE_URL=http://ai-v3-gpu-service-prod:8001 \
  -e MILVUS_HOST=************ \
  -e MILVUS_PORT=19530 \
  --network ai-network \
  ai-v3-cpu-service:prod
```

### 🧪 验证部署

```bash
# 1. 健康检查
curl http://localhost:8001/health  # GPU服务
curl http://localhost:8000/v2/system/health  # CPU服务
curl http://localhost/health       # 负载均衡器

# 2. 测试GPU稀疏嵌入
curl -X POST http://localhost:8001/embeddings/sparse \
  -H "Content-Type: application/json" \
  -d '{"texts": ["测试文本"], "batch_size": 1}'

# 3. 测试CPU+GPU集成
curl -X POST http://localhost:8000/v2/retrieval/search \
  -H "Content-Type: application/json" \
  -d '{"query": "测试查询", "collection": "department"}'

# 4. 查看API文档
open http://localhost:8000/docs  # CPU服务API文档
open http://localhost:8001/docs  # GPU服务API文档

# 5. 查看容器状态
docker ps | grep ai-v3
docker logs ai-v3-gpu-service-prod
docker logs ai-v3-cpu-service-1
```

## 📊 性能对比

| 指标 | v2架构 | v3架构 | 提升 |
|------|--------|--------|------|
| QPS | 45 req/s | 78 req/s | +73% |
| 响应时间 | 2.1s | 1.8s | +14% |
| GPU利用率 | 65% | 92% | +42% |
| 成本 | 基准 | -16% | 节省 |

## 🔧 配置说明

### GPU服务配置
- 端口：8001
- 模型：BGE-M3
- 批处理大小：8
- GPU内存：自动管理

### CPU服务配置
- 端口：8009
- API嵌入：硅基流等
- 业务逻辑：完整功能
- 回退机制：多层保障

## 🔄 依赖更新

### 快速升级（推荐）
```bash
# 升级CPU服务依赖到最新版本
python scripts/quick_upgrade.py

# 重建GPU服务
python scripts/rebuild_gpu_service.py
```

### 完整升级流程
```bash
# 完整的现代化升级流程
python scripts/modern_upgrade_dependencies.py
```

更多详细信息请参考：[依赖更新指南](docs/dependency_update_guide.md)

## 📚 文档

- [Docker构建部署指南](docs/DOCKER_BUILD_DEPLOY_GUIDE.md) - **完整构建和部署命令**
- [依赖更新指南](docs/dependency_update_guide.md) - **现代化升级策略**
- [脚本目录指南](docs/SCRIPTS_DIRECTORY_GUIDE.md) - **脚本使用说明**
- [CPU服务启动指南](docs/CPU_SERVICE_STARTUP_GUIDE.md)
- [快速故障排除](docs/QUICK_TROUBLESHOOTING_GUIDE.md)
- [本地开发指南](docs/CPU_SERVICE_LOCAL_DEVELOPMENT_GUIDE.md)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

本项目采用MIT许可证。
