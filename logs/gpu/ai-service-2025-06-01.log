2025-06-01 17:22:50 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 17:22:50 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/ai-service-2025-06-01.log
2025-06-01 17:22:50 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/ai-service-error-2025-06-01.log
2025-06-01 17:22:50 [INFO] uvicorn.error [server.py:77] - Started server process [1]
2025-06-01 17:22:50 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-01 17:22:50 [INFO] main [main.py:261] - 初始化BGE-M3稀疏嵌入服务...
2025-06-01 17:22:50 [INFO] main [main.py:102] - 使用GPU: NVIDIA GeForce RTX 4060 Laptop GPU, 内存: 8.0GB
2025-06-01 17:22:50 [INFO] main [main.py:108] - 加载BGE-M3模型: /app/models/bge-m3
2025-06-01 17:22:50 [ERROR] main [main.py:122] - 模型初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:22:50 [ERROR] main [main.py:266] - 服务初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:22:50 [INFO] main [main.py:272] - BGE-M3稀疏嵌入服务关闭
2025-06-01 17:22:50 [ERROR] uvicorn.error [on.py:121] - Traceback (most recent call last):
  File "/app/start_optimized.py", line 113, in start_service
    uvicorn.run(
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/main.py", line 587, in run
    server.run()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    self.config.setup_event_loop()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/config.py", line 502, in setup_event_loop
    loop_setup: Callable | None = import_from_string(LOOP_SETUPS[self.loop])
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/loops/uvloop.py", line 3, in <module>
    import uvloop
ModuleNotFoundError: No module named 'uvloop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 470, in cached_files
    hf_hub_download(
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/opt/conda/lib/python3.11/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 262, in lifespan
    sparse_model = BGEMSparseEmbeddingService()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 90, in __init__
    self._initialize_model()
  File "/app/main.py", line 110, in _initialize_model
    self.model = BGEM3FlagModel(
                 ^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/FlagEmbedding/inference/embedder/encoder_only/m3.py", line 89, in __init__
    self.tokenizer = AutoTokenizer.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 950, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 782, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 312, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 522, in cached_files
    resolved_files = [
                     ^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 523, in <listcomp>
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision) for filename in full_filenames
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 140, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(path_or_repo_id, full_filename, cache_dir=cache_dir, revision=revision)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

2025-06-01 17:22:50 [ERROR] uvicorn.error [on.py:57] - Application startup failed. Exiting.
2025-06-01 17:22:56 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 17:22:56 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/ai-service-2025-06-01.log
2025-06-01 17:22:56 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/ai-service-error-2025-06-01.log
2025-06-01 17:22:56 [INFO] uvicorn.error [server.py:77] - Started server process [1]
2025-06-01 17:22:56 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-01 17:22:56 [INFO] main [main.py:261] - 初始化BGE-M3稀疏嵌入服务...
2025-06-01 17:22:56 [INFO] main [main.py:102] - 使用GPU: NVIDIA GeForce RTX 4060 Laptop GPU, 内存: 8.0GB
2025-06-01 17:22:56 [INFO] main [main.py:108] - 加载BGE-M3模型: /app/models/bge-m3
2025-06-01 17:22:56 [ERROR] main [main.py:122] - 模型初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:22:56 [ERROR] main [main.py:266] - 服务初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:22:56 [INFO] main [main.py:272] - BGE-M3稀疏嵌入服务关闭
2025-06-01 17:22:56 [ERROR] uvicorn.error [on.py:121] - Traceback (most recent call last):
  File "/app/start_optimized.py", line 113, in start_service
    uvicorn.run(
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/main.py", line 587, in run
    server.run()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    self.config.setup_event_loop()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/config.py", line 502, in setup_event_loop
    loop_setup: Callable | None = import_from_string(LOOP_SETUPS[self.loop])
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/loops/uvloop.py", line 3, in <module>
    import uvloop
ModuleNotFoundError: No module named 'uvloop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 470, in cached_files
    hf_hub_download(
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/opt/conda/lib/python3.11/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 262, in lifespan
    sparse_model = BGEMSparseEmbeddingService()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 90, in __init__
    self._initialize_model()
  File "/app/main.py", line 110, in _initialize_model
    self.model = BGEM3FlagModel(
                 ^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/FlagEmbedding/inference/embedder/encoder_only/m3.py", line 89, in __init__
    self.tokenizer = AutoTokenizer.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 950, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 782, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 312, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 522, in cached_files
    resolved_files = [
                     ^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 523, in <listcomp>
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision) for filename in full_filenames
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 140, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(path_or_repo_id, full_filename, cache_dir=cache_dir, revision=revision)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

2025-06-01 17:22:56 [ERROR] uvicorn.error [on.py:57] - Application startup failed. Exiting.
2025-06-01 17:23:02 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 17:23:02 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/ai-service-2025-06-01.log
2025-06-01 17:23:02 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/ai-service-error-2025-06-01.log
2025-06-01 17:23:02 [INFO] uvicorn.error [server.py:77] - Started server process [1]
2025-06-01 17:23:02 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-01 17:23:02 [INFO] main [main.py:261] - 初始化BGE-M3稀疏嵌入服务...
2025-06-01 17:23:02 [INFO] main [main.py:102] - 使用GPU: NVIDIA GeForce RTX 4060 Laptop GPU, 内存: 8.0GB
2025-06-01 17:23:02 [INFO] main [main.py:108] - 加载BGE-M3模型: /app/models/bge-m3
2025-06-01 17:23:02 [ERROR] main [main.py:122] - 模型初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:02 [ERROR] main [main.py:266] - 服务初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:02 [INFO] main [main.py:272] - BGE-M3稀疏嵌入服务关闭
2025-06-01 17:23:02 [ERROR] uvicorn.error [on.py:121] - Traceback (most recent call last):
  File "/app/start_optimized.py", line 113, in start_service
    uvicorn.run(
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/main.py", line 587, in run
    server.run()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    self.config.setup_event_loop()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/config.py", line 502, in setup_event_loop
    loop_setup: Callable | None = import_from_string(LOOP_SETUPS[self.loop])
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/loops/uvloop.py", line 3, in <module>
    import uvloop
ModuleNotFoundError: No module named 'uvloop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 470, in cached_files
    hf_hub_download(
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/opt/conda/lib/python3.11/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 262, in lifespan
    sparse_model = BGEMSparseEmbeddingService()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 90, in __init__
    self._initialize_model()
  File "/app/main.py", line 110, in _initialize_model
    self.model = BGEM3FlagModel(
                 ^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/FlagEmbedding/inference/embedder/encoder_only/m3.py", line 89, in __init__
    self.tokenizer = AutoTokenizer.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 950, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 782, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 312, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 522, in cached_files
    resolved_files = [
                     ^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 523, in <listcomp>
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision) for filename in full_filenames
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 140, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(path_or_repo_id, full_filename, cache_dir=cache_dir, revision=revision)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

2025-06-01 17:23:02 [ERROR] uvicorn.error [on.py:57] - Application startup failed. Exiting.
2025-06-01 17:23:07 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 17:23:07 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/ai-service-2025-06-01.log
2025-06-01 17:23:07 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/ai-service-error-2025-06-01.log
2025-06-01 17:23:07 [INFO] uvicorn.error [server.py:77] - Started server process [1]
2025-06-01 17:23:07 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-01 17:23:07 [INFO] main [main.py:261] - 初始化BGE-M3稀疏嵌入服务...
2025-06-01 17:23:07 [INFO] main [main.py:102] - 使用GPU: NVIDIA GeForce RTX 4060 Laptop GPU, 内存: 8.0GB
2025-06-01 17:23:07 [INFO] main [main.py:108] - 加载BGE-M3模型: /app/models/bge-m3
2025-06-01 17:23:07 [ERROR] main [main.py:122] - 模型初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:07 [ERROR] main [main.py:266] - 服务初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:07 [INFO] main [main.py:272] - BGE-M3稀疏嵌入服务关闭
2025-06-01 17:23:07 [ERROR] uvicorn.error [on.py:121] - Traceback (most recent call last):
  File "/app/start_optimized.py", line 113, in start_service
    uvicorn.run(
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/main.py", line 587, in run
    server.run()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    self.config.setup_event_loop()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/config.py", line 502, in setup_event_loop
    loop_setup: Callable | None = import_from_string(LOOP_SETUPS[self.loop])
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/loops/uvloop.py", line 3, in <module>
    import uvloop
ModuleNotFoundError: No module named 'uvloop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 470, in cached_files
    hf_hub_download(
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/opt/conda/lib/python3.11/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 262, in lifespan
    sparse_model = BGEMSparseEmbeddingService()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 90, in __init__
    self._initialize_model()
  File "/app/main.py", line 110, in _initialize_model
    self.model = BGEM3FlagModel(
                 ^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/FlagEmbedding/inference/embedder/encoder_only/m3.py", line 89, in __init__
    self.tokenizer = AutoTokenizer.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 950, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 782, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 312, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 522, in cached_files
    resolved_files = [
                     ^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 523, in <listcomp>
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision) for filename in full_filenames
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 140, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(path_or_repo_id, full_filename, cache_dir=cache_dir, revision=revision)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

2025-06-01 17:23:07 [ERROR] uvicorn.error [on.py:57] - Application startup failed. Exiting.
2025-06-01 17:23:12 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 17:23:12 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/ai-service-2025-06-01.log
2025-06-01 17:23:12 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/ai-service-error-2025-06-01.log
2025-06-01 17:23:12 [INFO] uvicorn.error [server.py:77] - Started server process [1]
2025-06-01 17:23:12 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-01 17:23:12 [INFO] main [main.py:261] - 初始化BGE-M3稀疏嵌入服务...
2025-06-01 17:23:12 [INFO] main [main.py:102] - 使用GPU: NVIDIA GeForce RTX 4060 Laptop GPU, 内存: 8.0GB
2025-06-01 17:23:12 [INFO] main [main.py:108] - 加载BGE-M3模型: /app/models/bge-m3
2025-06-01 17:23:12 [ERROR] main [main.py:122] - 模型初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:12 [ERROR] main [main.py:266] - 服务初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:12 [INFO] main [main.py:272] - BGE-M3稀疏嵌入服务关闭
2025-06-01 17:23:12 [ERROR] uvicorn.error [on.py:121] - Traceback (most recent call last):
  File "/app/start_optimized.py", line 113, in start_service
    uvicorn.run(
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/main.py", line 587, in run
    server.run()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    self.config.setup_event_loop()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/config.py", line 502, in setup_event_loop
    loop_setup: Callable | None = import_from_string(LOOP_SETUPS[self.loop])
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/loops/uvloop.py", line 3, in <module>
    import uvloop
ModuleNotFoundError: No module named 'uvloop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 470, in cached_files
    hf_hub_download(
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/opt/conda/lib/python3.11/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 262, in lifespan
    sparse_model = BGEMSparseEmbeddingService()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 90, in __init__
    self._initialize_model()
  File "/app/main.py", line 110, in _initialize_model
    self.model = BGEM3FlagModel(
                 ^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/FlagEmbedding/inference/embedder/encoder_only/m3.py", line 89, in __init__
    self.tokenizer = AutoTokenizer.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 950, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 782, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 312, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 522, in cached_files
    resolved_files = [
                     ^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 523, in <listcomp>
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision) for filename in full_filenames
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 140, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(path_or_repo_id, full_filename, cache_dir=cache_dir, revision=revision)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

2025-06-01 17:23:13 [ERROR] uvicorn.error [on.py:57] - Application startup failed. Exiting.
2025-06-01 17:23:19 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 17:23:19 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/ai-service-2025-06-01.log
2025-06-01 17:23:19 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/ai-service-error-2025-06-01.log
2025-06-01 17:23:19 [INFO] uvicorn.error [server.py:77] - Started server process [1]
2025-06-01 17:23:19 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-01 17:23:19 [INFO] main [main.py:261] - 初始化BGE-M3稀疏嵌入服务...
2025-06-01 17:23:19 [INFO] main [main.py:102] - 使用GPU: NVIDIA GeForce RTX 4060 Laptop GPU, 内存: 8.0GB
2025-06-01 17:23:19 [INFO] main [main.py:108] - 加载BGE-M3模型: /app/models/bge-m3
2025-06-01 17:23:19 [ERROR] main [main.py:122] - 模型初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:19 [ERROR] main [main.py:266] - 服务初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:19 [INFO] main [main.py:272] - BGE-M3稀疏嵌入服务关闭
2025-06-01 17:23:19 [ERROR] uvicorn.error [on.py:121] - Traceback (most recent call last):
  File "/app/start_optimized.py", line 113, in start_service
    uvicorn.run(
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/main.py", line 587, in run
    server.run()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    self.config.setup_event_loop()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/config.py", line 502, in setup_event_loop
    loop_setup: Callable | None = import_from_string(LOOP_SETUPS[self.loop])
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/loops/uvloop.py", line 3, in <module>
    import uvloop
ModuleNotFoundError: No module named 'uvloop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 470, in cached_files
    hf_hub_download(
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/opt/conda/lib/python3.11/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 262, in lifespan
    sparse_model = BGEMSparseEmbeddingService()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 90, in __init__
    self._initialize_model()
  File "/app/main.py", line 110, in _initialize_model
    self.model = BGEM3FlagModel(
                 ^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/FlagEmbedding/inference/embedder/encoder_only/m3.py", line 89, in __init__
    self.tokenizer = AutoTokenizer.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 950, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 782, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 312, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 522, in cached_files
    resolved_files = [
                     ^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 523, in <listcomp>
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision) for filename in full_filenames
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 140, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(path_or_repo_id, full_filename, cache_dir=cache_dir, revision=revision)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

2025-06-01 17:23:19 [ERROR] uvicorn.error [on.py:57] - Application startup failed. Exiting.
2025-06-01 17:23:28 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 17:23:28 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/ai-service-2025-06-01.log
2025-06-01 17:23:28 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/ai-service-error-2025-06-01.log
2025-06-01 17:23:28 [INFO] uvicorn.error [server.py:77] - Started server process [1]
2025-06-01 17:23:28 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-01 17:23:28 [INFO] main [main.py:261] - 初始化BGE-M3稀疏嵌入服务...
2025-06-01 17:23:28 [INFO] main [main.py:102] - 使用GPU: NVIDIA GeForce RTX 4060 Laptop GPU, 内存: 8.0GB
2025-06-01 17:23:28 [INFO] main [main.py:108] - 加载BGE-M3模型: /app/models/bge-m3
2025-06-01 17:23:28 [ERROR] main [main.py:122] - 模型初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:28 [ERROR] main [main.py:266] - 服务初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:28 [INFO] main [main.py:272] - BGE-M3稀疏嵌入服务关闭
2025-06-01 17:23:28 [ERROR] uvicorn.error [on.py:121] - Traceback (most recent call last):
  File "/app/start_optimized.py", line 113, in start_service
    uvicorn.run(
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/main.py", line 587, in run
    server.run()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    self.config.setup_event_loop()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/config.py", line 502, in setup_event_loop
    loop_setup: Callable | None = import_from_string(LOOP_SETUPS[self.loop])
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/loops/uvloop.py", line 3, in <module>
    import uvloop
ModuleNotFoundError: No module named 'uvloop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 470, in cached_files
    hf_hub_download(
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/opt/conda/lib/python3.11/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 262, in lifespan
    sparse_model = BGEMSparseEmbeddingService()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 90, in __init__
    self._initialize_model()
  File "/app/main.py", line 110, in _initialize_model
    self.model = BGEM3FlagModel(
                 ^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/FlagEmbedding/inference/embedder/encoder_only/m3.py", line 89, in __init__
    self.tokenizer = AutoTokenizer.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 950, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 782, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 312, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 522, in cached_files
    resolved_files = [
                     ^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 523, in <listcomp>
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision) for filename in full_filenames
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 140, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(path_or_repo_id, full_filename, cache_dir=cache_dir, revision=revision)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

2025-06-01 17:23:28 [ERROR] uvicorn.error [on.py:57] - Application startup failed. Exiting.
2025-06-01 17:23:41 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 17:23:41 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/ai-service-2025-06-01.log
2025-06-01 17:23:41 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/ai-service-error-2025-06-01.log
2025-06-01 17:23:41 [INFO] uvicorn.error [server.py:77] - Started server process [1]
2025-06-01 17:23:41 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-01 17:23:41 [INFO] main [main.py:261] - 初始化BGE-M3稀疏嵌入服务...
2025-06-01 17:23:41 [INFO] main [main.py:102] - 使用GPU: NVIDIA GeForce RTX 4060 Laptop GPU, 内存: 8.0GB
2025-06-01 17:23:41 [INFO] main [main.py:108] - 加载BGE-M3模型: /app/models/bge-m3
2025-06-01 17:23:41 [ERROR] main [main.py:122] - 模型初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:41 [ERROR] main [main.py:266] - 服务初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:41 [INFO] main [main.py:272] - BGE-M3稀疏嵌入服务关闭
2025-06-01 17:23:41 [ERROR] uvicorn.error [on.py:121] - Traceback (most recent call last):
  File "/app/start_optimized.py", line 113, in start_service
    uvicorn.run(
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/main.py", line 587, in run
    server.run()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    self.config.setup_event_loop()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/config.py", line 502, in setup_event_loop
    loop_setup: Callable | None = import_from_string(LOOP_SETUPS[self.loop])
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/loops/uvloop.py", line 3, in <module>
    import uvloop
ModuleNotFoundError: No module named 'uvloop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 470, in cached_files
    hf_hub_download(
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/opt/conda/lib/python3.11/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 262, in lifespan
    sparse_model = BGEMSparseEmbeddingService()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 90, in __init__
    self._initialize_model()
  File "/app/main.py", line 110, in _initialize_model
    self.model = BGEM3FlagModel(
                 ^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/FlagEmbedding/inference/embedder/encoder_only/m3.py", line 89, in __init__
    self.tokenizer = AutoTokenizer.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 950, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 782, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 312, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 522, in cached_files
    resolved_files = [
                     ^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 523, in <listcomp>
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision) for filename in full_filenames
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 140, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(path_or_repo_id, full_filename, cache_dir=cache_dir, revision=revision)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

2025-06-01 17:23:41 [ERROR] uvicorn.error [on.py:57] - Application startup failed. Exiting.
2025-06-01 17:23:59 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 17:23:59 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/ai-service-2025-06-01.log
2025-06-01 17:23:59 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/ai-service-error-2025-06-01.log
2025-06-01 17:23:59 [INFO] uvicorn.error [server.py:77] - Started server process [1]
2025-06-01 17:23:59 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-01 17:23:59 [INFO] main [main.py:261] - 初始化BGE-M3稀疏嵌入服务...
2025-06-01 17:23:59 [INFO] main [main.py:102] - 使用GPU: NVIDIA GeForce RTX 4060 Laptop GPU, 内存: 8.0GB
2025-06-01 17:23:59 [INFO] main [main.py:108] - 加载BGE-M3模型: /app/models/bge-m3
2025-06-01 17:23:59 [ERROR] main [main.py:122] - 模型初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:59 [ERROR] main [main.py:266] - 服务初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:59 [INFO] main [main.py:272] - BGE-M3稀疏嵌入服务关闭
2025-06-01 17:23:59 [ERROR] uvicorn.error [on.py:121] - Traceback (most recent call last):
  File "/app/start_optimized.py", line 113, in start_service
    uvicorn.run(
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/main.py", line 587, in run
    server.run()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    self.config.setup_event_loop()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/config.py", line 502, in setup_event_loop
    loop_setup: Callable | None = import_from_string(LOOP_SETUPS[self.loop])
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/loops/uvloop.py", line 3, in <module>
    import uvloop
ModuleNotFoundError: No module named 'uvloop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 470, in cached_files
    hf_hub_download(
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/opt/conda/lib/python3.11/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 262, in lifespan
    sparse_model = BGEMSparseEmbeddingService()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 90, in __init__
    self._initialize_model()
  File "/app/main.py", line 110, in _initialize_model
    self.model = BGEM3FlagModel(
                 ^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/FlagEmbedding/inference/embedder/encoder_only/m3.py", line 89, in __init__
    self.tokenizer = AutoTokenizer.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 950, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 782, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 312, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 522, in cached_files
    resolved_files = [
                     ^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 523, in <listcomp>
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision) for filename in full_filenames
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 140, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(path_or_repo_id, full_filename, cache_dir=cache_dir, revision=revision)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

2025-06-01 17:23:59 [ERROR] uvicorn.error [on.py:57] - Application startup failed. Exiting.
2025-06-01 17:26:40 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 17:26:40 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/ai-service-2025-06-01.log
2025-06-01 17:26:40 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/ai-service-error-2025-06-01.log
2025-06-01 17:26:40 [INFO] uvicorn.error [server.py:77] - Started server process [1]
2025-06-01 17:26:40 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-01 17:26:40 [INFO] main [main.py:261] - 初始化BGE-M3稀疏嵌入服务...
2025-06-01 17:26:40 [INFO] main [main.py:102] - 使用GPU: NVIDIA GeForce RTX 4060 Laptop GPU, 内存: 8.0GB
2025-06-01 17:26:40 [INFO] main [main.py:108] - 加载BGE-M3模型: /app/models/hub/bge-m3
2025-06-01 17:26:44 [INFO] FlagEmbedding.finetune.embedder.encoder_only.m3.runner [runner.py:86] - loading existing colbert_linear and sparse_linear---------
2025-06-01 17:26:44 [INFO] main [main.py:116] - BGE-M3模型加载完成，耗时: 3.487秒
2025-06-01 17:26:44 [INFO] main [main.py:128] - 开始模型预热...
2025-06-01 17:26:45 [INFO] main [main.py:153] - 模型预热完成，耗时: 1.209秒
2025-06-01 17:26:45 [INFO] main [main.py:154] - 🚀 BGE-M3模型已就绪，首次推理性能已优化
2025-06-01 17:26:45 [INFO] main [main.py:263] - BGE-M3稀疏嵌入服务初始化完成
2025-06-01 17:26:45 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-01 17:26:45 [INFO] uvicorn.error [server.py:219] - Uvicorn running on http://0.0.0.0:8001 (Press CTRL+C to quit)
2025-06-01 17:26:47 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:49370 - "GET /health HTTP/1.1" 200
2025-06-01 17:26:57 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:47376 - "GET /health HTTP/1.1" 200
2025-06-01 17:27:14 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.146秒
2025-06-01 17:27:14 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56996 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 17:27:17 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:35536 - "GET /health HTTP/1.1" 200
2025-06-01 17:27:26 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:35810 - "GET /health HTTP/1.1" 200
2025-06-01 17:27:47 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:54328 - "GET /health HTTP/1.1" 200
2025-06-01 17:28:17 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:37888 - "GET /health HTTP/1.1" 200
2025-06-01 17:28:34 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:35390 - "GET /health HTTP/1.1" 200
2025-06-01 17:28:47 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:54980 - "GET /health HTTP/1.1" 200
2025-06-01 17:29:09 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:40220 - "GET /health HTTP/1.1" 200
2025-06-01 17:29:09 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.164秒
2025-06-01 17:29:09 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:40236 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 17:29:17 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:60332 - "GET /health HTTP/1.1" 200
2025-06-01 17:29:47 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:34936 - "GET /health HTTP/1.1" 200
2025-06-01 17:29:59 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 5个文本, 耗时: 0.236秒
2025-06-01 17:29:59 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:33984 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 17:29:59 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:33988 - "GET /docs HTTP/1.1" 200
2025-06-01 17:30:17 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:36674 - "GET /health HTTP/1.1" 200
2025-06-01 17:30:47 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:44084 - "GET /health HTTP/1.1" 200
2025-06-01 17:31:17 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:48884 - "GET /health HTTP/1.1" 200
2025-06-01 17:31:47 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:60964 - "GET /health HTTP/1.1" 200
2025-06-01 17:32:17 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:54742 - "GET /health HTTP/1.1" 200
2025-06-01 17:32:47 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:54056 - "GET /health HTTP/1.1" 200
2025-06-01 17:33:17 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:33480 - "GET /health HTTP/1.1" 200
2025-06-01 17:33:47 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:51844 - "GET /health HTTP/1.1" 200
2025-06-01 17:34:17 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:57752 - "GET /health HTTP/1.1" 200
2025-06-01 17:34:47 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:54624 - "GET /health HTTP/1.1" 200
2025-06-01 17:35:17 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:50642 - "GET /health HTTP/1.1" 200
2025-06-01 17:35:48 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:39062 - "GET /health HTTP/1.1" 200
2025-06-01 17:36:18 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:58220 - "GET /health HTTP/1.1" 200
2025-06-01 17:36:48 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:45284 - "GET /health HTTP/1.1" 200
2025-06-01 17:37:18 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:55760 - "GET /health HTTP/1.1" 200
2025-06-01 17:37:48 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:43368 - "GET /health HTTP/1.1" 200
2025-06-01 17:37:54 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:37956 - "GET /health HTTP/1.1" 200
2025-06-01 17:38:18 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:41950 - "GET /health HTTP/1.1" 200
2025-06-01 17:38:48 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:51434 - "GET /health HTTP/1.1" 200
2025-06-01 17:39:18 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:39412 - "GET /health HTTP/1.1" 200
2025-06-01 17:39:48 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:33372 - "GET /health HTTP/1.1" 200
2025-06-01 17:40:18 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:36010 - "GET /health HTTP/1.1" 200
2025-06-01 17:40:48 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:59564 - "GET /health HTTP/1.1" 200
2025-06-01 17:41:18 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:53030 - "GET /health HTTP/1.1" 200
2025-06-01 17:41:48 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:59074 - "GET /health HTTP/1.1" 200
2025-06-01 17:42:18 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:54174 - "GET /health HTTP/1.1" 200
2025-06-01 17:42:48 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:46792 - "GET /health HTTP/1.1" 200
2025-06-01 17:43:18 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:47100 - "GET /health HTTP/1.1" 200
2025-06-01 17:43:48 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:42612 - "GET /health HTTP/1.1" 200
2025-06-01 17:44:18 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:56154 - "GET /health HTTP/1.1" 200
2025-06-01 17:44:48 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:49332 - "GET /health HTTP/1.1" 200
2025-06-01 17:45:18 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:54620 - "GET /health HTTP/1.1" 200
2025-06-01 17:45:48 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:50202 - "GET /health HTTP/1.1" 200
2025-06-01 17:46:12 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:53108 - "GET /health HTTP/1.1" 200
2025-06-01 17:46:18 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:46894 - "GET /health HTTP/1.1" 200
2025-06-01 17:46:48 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:43506 - "GET /health HTTP/1.1" 200
2025-06-01 17:47:18 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:44638 - "GET /health HTTP/1.1" 200
2025-06-01 17:47:48 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:49324 - "GET /health HTTP/1.1" 200
2025-06-01 17:48:19 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:48514 - "GET /health HTTP/1.1" 200
2025-06-01 17:48:49 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:49560 - "GET /health HTTP/1.1" 200
2025-06-01 17:49:19 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:37774 - "GET /health HTTP/1.1" 200
2025-06-01 17:49:49 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:37824 - "GET /health HTTP/1.1" 200
2025-06-01 17:50:19 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:49972 - "GET /health HTTP/1.1" 200
2025-06-01 17:50:49 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:45542 - "GET /health HTTP/1.1" 200
2025-06-01 17:51:19 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:38380 - "GET /health HTTP/1.1" 200
2025-06-01 17:51:49 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:47636 - "GET /health HTTP/1.1" 200
2025-06-01 17:52:19 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:36414 - "GET /health HTTP/1.1" 200
2025-06-01 17:52:49 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:50534 - "GET /health HTTP/1.1" 200
2025-06-01 17:53:03 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:55178 - "GET /health HTTP/1.1" 200
2025-06-01 17:53:03 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.296秒
2025-06-01 17:53:03 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:55180 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 17:53:03 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 5个文本, 耗时: 0.039秒
2025-06-01 17:53:03 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:55196 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 17:53:03 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.022秒
2025-06-01 17:53:03 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:55202 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 17:53:03 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.027秒
2025-06-01 17:53:03 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:55204 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 17:53:03 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.022秒
2025-06-01 17:53:03 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:55214 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 17:53:19 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:46030 - "GET /health HTTP/1.1" 200
2025-06-01 17:53:49 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:54814 - "GET /health HTTP/1.1" 200
2025-06-01 17:54:19 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:55590 - "GET /health HTTP/1.1" 200
2025-06-01 17:54:49 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:60736 - "GET /health HTTP/1.1" 200
2025-06-01 17:55:19 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:54682 - "GET /health HTTP/1.1" 200
2025-06-01 17:55:49 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:59450 - "GET /health HTTP/1.1" 200
2025-06-01 17:56:19 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:44448 - "GET /health HTTP/1.1" 200
2025-06-01 17:56:49 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:48510 - "GET /health HTTP/1.1" 200
2025-06-01 17:57:19 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:37260 - "GET /health HTTP/1.1" 200
2025-06-01 17:57:49 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:48238 - "GET /health HTTP/1.1" 200
2025-06-01 17:58:19 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:54632 - "GET /health HTTP/1.1" 200
2025-06-01 17:58:49 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:37220 - "GET /health HTTP/1.1" 200
2025-06-01 17:59:19 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:33774 - "GET /health HTTP/1.1" 200
2025-06-01 17:59:49 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:45110 - "GET /health HTTP/1.1" 200
2025-06-01 18:00:19 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:35340 - "GET /health HTTP/1.1" 200
2025-06-01 18:00:49 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:40752 - "GET /health HTTP/1.1" 200
2025-06-01 18:01:19 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:52664 - "GET /health HTTP/1.1" 200
2025-06-01 18:01:50 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:52064 - "GET /health HTTP/1.1" 200
2025-06-01 18:02:20 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:44086 - "GET /health HTTP/1.1" 200
2025-06-01 18:02:50 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:52936 - "GET /health HTTP/1.1" 200
2025-06-01 18:03:20 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:41740 - "GET /health HTTP/1.1" 200
2025-06-01 18:03:50 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:60130 - "GET /health HTTP/1.1" 200
2025-06-01 18:04:20 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:38056 - "GET /health HTTP/1.1" 200
2025-06-01 18:04:50 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:51262 - "GET /health HTTP/1.1" 200
2025-06-01 18:05:20 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:44560 - "GET /health HTTP/1.1" 200
2025-06-01 18:05:50 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:60038 - "GET /health HTTP/1.1" 200
2025-06-01 18:06:20 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:36188 - "GET /health HTTP/1.1" 200
2025-06-01 18:06:50 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:50162 - "GET /health HTTP/1.1" 200
2025-06-01 18:07:20 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:46404 - "GET /health HTTP/1.1" 200
2025-06-01 18:07:50 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:48222 - "GET /health HTTP/1.1" 200
2025-06-01 18:08:20 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:42392 - "GET /health HTTP/1.1" 200
2025-06-01 18:08:50 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:34180 - "GET /health HTTP/1.1" 200
2025-06-01 18:09:20 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:42098 - "GET /health HTTP/1.1" 200
2025-06-01 18:09:50 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:47676 - "GET /health HTTP/1.1" 200
2025-06-01 18:10:20 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:41332 - "GET /health HTTP/1.1" 200
2025-06-01 18:10:50 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:57754 - "GET /health HTTP/1.1" 200
2025-06-01 18:11:20 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:37070 - "GET /health HTTP/1.1" 200
2025-06-01 18:11:50 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:42786 - "GET /health HTTP/1.1" 200
2025-06-01 18:12:20 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:33776 - "GET /health HTTP/1.1" 200
2025-06-01 18:12:50 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:48876 - "GET /health HTTP/1.1" 200
2025-06-01 18:13:20 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:59830 - "GET /health HTTP/1.1" 200
2025-06-01 18:13:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:58718 - "GET /health HTTP/1.1" 200
2025-06-01 18:13:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:58724 - "GET /model/info HTTP/1.1" 200
2025-06-01 18:13:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 3个文本, 耗时: 0.166秒
2025-06-01 18:13:47 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:58728 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:13:47 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:58732 - "GET /health HTTP/1.1" 200
2025-06-01 18:13:50 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:57688 - "GET /health HTTP/1.1" 200
2025-06-01 18:14:20 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:60990 - "GET /health HTTP/1.1" 200
2025-06-01 18:14:50 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:42814 - "GET /health HTTP/1.1" 200
2025-06-01 18:15:20 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:33684 - "GET /health HTTP/1.1" 200
2025-06-01 18:15:50 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:34032 - "GET /health HTTP/1.1" 200
2025-06-01 18:16:20 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:46778 - "GET /health HTTP/1.1" 200
2025-06-01 18:16:50 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:34564 - "GET /health HTTP/1.1" 200
2025-06-01 18:17:21 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:40270 - "GET /health HTTP/1.1" 200
2025-06-01 18:17:51 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:57632 - "GET /health HTTP/1.1" 200
2025-06-01 18:18:21 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:41482 - "GET /health HTTP/1.1" 200
2025-06-01 18:18:51 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:60808 - "GET /health HTTP/1.1" 200
2025-06-01 18:19:21 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:37866 - "GET /health HTTP/1.1" 200
2025-06-01 18:19:51 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:37240 - "GET /health HTTP/1.1" 200
2025-06-01 18:20:21 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:49856 - "GET /health HTTP/1.1" 200
2025-06-01 18:20:51 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:40274 - "GET /health HTTP/1.1" 200
2025-06-01 18:21:21 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:60594 - "GET /health HTTP/1.1" 200
2025-06-01 18:21:51 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:56924 - "GET /health HTTP/1.1" 200
2025-06-01 18:22:21 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:46474 - "GET /health HTTP/1.1" 200
2025-06-01 18:22:51 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:56580 - "GET /health HTTP/1.1" 200
2025-06-01 18:23:21 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:40690 - "GET /health HTTP/1.1" 200
2025-06-01 18:23:51 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:55640 - "GET /health HTTP/1.1" 200
2025-06-01 18:24:21 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:58858 - "GET /health HTTP/1.1" 200
2025-06-01 18:24:51 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:39228 - "GET /health HTTP/1.1" 200
2025-06-01 18:25:21 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:57606 - "GET /health HTTP/1.1" 200
2025-06-01 18:25:51 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:56506 - "GET /health HTTP/1.1" 200
2025-06-01 18:26:06 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:43404 - "GET /health HTTP/1.1" 200
2025-06-01 18:26:21 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:32942 - "GET /health HTTP/1.1" 200
2025-06-01 18:26:24 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:42014 - "GET /health HTTP/1.1" 200
2025-06-01 18:26:24 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:42022 - "GET /health HTTP/1.1" 200
2025-06-01 18:26:24 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:42026 - "GET /health HTTP/1.1" 200
2025-06-01 18:26:24 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.219秒
2025-06-01 18:26:24 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:42032 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 7个文本, 耗时: 0.296秒
2025-06-01 18:26:33 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:43466 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 7个文本, 耗时: 0.032秒
2025-06-01 18:26:33 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:43474 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.027秒
2025-06-01 18:26:33 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:43490 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.021秒
2025-06-01 18:26:33 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:43502 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.021秒
2025-06-01 18:26:33 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:43516 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.022秒
2025-06-01 18:26:33 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:43518 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.021秒
2025-06-01 18:26:33 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:43526 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.021秒
2025-06-01 18:26:33 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:43542 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.023秒
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.022秒
2025-06-01 18:26:33 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:43546 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.022秒
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.023秒
2025-06-01 18:26:33 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:43560 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.026秒
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.035秒
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 4个文本, 耗时: 0.023秒
2025-06-01 18:26:33 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:43576 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.022秒
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.032秒
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 4个文本, 耗时: 0.030秒
2025-06-01 18:26:33 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:43586 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.024秒
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.021秒
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.021秒
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.021秒
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.030秒
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.030秒
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.023秒
2025-06-01 18:26:33 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:43602 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.022秒
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.021秒
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.022秒
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.023秒
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.025秒
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.022秒
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.021秒
2025-06-01 18:26:33 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:43616 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:26:33 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.023秒
2025-06-01 18:26:33 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:43632 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:26:51 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:52960 - "GET /health HTTP/1.1" 200
2025-06-01 18:27:21 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:38476 - "GET /health HTTP/1.1" 200
2025-06-01 18:27:50 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:58528 - "GET /health HTTP/1.1" 200
2025-06-01 18:27:50 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:58532 - "GET /health HTTP/1.1" 200
2025-06-01 18:27:50 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.213秒
2025-06-01 18:27:50 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:58548 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:27:51 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:59358 - "GET /health HTTP/1.1" 200
2025-06-01 18:27:52 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:58562 - "GET /health HTTP/1.1" 200
2025-06-01 18:27:52 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.031秒
2025-06-01 18:27:52 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:58572 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:27:55 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:37898 - "GET /health HTTP/1.1" 200
2025-06-01 18:27:56 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.258秒
2025-06-01 18:27:56 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:37912 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:21 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:47486 - "GET /health HTTP/1.1" 200
2025-06-01 18:28:37 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:34200 - "GET /health HTTP/1.1" 200
2025-06-01 18:28:37 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:34216 - "GET /health HTTP/1.1" 200
2025-06-01 18:28:38 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:34222 - "GET /health HTTP/1.1" 200
2025-06-01 18:28:38 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.024秒
2025-06-01 18:28:38 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:34230 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:45 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 7个文本, 耗时: 0.033秒
2025-06-01 18:28:45 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45544 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:45 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 7个文本, 耗时: 0.047秒
2025-06-01 18:28:45 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45556 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:45 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.092秒
2025-06-01 18:28:45 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45566 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.038秒
2025-06-01 18:28:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45572 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.023秒
2025-06-01 18:28:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45576 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.024秒
2025-06-01 18:28:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45584 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.024秒
2025-06-01 18:28:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45598 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.025秒
2025-06-01 18:28:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45600 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.024秒
2025-06-01 18:28:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45616 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.024秒
2025-06-01 18:28:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45632 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.023秒
2025-06-01 18:28:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45648 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.023秒
2025-06-01 18:28:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45662 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.022秒
2025-06-01 18:28:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45678 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.024秒
2025-06-01 18:28:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45680 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.023秒
2025-06-01 18:28:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45690 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.023秒
2025-06-01 18:28:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45698 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.022秒
2025-06-01 18:28:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45702 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.023秒
2025-06-01 18:28:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45704 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.024秒
2025-06-01 18:28:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45708 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.033秒
2025-06-01 18:28:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.021秒
2025-06-01 18:28:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45712 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.024秒
2025-06-01 18:28:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.021秒
2025-06-01 18:28:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45718 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.028秒
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.023秒
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 4个文本, 耗时: 0.023秒
2025-06-01 18:28:47 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45726 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.024秒
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.024秒
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 4个文本, 耗时: 0.022秒
2025-06-01 18:28:47 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45738 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.027秒
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.022秒
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.023秒
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.028秒
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.025秒
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.022秒
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.022秒
2025-06-01 18:28:47 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45746 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.026秒
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.022秒
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.023秒
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.022秒
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.021秒
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.023秒
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.020秒
2025-06-01 18:28:47 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45756 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.028秒
2025-06-01 18:28:47 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45766 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:28:51 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:41326 - "GET /health HTTP/1.1" 200
2025-06-01 18:29:21 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:38656 - "GET /health HTTP/1.1" 200
2025-06-01 18:29:51 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:40958 - "GET /health HTTP/1.1" 200
2025-06-01 18:30:21 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:35578 - "GET /health HTTP/1.1" 200
2025-06-01 18:30:51 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:33344 - "GET /health HTTP/1.1" 200
2025-06-01 18:31:21 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:45132 - "GET /health HTTP/1.1" 200
2025-06-01 18:31:51 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:52128 - "GET /health HTTP/1.1" 200
2025-06-01 18:32:21 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:39552 - "GET /health HTTP/1.1" 200
2025-06-01 18:32:52 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:56602 - "GET /health HTTP/1.1" 200
2025-06-01 18:33:22 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:39700 - "GET /health HTTP/1.1" 200
2025-06-01 18:33:42 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:60802 - "GET /health HTTP/1.1" 200
2025-06-01 18:33:42 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.319秒
2025-06-01 18:33:42 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:60806 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:33:52 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:43668 - "GET /health HTTP/1.1" 200
2025-06-01 18:34:22 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:55588 - "GET /health HTTP/1.1" 200
2025-06-01 18:34:34 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.309秒
2025-06-01 18:34:34 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:33552 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:34:52 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:58810 - "GET /health HTTP/1.1" 200
2025-06-01 18:35:22 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:35008 - "GET /health HTTP/1.1" 200
2025-06-01 18:35:52 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:32788 - "GET /health HTTP/1.1" 200
2025-06-01 18:36:22 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:53940 - "GET /health HTTP/1.1" 200
2025-06-01 18:36:52 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:56092 - "GET /health HTTP/1.1" 200
2025-06-01 18:37:22 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:52844 - "GET /health HTTP/1.1" 200
2025-06-01 18:37:28 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:51062 - "GET /health HTTP/1.1" 200
2025-06-01 18:37:30 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 5个文本, 耗时: 0.026秒
2025-06-01 18:37:30 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:51064 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:37:52 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:42548 - "GET /health HTTP/1.1" 200
2025-06-01 18:38:22 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:59524 - "GET /health HTTP/1.1" 200
2025-06-01 18:38:32 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:51534 - "GET /health HTTP/1.1" 200
2025-06-01 18:38:34 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 5个文本, 耗时: 0.275秒
2025-06-01 18:38:34 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:51544 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:38:52 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:37152 - "GET /health HTTP/1.1" 200
2025-06-01 18:39:10 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50062 - "GET /health HTTP/1.1" 200
2025-06-01 18:39:10 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.304秒
2025-06-01 18:39:10 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50072 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:39:11 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50074 - "GET /health HTTP/1.1" 200
2025-06-01 18:39:11 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.028秒
2025-06-01 18:39:11 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50076 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:39:22 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:40168 - "GET /health HTTP/1.1" 200
2025-06-01 18:39:44 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:39520 - "GET /health HTTP/1.1" 200
2025-06-01 18:39:46 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 5个文本, 耗时: 0.169秒
2025-06-01 18:39:46 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:37042 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:39:52 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:51120 - "GET /health HTTP/1.1" 200
2025-06-01 18:40:21 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:33748 - "GET /health HTTP/1.1" 200
2025-06-01 18:40:21 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.301秒
2025-06-01 18:40:21 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:33758 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:40:22 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:55866 - "GET /health HTTP/1.1" 200
2025-06-01 18:40:52 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:49290 - "GET /health HTTP/1.1" 200
2025-06-01 18:41:09 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45412 - "GET /health HTTP/1.1" 200
2025-06-01 18:41:11 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 5个文本, 耗时: 0.345秒
2025-06-01 18:41:11 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45428 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:41:22 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:34828 - "GET /health HTTP/1.1" 200
2025-06-01 18:41:49 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:46178 - "GET /health HTTP/1.1" 200
2025-06-01 18:41:49 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.253秒
2025-06-01 18:41:49 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:46180 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:41:52 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:35414 - "GET /health HTTP/1.1" 200
2025-06-01 18:42:22 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:49844 - "GET /health HTTP/1.1" 200
2025-06-01 18:42:31 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45506 - "GET /health HTTP/1.1" 200
2025-06-01 18:42:31 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.024秒
2025-06-01 18:42:31 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45518 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:42:41 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:47276 - "GET /health HTTP/1.1" 200
2025-06-01 18:42:43 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 5个文本, 耗时: 0.240秒
2025-06-01 18:42:43 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:47284 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:42:52 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:60670 - "GET /health HTTP/1.1" 200
2025-06-01 18:43:22 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:47220 - "GET /health HTTP/1.1" 200
2025-06-01 18:43:52 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:46062 - "GET /health HTTP/1.1" 200
2025-06-01 18:44:02 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:60950 - "GET /health HTTP/1.1" 200
2025-06-01 18:44:04 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 5个文本, 耗时: 0.025秒
2025-06-01 18:44:04 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:60958 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:44:22 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:51646 - "GET /health HTTP/1.1" 200
2025-06-01 18:44:39 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45188 - "GET /health HTTP/1.1" 200
2025-06-01 18:44:45 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 5个文本, 耗时: 0.394秒
2025-06-01 18:44:45 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:45192 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:44:50 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.030秒
2025-06-01 18:44:50 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50416 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:44:50 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.020秒
2025-06-01 18:44:50 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50424 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:44:50 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.035秒
2025-06-01 18:44:50 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50432 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:44:50 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.023秒
2025-06-01 18:44:50 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50446 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:44:50 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.021秒
2025-06-01 18:44:50 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50456 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:44:50 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.021秒
2025-06-01 18:44:50 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50462 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:44:51 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.025秒
2025-06-01 18:44:51 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50476 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:44:51 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.023秒
2025-06-01 18:44:51 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50492 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:44:51 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.024秒
2025-06-01 18:44:51 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50496 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:44:51 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.021秒
2025-06-01 18:44:51 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50508 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:44:51 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.025秒
2025-06-01 18:44:51 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50520 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:44:51 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.023秒
2025-06-01 18:44:51 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50524 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:44:51 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.023秒
2025-06-01 18:44:51 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50532 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:44:51 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.021秒
2025-06-01 18:44:51 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50536 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:44:51 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.027秒
2025-06-01 18:44:51 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50552 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:44:51 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.023秒
2025-06-01 18:44:51 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50566 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:44:51 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.022秒
2025-06-01 18:44:51 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:50568 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:44:52 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:47160 - "GET /health HTTP/1.1" 200
2025-06-01 18:45:03 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:58562 - "GET /health HTTP/1.1" 200
2025-06-01 18:45:03 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:58576 - "GET /health HTTP/1.1" 200
2025-06-01 18:45:03 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:58584 - "GET /health HTTP/1.1" 200
2025-06-01 18:45:03 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.247秒
2025-06-01 18:45:03 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:58590 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:10 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 7个文本, 耗时: 0.289秒
2025-06-01 18:45:10 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:35826 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:10 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 7个文本, 耗时: 0.033秒
2025-06-01 18:45:10 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:35834 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:11 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.035秒
2025-06-01 18:45:11 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:35838 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:14 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.046秒
2025-06-01 18:45:14 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:35848 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:17 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.272秒
2025-06-01 18:45:17 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56500 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:20 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.219秒
2025-06-01 18:45:20 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56512 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:22 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:41138 - "GET /health HTTP/1.1" 200
2025-06-01 18:45:24 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.038秒
2025-06-01 18:45:24 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56514 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:24 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.039秒
2025-06-01 18:45:24 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56516 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:24 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.033秒
2025-06-01 18:45:24 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56532 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:24 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.032秒
2025-06-01 18:45:24 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56534 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:24 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.038秒
2025-06-01 18:45:24 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56546 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:24 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.034秒
2025-06-01 18:45:24 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56554 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:24 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.026秒
2025-06-01 18:45:24 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56568 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:24 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.026秒
2025-06-01 18:45:24 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56576 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:24 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.026秒
2025-06-01 18:45:24 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56584 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:24 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.035秒
2025-06-01 18:45:24 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56590 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:24 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.030秒
2025-06-01 18:45:24 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56606 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:24 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.027秒
2025-06-01 18:45:24 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56612 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:24 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.028秒
2025-06-01 18:45:24 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56616 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:24 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.029秒
2025-06-01 18:45:24 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56622 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:24 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.031秒
2025-06-01 18:45:24 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56626 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:24 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.032秒
2025-06-01 18:45:24 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56640 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:24 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.038秒
2025-06-01 18:45:25 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.027秒
2025-06-01 18:45:25 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56654 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:25 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.035秒
2025-06-01 18:45:25 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.029秒
2025-06-01 18:45:25 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56658 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:28 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.061秒
2025-06-01 18:45:28 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.052秒
2025-06-01 18:45:28 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 4个文本, 耗时: 0.040秒
2025-06-01 18:45:28 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:35474 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:28 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.051秒
2025-06-01 18:45:28 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.065秒
2025-06-01 18:45:28 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 4个文本, 耗时: 0.034秒
2025-06-01 18:45:28 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:35478 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:36 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.250秒
2025-06-01 18:45:36 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.032秒
2025-06-01 18:45:36 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.025秒
2025-06-01 18:45:36 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.024秒
2025-06-01 18:45:37 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.031秒
2025-06-01 18:45:37 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.022秒
2025-06-01 18:45:37 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.031秒
2025-06-01 18:45:37 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:52566 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:37 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.028秒
2025-06-01 18:45:37 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.022秒
2025-06-01 18:45:37 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.030秒
2025-06-01 18:45:37 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.024秒
2025-06-01 18:45:37 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.024秒
2025-06-01 18:45:37 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 8个文本, 耗时: 0.022秒
2025-06-01 18:45:37 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.028秒
2025-06-01 18:45:37 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:52576 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:47 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.026秒
2025-06-01 18:45:47 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:59874 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:45:52 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:56814 - "GET /health HTTP/1.1" 200
2025-06-01 18:46:22 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:49874 - "GET /health HTTP/1.1" 200
2025-06-01 18:46:52 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:41906 - "GET /health HTTP/1.1" 200
2025-06-01 18:47:22 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:45964 - "GET /health HTTP/1.1" 200
2025-06-01 18:47:52 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:43146 - "GET /health HTTP/1.1" 200
2025-06-01 18:48:13 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56740 - "GET /docs HTTP/1.1" 200
2025-06-01 18:48:13 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56740 - "GET /openapi.json HTTP/1.1" 200
2025-06-01 18:48:16 [INFO] main [main.py:197] - 稀疏嵌入编码完成: 1个文本, 耗时: 0.333秒
2025-06-01 18:48:16 [INFO] uvicorn.access [h11_impl.py:474] - 172.17.0.1:56740 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 18:48:22 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:35272 - "GET /health HTTP/1.1" 200
2025-06-01 18:48:53 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:33542 - "GET /health HTTP/1.1" 200
2025-06-01 18:49:23 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:42324 - "GET /health HTTP/1.1" 200
2025-06-01 18:49:53 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:46464 - "GET /health HTTP/1.1" 200
2025-06-01 18:50:23 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:43966 - "GET /health HTTP/1.1" 200
2025-06-01 18:50:53 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:38686 - "GET /health HTTP/1.1" 200
2025-06-01 18:51:23 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:44116 - "GET /health HTTP/1.1" 200
2025-06-01 18:51:53 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:59706 - "GET /health HTTP/1.1" 200
2025-06-01 18:52:23 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:58904 - "GET /health HTTP/1.1" 200
2025-06-01 18:52:53 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:51836 - "GET /health HTTP/1.1" 200
2025-06-01 18:53:23 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:45196 - "GET /health HTTP/1.1" 200
2025-06-01 18:53:53 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:56224 - "GET /health HTTP/1.1" 200
2025-06-01 18:54:23 [INFO] uvicorn.access [h11_impl.py:474] - 127.0.0.1:56852 - "GET /health HTTP/1.1" 200
