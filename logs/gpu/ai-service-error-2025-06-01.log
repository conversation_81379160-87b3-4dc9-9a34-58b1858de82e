2025-06-01 17:22:50 [ERROR] main [main.py:122] - 模型初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:22:50 [ERROR] main [main.py:266] - 服务初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:22:50 [ERROR] uvicorn.error [on.py:121] - Traceback (most recent call last):
  File "/app/start_optimized.py", line 113, in start_service
    uvicorn.run(
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/main.py", line 587, in run
    server.run()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    self.config.setup_event_loop()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/config.py", line 502, in setup_event_loop
    loop_setup: Callable | None = import_from_string(LOOP_SETUPS[self.loop])
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/loops/uvloop.py", line 3, in <module>
    import uvloop
ModuleNotFoundError: No module named 'uvloop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 470, in cached_files
    hf_hub_download(
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/opt/conda/lib/python3.11/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 262, in lifespan
    sparse_model = BGEMSparseEmbeddingService()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 90, in __init__
    self._initialize_model()
  File "/app/main.py", line 110, in _initialize_model
    self.model = BGEM3FlagModel(
                 ^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/FlagEmbedding/inference/embedder/encoder_only/m3.py", line 89, in __init__
    self.tokenizer = AutoTokenizer.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 950, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 782, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 312, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 522, in cached_files
    resolved_files = [
                     ^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 523, in <listcomp>
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision) for filename in full_filenames
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 140, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(path_or_repo_id, full_filename, cache_dir=cache_dir, revision=revision)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

2025-06-01 17:22:50 [ERROR] uvicorn.error [on.py:57] - Application startup failed. Exiting.
2025-06-01 17:22:56 [ERROR] main [main.py:122] - 模型初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:22:56 [ERROR] main [main.py:266] - 服务初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:22:56 [ERROR] uvicorn.error [on.py:121] - Traceback (most recent call last):
  File "/app/start_optimized.py", line 113, in start_service
    uvicorn.run(
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/main.py", line 587, in run
    server.run()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    self.config.setup_event_loop()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/config.py", line 502, in setup_event_loop
    loop_setup: Callable | None = import_from_string(LOOP_SETUPS[self.loop])
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/loops/uvloop.py", line 3, in <module>
    import uvloop
ModuleNotFoundError: No module named 'uvloop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 470, in cached_files
    hf_hub_download(
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/opt/conda/lib/python3.11/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 262, in lifespan
    sparse_model = BGEMSparseEmbeddingService()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 90, in __init__
    self._initialize_model()
  File "/app/main.py", line 110, in _initialize_model
    self.model = BGEM3FlagModel(
                 ^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/FlagEmbedding/inference/embedder/encoder_only/m3.py", line 89, in __init__
    self.tokenizer = AutoTokenizer.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 950, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 782, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 312, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 522, in cached_files
    resolved_files = [
                     ^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 523, in <listcomp>
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision) for filename in full_filenames
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 140, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(path_or_repo_id, full_filename, cache_dir=cache_dir, revision=revision)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

2025-06-01 17:22:56 [ERROR] uvicorn.error [on.py:57] - Application startup failed. Exiting.
2025-06-01 17:23:02 [ERROR] main [main.py:122] - 模型初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:02 [ERROR] main [main.py:266] - 服务初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:02 [ERROR] uvicorn.error [on.py:121] - Traceback (most recent call last):
  File "/app/start_optimized.py", line 113, in start_service
    uvicorn.run(
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/main.py", line 587, in run
    server.run()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    self.config.setup_event_loop()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/config.py", line 502, in setup_event_loop
    loop_setup: Callable | None = import_from_string(LOOP_SETUPS[self.loop])
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/loops/uvloop.py", line 3, in <module>
    import uvloop
ModuleNotFoundError: No module named 'uvloop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 470, in cached_files
    hf_hub_download(
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/opt/conda/lib/python3.11/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 262, in lifespan
    sparse_model = BGEMSparseEmbeddingService()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 90, in __init__
    self._initialize_model()
  File "/app/main.py", line 110, in _initialize_model
    self.model = BGEM3FlagModel(
                 ^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/FlagEmbedding/inference/embedder/encoder_only/m3.py", line 89, in __init__
    self.tokenizer = AutoTokenizer.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 950, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 782, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 312, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 522, in cached_files
    resolved_files = [
                     ^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 523, in <listcomp>
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision) for filename in full_filenames
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 140, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(path_or_repo_id, full_filename, cache_dir=cache_dir, revision=revision)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

2025-06-01 17:23:02 [ERROR] uvicorn.error [on.py:57] - Application startup failed. Exiting.
2025-06-01 17:23:07 [ERROR] main [main.py:122] - 模型初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:07 [ERROR] main [main.py:266] - 服务初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:07 [ERROR] uvicorn.error [on.py:121] - Traceback (most recent call last):
  File "/app/start_optimized.py", line 113, in start_service
    uvicorn.run(
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/main.py", line 587, in run
    server.run()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    self.config.setup_event_loop()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/config.py", line 502, in setup_event_loop
    loop_setup: Callable | None = import_from_string(LOOP_SETUPS[self.loop])
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/loops/uvloop.py", line 3, in <module>
    import uvloop
ModuleNotFoundError: No module named 'uvloop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 470, in cached_files
    hf_hub_download(
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/opt/conda/lib/python3.11/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 262, in lifespan
    sparse_model = BGEMSparseEmbeddingService()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 90, in __init__
    self._initialize_model()
  File "/app/main.py", line 110, in _initialize_model
    self.model = BGEM3FlagModel(
                 ^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/FlagEmbedding/inference/embedder/encoder_only/m3.py", line 89, in __init__
    self.tokenizer = AutoTokenizer.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 950, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 782, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 312, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 522, in cached_files
    resolved_files = [
                     ^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 523, in <listcomp>
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision) for filename in full_filenames
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 140, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(path_or_repo_id, full_filename, cache_dir=cache_dir, revision=revision)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

2025-06-01 17:23:07 [ERROR] uvicorn.error [on.py:57] - Application startup failed. Exiting.
2025-06-01 17:23:12 [ERROR] main [main.py:122] - 模型初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:12 [ERROR] main [main.py:266] - 服务初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:12 [ERROR] uvicorn.error [on.py:121] - Traceback (most recent call last):
  File "/app/start_optimized.py", line 113, in start_service
    uvicorn.run(
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/main.py", line 587, in run
    server.run()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    self.config.setup_event_loop()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/config.py", line 502, in setup_event_loop
    loop_setup: Callable | None = import_from_string(LOOP_SETUPS[self.loop])
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/loops/uvloop.py", line 3, in <module>
    import uvloop
ModuleNotFoundError: No module named 'uvloop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 470, in cached_files
    hf_hub_download(
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/opt/conda/lib/python3.11/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 262, in lifespan
    sparse_model = BGEMSparseEmbeddingService()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 90, in __init__
    self._initialize_model()
  File "/app/main.py", line 110, in _initialize_model
    self.model = BGEM3FlagModel(
                 ^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/FlagEmbedding/inference/embedder/encoder_only/m3.py", line 89, in __init__
    self.tokenizer = AutoTokenizer.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 950, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 782, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 312, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 522, in cached_files
    resolved_files = [
                     ^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 523, in <listcomp>
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision) for filename in full_filenames
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 140, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(path_or_repo_id, full_filename, cache_dir=cache_dir, revision=revision)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

2025-06-01 17:23:13 [ERROR] uvicorn.error [on.py:57] - Application startup failed. Exiting.
2025-06-01 17:23:19 [ERROR] main [main.py:122] - 模型初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:19 [ERROR] main [main.py:266] - 服务初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:19 [ERROR] uvicorn.error [on.py:121] - Traceback (most recent call last):
  File "/app/start_optimized.py", line 113, in start_service
    uvicorn.run(
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/main.py", line 587, in run
    server.run()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    self.config.setup_event_loop()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/config.py", line 502, in setup_event_loop
    loop_setup: Callable | None = import_from_string(LOOP_SETUPS[self.loop])
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/loops/uvloop.py", line 3, in <module>
    import uvloop
ModuleNotFoundError: No module named 'uvloop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 470, in cached_files
    hf_hub_download(
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/opt/conda/lib/python3.11/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 262, in lifespan
    sparse_model = BGEMSparseEmbeddingService()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 90, in __init__
    self._initialize_model()
  File "/app/main.py", line 110, in _initialize_model
    self.model = BGEM3FlagModel(
                 ^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/FlagEmbedding/inference/embedder/encoder_only/m3.py", line 89, in __init__
    self.tokenizer = AutoTokenizer.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 950, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 782, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 312, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 522, in cached_files
    resolved_files = [
                     ^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 523, in <listcomp>
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision) for filename in full_filenames
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 140, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(path_or_repo_id, full_filename, cache_dir=cache_dir, revision=revision)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

2025-06-01 17:23:19 [ERROR] uvicorn.error [on.py:57] - Application startup failed. Exiting.
2025-06-01 17:23:28 [ERROR] main [main.py:122] - 模型初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:28 [ERROR] main [main.py:266] - 服务初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:28 [ERROR] uvicorn.error [on.py:121] - Traceback (most recent call last):
  File "/app/start_optimized.py", line 113, in start_service
    uvicorn.run(
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/main.py", line 587, in run
    server.run()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    self.config.setup_event_loop()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/config.py", line 502, in setup_event_loop
    loop_setup: Callable | None = import_from_string(LOOP_SETUPS[self.loop])
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/loops/uvloop.py", line 3, in <module>
    import uvloop
ModuleNotFoundError: No module named 'uvloop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 470, in cached_files
    hf_hub_download(
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/opt/conda/lib/python3.11/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 262, in lifespan
    sparse_model = BGEMSparseEmbeddingService()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 90, in __init__
    self._initialize_model()
  File "/app/main.py", line 110, in _initialize_model
    self.model = BGEM3FlagModel(
                 ^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/FlagEmbedding/inference/embedder/encoder_only/m3.py", line 89, in __init__
    self.tokenizer = AutoTokenizer.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 950, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 782, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 312, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 522, in cached_files
    resolved_files = [
                     ^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 523, in <listcomp>
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision) for filename in full_filenames
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 140, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(path_or_repo_id, full_filename, cache_dir=cache_dir, revision=revision)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

2025-06-01 17:23:28 [ERROR] uvicorn.error [on.py:57] - Application startup failed. Exiting.
2025-06-01 17:23:41 [ERROR] main [main.py:122] - 模型初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:41 [ERROR] main [main.py:266] - 服务初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:41 [ERROR] uvicorn.error [on.py:121] - Traceback (most recent call last):
  File "/app/start_optimized.py", line 113, in start_service
    uvicorn.run(
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/main.py", line 587, in run
    server.run()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    self.config.setup_event_loop()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/config.py", line 502, in setup_event_loop
    loop_setup: Callable | None = import_from_string(LOOP_SETUPS[self.loop])
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/loops/uvloop.py", line 3, in <module>
    import uvloop
ModuleNotFoundError: No module named 'uvloop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 470, in cached_files
    hf_hub_download(
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/opt/conda/lib/python3.11/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 262, in lifespan
    sparse_model = BGEMSparseEmbeddingService()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 90, in __init__
    self._initialize_model()
  File "/app/main.py", line 110, in _initialize_model
    self.model = BGEM3FlagModel(
                 ^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/FlagEmbedding/inference/embedder/encoder_only/m3.py", line 89, in __init__
    self.tokenizer = AutoTokenizer.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 950, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 782, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 312, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 522, in cached_files
    resolved_files = [
                     ^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 523, in <listcomp>
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision) for filename in full_filenames
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 140, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(path_or_repo_id, full_filename, cache_dir=cache_dir, revision=revision)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

2025-06-01 17:23:41 [ERROR] uvicorn.error [on.py:57] - Application startup failed. Exiting.
2025-06-01 17:23:59 [ERROR] main [main.py:122] - 模型初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:59 [ERROR] main [main.py:266] - 服务初始化失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.
2025-06-01 17:23:59 [ERROR] uvicorn.error [on.py:121] - Traceback (most recent call last):
  File "/app/start_optimized.py", line 113, in start_service
    uvicorn.run(
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/main.py", line 587, in run
    server.run()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    self.config.setup_event_loop()
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/config.py", line 502, in setup_event_loop
    loop_setup: Callable | None = import_from_string(LOOP_SETUPS[self.loop])
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/conda/lib/python3.11/site-packages/uvicorn/loops/uvloop.py", line 3, in <module>
    import uvloop
ModuleNotFoundError: No module named 'uvloop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 470, in cached_files
    hf_hub_download(
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/conda/lib/python3.11/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/opt/conda/lib/python3.11/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 262, in lifespan
    sparse_model = BGEMSparseEmbeddingService()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/main.py", line 90, in __init__
    self._initialize_model()
  File "/app/main.py", line 110, in _initialize_model
    self.model = BGEM3FlagModel(
                 ^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/FlagEmbedding/inference/embedder/encoder_only/m3.py", line 89, in __init__
    self.tokenizer = AutoTokenizer.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 950, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 782, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 312, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 522, in cached_files
    resolved_files = [
                     ^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 523, in <listcomp>
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision) for filename in full_filenames
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/transformers/utils/hub.py", line 140, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(path_or_repo_id, full_filename, cache_dir=cache_dir, revision=revision)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/opt/conda/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/app/models/bge-m3'. Use `repo_type` argument if needed.

2025-06-01 17:23:59 [ERROR] uvicorn.error [on.py:57] - Application startup failed. Exiting.
