2025-06-08 05:14:15 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-08 05:14:15 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-08.log
2025-06-08 05:14:15 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-08.log
2025-06-08 05:14:15 [INFO] uvicorn.error [server.py:76] - Started server process [1]
2025-06-08 05:14:15 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-08 05:14:15 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-08 05:14:15 [INFO] main [main.py:83] - 环境: production
2025-06-08 05:14:15 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-08 05:14:15 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-08 05:14:15 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-08 05:14:15 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'deprecated'
2025-06-08 05:14:15 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-08 05:14:15 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-08 05:14:15 [INFO] uvicorn.error [server.py:218] - Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
2025-06-08 05:14:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42916 - "GET /health HTTP/1.1" 200
2025-06-08 05:14:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51660 - "GET /health HTTP/1.1" 200
2025-06-08 05:15:08 [INFO] uvicorn.access [httptools_impl.py:496] - 172.22.0.1:34028 - "GET /health HTTP/1.1" 200
2025-06-08 05:15:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53140 - "GET /health HTTP/1.1" 200
2025-06-08 05:15:18 [INFO] uvicorn.access [httptools_impl.py:496] - 172.22.0.1:53268 - "GET /docs HTTP/1.1" 200
2025-06-08 05:15:32 [INFO] uvicorn.access [httptools_impl.py:496] - 172.22.0.1:58422 - "POST /v2/chat/completions HTTP/1.1" 404
2025-06-08 05:15:43 [INFO] uvicorn.access [httptools_impl.py:496] - 172.22.0.1:48862 - "GET /openapi.json HTTP/1.1" 200
2025-06-08 05:15:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53418 - "GET /health HTTP/1.1" 200
2025-06-08 05:16:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39736 - "GET /health HTTP/1.1" 200
2025-06-08 05:16:17 [INFO] uvicorn.access [httptools_impl.py:496] - 172.22.0.1:58438 - "GET /health HTTP/1.1" 200
2025-06-08 05:16:17 [INFO] uvicorn.access [httptools_impl.py:496] - 172.22.0.1:58452 - "GET /openapi.json HTTP/1.1" 200
2025-06-08 05:16:17 [INFO] uvicorn.access [httptools_impl.py:496] - 172.22.0.1:58460 - "GET / HTTP/1.1" 200
2025-06-08 05:16:17 [INFO] uvicorn.access [httptools_impl.py:496] - 172.22.0.1:58462 - "GET /docs HTTP/1.1" 200
2025-06-08 05:16:28 [INFO] uvicorn.access [httptools_impl.py:496] - 172.22.0.1:60238 - "GET /v2/ HTTP/1.1" 200
2025-06-08 05:16:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49566 - "GET /health HTTP/1.1" 200
2025-06-08 05:17:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40740 - "GET /health HTTP/1.1" 200
2025-06-08 05:17:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38154 - "GET /health HTTP/1.1" 200
2025-06-08 05:18:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38102 - "GET /health HTTP/1.1" 200
2025-06-08 05:18:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45228 - "GET /health HTTP/1.1" 200
2025-06-08 05:19:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60732 - "GET /health HTTP/1.1" 200
2025-06-08 05:19:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43228 - "GET /health HTTP/1.1" 200
2025-06-08 05:20:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57290 - "GET /health HTTP/1.1" 200
2025-06-08 05:20:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38246 - "GET /health HTTP/1.1" 200
2025-06-08 05:21:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34096 - "GET /health HTTP/1.1" 200
2025-06-08 05:21:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46134 - "GET /health HTTP/1.1" 200
2025-06-08 05:22:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49894 - "GET /health HTTP/1.1" 200
2025-06-08 05:22:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50198 - "GET /health HTTP/1.1" 200
2025-06-08 05:23:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40008 - "GET /health HTTP/1.1" 200
2025-06-08 05:23:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56144 - "GET /health HTTP/1.1" 200
2025-06-08 05:24:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35608 - "GET /health HTTP/1.1" 200
2025-06-08 05:24:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35042 - "GET /health HTTP/1.1" 200
2025-06-08 05:25:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39980 - "GET /health HTTP/1.1" 200
2025-06-08 05:25:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49794 - "GET /health HTTP/1.1" 200
2025-06-08 05:26:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48348 - "GET /health HTTP/1.1" 200
2025-06-08 05:26:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41566 - "GET /health HTTP/1.1" 200
2025-06-08 05:27:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54076 - "GET /health HTTP/1.1" 200
2025-06-08 05:27:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44146 - "GET /health HTTP/1.1" 200
2025-06-08 05:28:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47602 - "GET /health HTTP/1.1" 200
2025-06-08 05:28:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35172 - "GET /health HTTP/1.1" 200
2025-06-08 05:29:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37918 - "GET /health HTTP/1.1" 200
2025-06-08 05:29:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44680 - "GET /health HTTP/1.1" 200
2025-06-08 05:30:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53284 - "GET /health HTTP/1.1" 200
2025-06-08 05:30:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43576 - "GET /health HTTP/1.1" 200
2025-06-08 05:31:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56020 - "GET /health HTTP/1.1" 200
2025-06-08 05:31:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41270 - "GET /health HTTP/1.1" 200
2025-06-08 05:32:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54264 - "GET /health HTTP/1.1" 200
2025-06-08 05:32:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33836 - "GET /health HTTP/1.1" 200
2025-06-08 05:33:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40840 - "GET /health HTTP/1.1" 200
2025-06-08 05:33:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45494 - "GET /health HTTP/1.1" 200
2025-06-08 05:34:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44896 - "GET /health HTTP/1.1" 200
2025-06-08 05:34:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42242 - "GET /health HTTP/1.1" 200
2025-06-08 05:35:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42266 - "GET /health HTTP/1.1" 200
2025-06-08 05:35:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42282 - "GET /health HTTP/1.1" 200
2025-06-08 05:36:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42926 - "GET /health HTTP/1.1" 200
2025-06-08 05:36:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50958 - "GET /health HTTP/1.1" 200
2025-06-08 05:37:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58596 - "GET /health HTTP/1.1" 200
2025-06-08 05:37:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45628 - "GET /health HTTP/1.1" 200
2025-06-08 05:38:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54698 - "GET /health HTTP/1.1" 200
2025-06-08 05:38:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43624 - "GET /health HTTP/1.1" 200
2025-06-08 05:39:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56780 - "GET /health HTTP/1.1" 200
2025-06-08 05:39:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43130 - "GET /health HTTP/1.1" 200
2025-06-08 05:40:17 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46954 - "GET /health HTTP/1.1" 200
2025-06-08 05:40:47 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56962 - "GET /health HTTP/1.1" 200
2025-06-08 05:41:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41950 - "GET /health HTTP/1.1" 200
2025-06-08 05:41:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53172 - "GET /health HTTP/1.1" 200
2025-06-08 05:42:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39172 - "GET /health HTTP/1.1" 200
2025-06-08 05:42:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60506 - "GET /health HTTP/1.1" 200
2025-06-08 05:43:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59034 - "GET /health HTTP/1.1" 200
2025-06-08 05:43:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47924 - "GET /health HTTP/1.1" 200
2025-06-08 05:44:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41910 - "GET /health HTTP/1.1" 200
2025-06-08 05:44:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49612 - "GET /health HTTP/1.1" 200
2025-06-08 05:45:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60560 - "GET /health HTTP/1.1" 200
2025-06-08 05:45:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44122 - "GET /health HTTP/1.1" 200
2025-06-08 05:46:18 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46716 - "GET /health HTTP/1.1" 200
2025-06-08 05:46:48 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60648 - "GET /health HTTP/1.1" 200
2025-06-08 05:47:03 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-08 05:47:03 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-08 05:47:03 [INFO] main [main.py:121] - CPU服务关闭
2025-06-08 05:47:03 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-08 05:47:03 [INFO] uvicorn.error [server.py:86] - Finished server process [1]
