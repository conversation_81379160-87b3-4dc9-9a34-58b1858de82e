2025-06-01 05:18:20 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 05:18:20 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\ai-service-2025-06-01.log
2025-06-01 05:18:20 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\ai-service-error-2025-06-01.log
2025-06-01 05:18:20 [INFO] __main__ [main.py:305] - 启动BGE-M3稀疏嵌入服务: 0.0.0.0:8001
2025-06-01 05:18:20 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 05:18:20 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\ai-service-2025-06-01.log
2025-06-01 05:18:20 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\ai-service-error-2025-06-01.log
2025-06-01 05:18:20 [INFO] uvicorn.error [server.py:76] - Started server process [14468]
2025-06-01 05:18:20 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-01 05:18:20 [INFO] main [main.py:177] - 初始化BGE-M3稀疏嵌入服务...
2025-06-01 05:18:23 [INFO] datasets [config.py:54] - PyTorch version 2.6.0+cu126 available.
2025-06-01 05:18:25 [INFO] main [main.py:102] - 使用GPU: NVIDIA GeForce RTX 4060 Laptop GPU, 内存: 8.0GB
2025-06-01 05:18:25 [INFO] main [main.py:108] - 加载BGE-M3模型: BAAI/bge-m3
2025-06-01 05:18:27 [INFO] FlagEmbedding.finetune.embedder.encoder_only.m3.runner [runner.py:86] - loading existing colbert_linear and sparse_linear---------
2025-06-01 05:18:27 [INFO] main [main.py:114] - BGE-M3模型加载成功
2025-06-01 05:18:27 [INFO] main [main.py:179] - BGE-M3稀疏嵌入服务初始化完成
2025-06-01 05:18:27 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-01 05:18:27 [INFO] uvicorn.error [server.py:218] - Uvicorn running on http://0.0.0.0:8001 (Press CTRL+C to quit)
2025-06-01 05:19:26 [ERROR] main [main.py:167] - 获取GPU信息失败: pynvml does not seem to be installed or it can't be imported.
2025-06-01 05:19:26 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:55268 - "GET /health HTTP/1.1" 200
2025-06-01 05:19:36 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-01 05:19:37 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-01 05:19:37 [INFO] main [main.py:188] - BGE-M3稀疏嵌入服务关闭
2025-06-01 05:19:37 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-01 05:19:37 [INFO] uvicorn.error [server.py:86] - Finished server process [14468]
2025-06-01 05:20:50 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 05:20:50 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\ai-service-2025-06-01.log
2025-06-01 05:20:50 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\ai-service-error-2025-06-01.log
2025-06-01 05:20:50 [INFO] __main__ [main.py:305] - 启动BGE-M3稀疏嵌入服务: 0.0.0.0:8001
2025-06-01 05:20:50 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 05:20:50 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\ai-service-2025-06-01.log
2025-06-01 05:20:50 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\ai-service-error-2025-06-01.log
2025-06-01 05:20:50 [INFO] uvicorn.error [server.py:76] - Started server process [8692]
2025-06-01 05:20:50 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-01 05:20:50 [INFO] main [main.py:177] - 初始化BGE-M3稀疏嵌入服务...
2025-06-01 05:20:52 [INFO] datasets [config.py:54] - PyTorch version 2.6.0+cu126 available.
2025-06-01 05:20:53 [INFO] main [main.py:102] - 使用GPU: NVIDIA GeForce RTX 4060 Laptop GPU, 内存: 8.0GB
2025-06-01 05:20:53 [INFO] main [main.py:108] - 加载BGE-M3模型: BAAI/bge-m3
2025-06-01 05:20:56 [INFO] FlagEmbedding.finetune.embedder.encoder_only.m3.runner [runner.py:86] - loading existing colbert_linear and sparse_linear---------
2025-06-01 05:20:56 [INFO] main [main.py:114] - BGE-M3模型加载成功
2025-06-01 05:20:56 [INFO] main [main.py:179] - BGE-M3稀疏嵌入服务初始化完成
2025-06-01 05:20:56 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-01 05:20:56 [INFO] uvicorn.error [server.py:218] - Uvicorn running on http://0.0.0.0:8001 (Press CTRL+C to quit)
2025-06-01 05:21:05 [ERROR] main [main.py:167] - 获取GPU信息失败: pynvml does not seem to be installed or it can't be imported.
2025-06-01 05:21:05 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:55363 - "GET /health HTTP/1.1" 200
2025-06-01 05:21:10 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-01 05:21:10 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-01 05:21:10 [INFO] main [main.py:188] - BGE-M3稀疏嵌入服务关闭
2025-06-01 05:21:10 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-01 05:21:10 [INFO] uvicorn.error [server.py:86] - Finished server process [8692]
2025-06-01 05:31:55 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 05:31:55 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\ai-service-2025-06-01.log
2025-06-01 05:31:55 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\ai-service-error-2025-06-01.log
2025-06-01 05:31:55 [INFO] __main__ [main.py:318] - 启动BGE-M3稀疏嵌入服务: 0.0.0.0:8001
2025-06-01 05:31:55 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 05:31:55 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\ai-service-2025-06-01.log
2025-06-01 05:31:55 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\ai-service-error-2025-06-01.log
2025-06-01 05:31:55 [INFO] uvicorn.error [server.py:76] - Started server process [30556]
2025-06-01 05:31:55 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-01 05:31:55 [INFO] main [main.py:190] - 初始化BGE-M3稀疏嵌入服务...
2025-06-01 05:31:58 [INFO] datasets [config.py:54] - PyTorch version 2.6.0+cu126 available.
2025-06-01 05:32:00 [INFO] main [main.py:102] - 使用GPU: NVIDIA GeForce RTX 4060 Laptop GPU, 内存: 8.0GB
2025-06-01 05:32:00 [INFO] main [main.py:108] - 加载BGE-M3模型: BAAI/bge-m3
2025-06-01 05:32:02 [INFO] FlagEmbedding.finetune.embedder.encoder_only.m3.runner [runner.py:86] - loading existing colbert_linear and sparse_linear---------
2025-06-01 05:32:02 [INFO] main [main.py:114] - BGE-M3模型加载成功
2025-06-01 05:32:02 [INFO] main [main.py:192] - BGE-M3稀疏嵌入服务初始化完成
2025-06-01 05:32:02 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-01 05:32:02 [INFO] uvicorn.error [server.py:218] - Uvicorn running on http://0.0.0.0:8001 (Press CTRL+C to quit)
2025-06-01 05:32:28 [ERROR] main [main.py:180] - 获取GPU信息失败: pynvml does not seem to be installed or it can't be imported.
2025-06-01 05:32:28 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:55736 - "GET /health HTTP/1.1" 200
2025-06-01 05:32:47 [ERROR] main [main.py:180] - 获取GPU信息失败: pynvml does not seem to be installed or it can't be imported.
2025-06-01 05:32:47 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:55772 - "GET /health HTTP/1.1" 200
2025-06-01 05:32:53 [ERROR] main [main.py:180] - 获取GPU信息失败: pynvml does not seem to be installed or it can't be imported.
2025-06-01 05:32:53 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:55777 - "GET /health HTTP/1.1" 200
2025-06-01 05:33:59 [ERROR] main [main.py:180] - 获取GPU信息失败: pynvml does not seem to be installed or it can't be imported.
2025-06-01 05:33:59 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:55821 - "GET /health HTTP/1.1" 200
2025-06-01 05:34:03 [ERROR] main [main.py:180] - 获取GPU信息失败: pynvml does not seem to be installed or it can't be imported.
2025-06-01 05:34:03 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:55826 - "GET /health HTTP/1.1" 200
2025-06-01 05:35:33 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-01 05:35:33 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-01 05:35:33 [INFO] main [main.py:201] - BGE-M3稀疏嵌入服务关闭
2025-06-01 05:35:33 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-01 05:35:33 [INFO] uvicorn.error [server.py:86] - Finished server process [30556]
2025-06-01 05:40:26 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 05:40:26 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\ai-service-2025-06-01.log
2025-06-01 05:40:26 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\ai-service-error-2025-06-01.log
2025-06-01 05:40:26 [INFO] __main__ [main.py:318] - 启动BGE-M3稀疏嵌入服务: 0.0.0.0:8001
2025-06-01 05:40:26 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 05:40:26 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\ai-service-2025-06-01.log
2025-06-01 05:40:26 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\ai-service-error-2025-06-01.log
2025-06-01 05:40:26 [INFO] uvicorn.error [server.py:76] - Started server process [38736]
2025-06-01 05:40:26 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-01 05:40:26 [INFO] main [main.py:190] - 初始化BGE-M3稀疏嵌入服务...
2025-06-01 05:40:28 [INFO] datasets [config.py:54] - PyTorch version 2.6.0+cu126 available.
2025-06-01 05:40:30 [INFO] main [main.py:102] - 使用GPU: NVIDIA GeForce RTX 4060 Laptop GPU, 内存: 8.0GB
2025-06-01 05:40:30 [INFO] main [main.py:108] - 加载BGE-M3模型: BAAI/bge-m3
2025-06-01 05:40:33 [INFO] FlagEmbedding.finetune.embedder.encoder_only.m3.runner [runner.py:86] - loading existing colbert_linear and sparse_linear---------
2025-06-01 05:40:33 [INFO] main [main.py:114] - BGE-M3模型加载成功
2025-06-01 05:40:33 [INFO] main [main.py:192] - BGE-M3稀疏嵌入服务初始化完成
2025-06-01 05:40:33 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-01 05:40:33 [INFO] uvicorn.error [server.py:218] - Uvicorn running on http://0.0.0.0:8001 (Press CTRL+C to quit)
2025-06-01 05:41:23 [ERROR] main [main.py:180] - 获取GPU信息失败: pynvml does not seem to be installed or it can't be imported.
2025-06-01 05:41:23 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:55946 - "GET /health HTTP/1.1" 200
2025-06-01 05:42:13 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:55974 - "GET / HTTP/1.1" 200
2025-06-01 05:43:39 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-01 05:43:39 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-01 05:43:39 [INFO] main [main.py:201] - BGE-M3稀疏嵌入服务关闭
2025-06-01 05:43:39 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-01 05:43:39 [INFO] uvicorn.error [server.py:86] - Finished server process [38736]
2025-06-01 05:53:21 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 05:53:21 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\ai-service-2025-06-01.log
2025-06-01 05:53:21 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\ai-service-error-2025-06-01.log
2025-06-01 05:53:21 [INFO] __main__ [main.py:318] - 启动BGE-M3稀疏嵌入服务: 0.0.0.0:8001
2025-06-01 05:53:21 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 05:53:21 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\ai-service-2025-06-01.log
2025-06-01 05:53:21 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\ai-service-error-2025-06-01.log
2025-06-01 05:53:21 [INFO] uvicorn.error [server.py:76] - Started server process [42688]
2025-06-01 05:53:21 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-01 05:53:21 [INFO] main [main.py:190] - 初始化BGE-M3稀疏嵌入服务...
2025-06-01 05:53:24 [INFO] datasets [config.py:54] - PyTorch version 2.6.0+cu126 available.
2025-06-01 05:53:27 [INFO] main [main.py:102] - 使用GPU: NVIDIA GeForce RTX 4060 Laptop GPU, 内存: 8.0GB
2025-06-01 05:53:27 [INFO] main [main.py:108] - 加载BGE-M3模型: BAAI/bge-m3
2025-06-01 05:53:31 [INFO] FlagEmbedding.finetune.embedder.encoder_only.m3.runner [runner.py:86] - loading existing colbert_linear and sparse_linear---------
2025-06-01 05:53:31 [INFO] main [main.py:114] - BGE-M3模型加载成功
2025-06-01 05:53:31 [INFO] main [main.py:192] - BGE-M3稀疏嵌入服务初始化完成
2025-06-01 05:53:31 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-01 05:53:31 [INFO] uvicorn.error [server.py:218] - Uvicorn running on http://0.0.0.0:8001 (Press CTRL+C to quit)
2025-06-01 05:53:56 [ERROR] main [main.py:180] - 获取GPU信息失败: pynvml does not seem to be installed or it can't be imported.
2025-06-01 05:53:56 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:56806 - "GET /health HTTP/1.1" 200
2025-06-01 05:54:27 [ERROR] main [main.py:180] - 获取GPU信息失败: pynvml does not seem to be installed or it can't be imported.
2025-06-01 05:54:27 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:56837 - "GET /health HTTP/1.1" 200
2025-06-01 05:54:33 [ERROR] main [main.py:180] - 获取GPU信息失败: pynvml does not seem to be installed or it can't be imported.
2025-06-01 05:54:33 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:56846 - "GET /health HTTP/1.1" 200
2025-06-01 05:55:03 [ERROR] main [main.py:180] - 获取GPU信息失败: pynvml does not seem to be installed or it can't be imported.
2025-06-01 05:55:03 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:56874 - "GET /health HTTP/1.1" 200
2025-06-01 05:55:07 [INFO] main [main.py:158] - 稀疏嵌入编码完成: 2个文本, 耗时: 1.535秒
2025-06-01 05:55:07 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:56876 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 05:55:19 [ERROR] main [main.py:180] - 获取GPU信息失败: pynvml does not seem to be installed or it can't be imported.
2025-06-01 05:55:19 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:56888 - "GET /health HTTP/1.1" 200
2025-06-01 05:55:21 [INFO] main [main.py:158] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.305秒
2025-06-01 05:55:21 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:56893 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 05:55:24 [ERROR] main [main.py:180] - 获取GPU信息失败: pynvml does not seem to be installed or it can't be imported.
2025-06-01 05:55:24 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:56897 - "GET /health HTTP/1.1" 200
2025-06-01 05:55:26 [INFO] main [main.py:158] - 稀疏嵌入编码完成: 2个文本, 耗时: 0.305秒
2025-06-01 05:55:26 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:56900 - "POST /embeddings/sparse HTTP/1.1" 200
2025-06-01 06:17:16 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-01 06:17:16 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-01 06:17:16 [INFO] main [main.py:201] - BGE-M3稀疏嵌入服务关闭
2025-06-01 06:17:16 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-01 06:17:16 [INFO] uvicorn.error [server.py:86] - Finished server process [42688]
