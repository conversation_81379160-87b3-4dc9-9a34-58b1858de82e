2025-06-06 03:58:02 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 03:58:02 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 03:58:02 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 03:58:02 [INFO] uvicorn.error [server.py:76] - Started server process [14]
2025-06-06 03:58:02 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 03:58:02 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 03:58:02 [INFO] main [main.py:83] - 环境: production
2025-06-06 03:58:02 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 03:58:02 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 03:58:02 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 03:58:02 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'cores.config'
2025-06-06 03:58:02 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 03:58:02 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 03:58:02 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 03:58:02 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 03:58:02 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 03:58:02 [INFO] uvicorn.error [server.py:76] - Started server process [13]
2025-06-06 03:58:02 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 03:58:02 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 03:58:02 [INFO] main [main.py:83] - 环境: production
2025-06-06 03:58:02 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 03:58:02 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 03:58:02 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 03:58:02 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'cores.config'
2025-06-06 03:58:02 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 03:58:02 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 03:58:02 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 03:58:02 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 03:58:02 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 03:58:02 [INFO] uvicorn.error [server.py:76] - Started server process [12]
2025-06-06 03:58:02 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 03:58:02 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 03:58:02 [INFO] main [main.py:83] - 环境: production
2025-06-06 03:58:02 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 03:58:02 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 03:58:02 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 03:58:02 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'cores.config'
2025-06-06 03:58:02 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 03:58:02 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 03:58:02 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 03:58:02 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 03:58:02 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 03:58:02 [INFO] uvicorn.error [server.py:76] - Started server process [11]
2025-06-06 03:58:02 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 03:58:02 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 03:58:02 [INFO] main [main.py:83] - 环境: production
2025-06-06 03:58:02 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 03:58:02 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 03:58:02 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 03:58:02 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'cores.config'
2025-06-06 03:58:02 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 03:58:02 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 03:58:04 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54476 - "GET /health HTTP/1.1" 200
2025-06-06 03:58:24 [INFO] uvicorn.access [httptools_impl.py:496] - **********:43326 - "GET /health HTTP/1.1" 200
2025-06-06 03:58:34 [INFO] uvicorn.access [httptools_impl.py:496] - **********:41520 - "GET /api/v1/test/gpu-connection HTTP/1.1" 404
2025-06-06 03:58:34 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60112 - "GET /health HTTP/1.1" 200
2025-06-06 03:58:41 [INFO] uvicorn.access [httptools_impl.py:496] - **********:41528 - "GET / HTTP/1.1" 200
2025-06-06 03:59:05 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37174 - "GET /health HTTP/1.1" 200
2025-06-06 03:59:35 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34412 - "GET /health HTTP/1.1" 200
2025-06-06 04:00:05 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36430 - "GET /health HTTP/1.1" 200
2025-06-06 04:00:35 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46080 - "GET /health HTTP/1.1" 200
2025-06-06 04:01:05 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42794 - "GET /health HTTP/1.1" 200
2025-06-06 04:01:16 [INFO] uvicorn.access [httptools_impl.py:496] - **********:58652 - "GET /v2/status HTTP/1.1" 404
2025-06-06 04:01:35 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51374 - "GET /health HTTP/1.1" 200
2025-06-06 04:02:05 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41906 - "GET /health HTTP/1.1" 200
2025-06-06 04:02:35 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36514 - "GET /health HTTP/1.1" 200
2025-06-06 04:03:05 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38188 - "GET /health HTTP/1.1" 200
2025-06-06 04:03:35 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39090 - "GET /health HTTP/1.1" 200
2025-06-06 04:03:54 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 04:03:54 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 04:03:54 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 04:03:54 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 04:03:54 [INFO] uvicorn.error [server.py:86] - Finished server process [11]
2025-06-06 04:03:54 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 04:03:54 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 04:03:54 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 04:03:54 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 04:03:54 [INFO] uvicorn.error [server.py:86] - Finished server process [12]
2025-06-06 04:03:54 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 04:03:54 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 04:03:54 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 04:03:54 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 04:03:54 [INFO] uvicorn.error [server.py:86] - Finished server process [13]
2025-06-06 04:03:55 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 04:03:55 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 04:03:55 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 04:03:55 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 04:03:55 [INFO] uvicorn.error [server.py:86] - Finished server process [14]
2025-06-06 04:06:36 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 04:06:36 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 04:06:36 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 04:06:36 [INFO] uvicorn.error [server.py:76] - Started server process [14]
2025-06-06 04:06:36 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 04:06:36 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 04:06:36 [INFO] main [main.py:83] - 环境: production
2025-06-06 04:06:36 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 04:06:36 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 04:06:36 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 04:06:36 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 04:06:36 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 04:06:36 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 04:06:36 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 04:06:36 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 04:06:36 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 04:06:36 [INFO] uvicorn.error [server.py:76] - Started server process [12]
2025-06-06 04:06:36 [INFO] uvicorn.error [server.py:76] - Started server process [13]
2025-06-06 04:06:36 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 04:06:36 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 04:06:36 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 04:06:36 [INFO] main [main.py:83] - 环境: production
2025-06-06 04:06:36 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 04:06:36 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 04:06:36 [INFO] main [main.py:83] - 环境: production
2025-06-06 04:06:36 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 04:06:36 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 04:06:36 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 04:06:36 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 04:06:36 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'scipy'
2025-06-06 04:06:36 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 04:06:36 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 04:06:36 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 04:06:36 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 04:06:36 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 04:06:36 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 04:06:36 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'scipy'
2025-06-06 04:06:36 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'scipy'
2025-06-06 04:06:36 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 04:06:36 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 04:06:36 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 04:06:36 [INFO] uvicorn.error [server.py:76] - Started server process [11]
2025-06-06 04:06:36 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 04:06:36 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 04:06:36 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 04:06:36 [INFO] main [main.py:83] - 环境: production
2025-06-06 04:06:36 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 04:06:36 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 04:06:36 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 04:06:36 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'scipy'
2025-06-06 04:06:36 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 04:06:36 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 04:06:40 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40756 - "GET /health HTTP/1.1" 200
2025-06-06 04:07:02 [INFO] uvicorn.access [httptools_impl.py:496] - **********:46740 - "GET /health HTTP/1.1" 200
2025-06-06 04:07:10 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56232 - "GET /health HTTP/1.1" 200
2025-06-06 04:07:10 [INFO] uvicorn.access [httptools_impl.py:496] - **********:56624 - "GET /v2/status HTTP/1.1" 404
2025-06-06 04:07:18 [INFO] uvicorn.access [httptools_impl.py:496] - **********:53132 - "GET / HTTP/1.1" 200
2025-06-06 04:07:26 [INFO] uvicorn.access [httptools_impl.py:496] - **********:55030 - "GET /v2/ HTTP/1.1" 200
2025-06-06 04:07:34 [INFO] uvicorn.access [httptools_impl.py:496] - **********:43530 - "POST /v2/test/gpu-connection HTTP/1.1" 404
2025-06-06 04:07:40 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39884 - "GET /health HTTP/1.1" 200
2025-06-06 04:07:42 [INFO] uvicorn.access [httptools_impl.py:496] - **********:43544 - "GET /docs HTTP/1.1" 200
2025-06-06 04:07:49 [INFO] uvicorn.access [httptools_impl.py:496] - **********:44162 - "GET /docs HTTP/1.1" 200
2025-06-06 04:07:49 [INFO] uvicorn.access [httptools_impl.py:496] - **********:44162 - "GET /openapi.json HTTP/1.1" 200
2025-06-06 04:08:10 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59532 - "GET /health HTTP/1.1" 200
2025-06-06 04:08:12 [INFO] uvicorn.access [httptools_impl.py:496] - **********:34416 - "POST /v2/simple_test/gpu_connection HTTP/1.1" 404
2025-06-06 04:08:40 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51338 - "GET /health HTTP/1.1" 200
2025-06-06 04:09:10 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37170 - "GET /health HTTP/1.1" 200
2025-06-06 04:09:40 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59132 - "GET /health HTTP/1.1" 200
2025-06-06 04:10:10 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41582 - "GET /health HTTP/1.1" 200
2025-06-06 04:10:40 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57188 - "GET /health HTTP/1.1" 200
2025-06-06 04:11:10 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:32888 - "GET /health HTTP/1.1" 200
2025-06-06 04:11:40 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50788 - "GET /health HTTP/1.1" 200
2025-06-06 04:12:10 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35528 - "GET /health HTTP/1.1" 200
2025-06-06 04:12:40 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45432 - "GET /health HTTP/1.1" 200
2025-06-06 04:13:10 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54798 - "GET /health HTTP/1.1" 200
2025-06-06 04:13:40 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35288 - "GET /health HTTP/1.1" 200
2025-06-06 04:14:10 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53564 - "GET /health HTTP/1.1" 200
2025-06-06 04:14:40 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42882 - "GET /health HTTP/1.1" 200
2025-06-06 04:15:10 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:32962 - "GET /health HTTP/1.1" 200
2025-06-06 04:15:40 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36332 - "GET /health HTTP/1.1" 200
2025-06-06 04:16:10 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39848 - "GET /health HTTP/1.1" 200
2025-06-06 04:16:40 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43768 - "GET /health HTTP/1.1" 200
2025-06-06 04:17:10 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50822 - "GET /health HTTP/1.1" 200
2025-06-06 04:17:40 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44764 - "GET /health HTTP/1.1" 200
2025-06-06 04:18:10 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55992 - "GET /health HTTP/1.1" 200
2025-06-06 04:18:40 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36548 - "GET /health HTTP/1.1" 200
2025-06-06 04:19:10 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40610 - "GET /health HTTP/1.1" 200
2025-06-06 04:19:40 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42826 - "GET /health HTTP/1.1" 200
2025-06-06 04:20:10 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54912 - "GET /health HTTP/1.1" 200
2025-06-06 04:20:40 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59882 - "GET /health HTTP/1.1" 200
2025-06-06 04:21:10 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55216 - "GET /health HTTP/1.1" 200
2025-06-06 04:21:40 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51892 - "GET /health HTTP/1.1" 200
2025-06-06 04:22:10 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50146 - "GET /health HTTP/1.1" 200
2025-06-06 04:22:40 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51352 - "GET /health HTTP/1.1" 200
2025-06-06 04:23:10 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41170 - "GET /health HTTP/1.1" 200
2025-06-06 04:23:40 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40298 - "GET /health HTTP/1.1" 200
2025-06-06 04:24:10 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58442 - "GET /health HTTP/1.1" 200
2025-06-06 04:24:40 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54484 - "GET /health HTTP/1.1" 200
2025-06-06 04:25:11 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59494 - "GET /health HTTP/1.1" 200
2025-06-06 04:25:41 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47448 - "GET /health HTTP/1.1" 200
2025-06-06 04:26:11 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48018 - "GET /health HTTP/1.1" 200
2025-06-06 04:26:41 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52552 - "GET /health HTTP/1.1" 200
2025-06-06 04:27:11 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33934 - "GET /health HTTP/1.1" 200
2025-06-06 04:27:41 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51806 - "GET /health HTTP/1.1" 200
2025-06-06 04:28:11 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56768 - "GET /health HTTP/1.1" 200
2025-06-06 04:28:41 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49746 - "GET /health HTTP/1.1" 200
2025-06-06 04:29:11 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43420 - "GET /health HTTP/1.1" 200
2025-06-06 04:29:41 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33188 - "GET /health HTTP/1.1" 200
2025-06-06 04:30:11 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60764 - "GET /health HTTP/1.1" 200
2025-06-06 04:30:41 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38808 - "GET /health HTTP/1.1" 200
2025-06-06 04:31:11 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56274 - "GET /health HTTP/1.1" 200
2025-06-06 04:31:41 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43202 - "GET /health HTTP/1.1" 200
2025-06-06 04:32:11 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55642 - "GET /health HTTP/1.1" 200
2025-06-06 04:32:41 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37206 - "GET /health HTTP/1.1" 200
2025-06-06 04:33:11 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54706 - "GET /health HTTP/1.1" 200
2025-06-06 04:33:41 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49958 - "GET /health HTTP/1.1" 200
2025-06-06 04:34:11 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36034 - "GET /health HTTP/1.1" 200
2025-06-06 04:34:41 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52362 - "GET /health HTTP/1.1" 200
2025-06-06 04:35:11 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59144 - "GET /health HTTP/1.1" 200
2025-06-06 04:35:41 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33812 - "GET /health HTTP/1.1" 200
2025-06-06 04:36:11 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47460 - "GET /health HTTP/1.1" 200
2025-06-06 04:36:41 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35136 - "GET /health HTTP/1.1" 200
2025-06-06 04:37:11 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57818 - "GET /health HTTP/1.1" 200
2025-06-06 04:37:41 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57180 - "GET /health HTTP/1.1" 200
2025-06-06 04:38:11 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33412 - "GET /health HTTP/1.1" 200
2025-06-06 04:38:41 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38434 - "GET /health HTTP/1.1" 200
2025-06-06 04:39:11 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39956 - "GET /health HTTP/1.1" 200
2025-06-06 04:39:41 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59154 - "GET /health HTTP/1.1" 200
2025-06-06 04:40:11 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44562 - "GET /health HTTP/1.1" 200
2025-06-06 04:40:41 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52676 - "GET /health HTTP/1.1" 200
2025-06-06 04:41:11 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51190 - "GET /health HTTP/1.1" 200
2025-06-06 04:41:41 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44250 - "GET /health HTTP/1.1" 200
2025-06-06 04:42:11 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46316 - "GET /health HTTP/1.1" 200
2025-06-06 04:42:41 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33142 - "GET /health HTTP/1.1" 200
2025-06-06 04:43:11 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33620 - "GET /health HTTP/1.1" 200
2025-06-06 04:43:41 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53642 - "GET /health HTTP/1.1" 200
2025-06-06 04:44:11 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60566 - "GET /health HTTP/1.1" 200
2025-06-06 04:44:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59312 - "GET /health HTTP/1.1" 200
2025-06-06 04:45:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51352 - "GET /health HTTP/1.1" 200
2025-06-06 04:45:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50538 - "GET /health HTTP/1.1" 200
2025-06-06 04:46:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53184 - "GET /health HTTP/1.1" 200
2025-06-06 04:46:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38422 - "GET /health HTTP/1.1" 200
2025-06-06 04:47:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50408 - "GET /health HTTP/1.1" 200
2025-06-06 04:47:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54310 - "GET /health HTTP/1.1" 200
2025-06-06 04:48:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44086 - "GET /health HTTP/1.1" 200
2025-06-06 04:48:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36738 - "GET /health HTTP/1.1" 200
2025-06-06 04:49:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40698 - "GET /health HTTP/1.1" 200
2025-06-06 04:49:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34718 - "GET /health HTTP/1.1" 200
2025-06-06 04:50:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33462 - "GET /health HTTP/1.1" 200
2025-06-06 04:50:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50966 - "GET /health HTTP/1.1" 200
2025-06-06 04:51:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54812 - "GET /health HTTP/1.1" 200
2025-06-06 04:51:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46054 - "GET /health HTTP/1.1" 200
2025-06-06 04:52:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59110 - "GET /health HTTP/1.1" 200
2025-06-06 04:52:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53960 - "GET /health HTTP/1.1" 200
2025-06-06 04:53:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33126 - "GET /health HTTP/1.1" 200
2025-06-06 04:53:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40702 - "GET /health HTTP/1.1" 200
2025-06-06 04:54:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35820 - "GET /health HTTP/1.1" 200
2025-06-06 04:54:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39384 - "GET /health HTTP/1.1" 200
2025-06-06 04:55:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33832 - "GET /health HTTP/1.1" 200
2025-06-06 04:55:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51966 - "GET /health HTTP/1.1" 200
2025-06-06 04:56:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57318 - "GET /health HTTP/1.1" 200
2025-06-06 04:56:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39150 - "GET /health HTTP/1.1" 200
2025-06-06 04:57:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40954 - "GET /health HTTP/1.1" 200
2025-06-06 04:57:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35714 - "GET /health HTTP/1.1" 200
2025-06-06 04:58:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37688 - "GET /health HTTP/1.1" 200
2025-06-06 04:58:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36350 - "GET /health HTTP/1.1" 200
2025-06-06 04:59:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54328 - "GET /health HTTP/1.1" 200
2025-06-06 04:59:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43436 - "GET /health HTTP/1.1" 200
2025-06-06 05:00:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48088 - "GET /health HTTP/1.1" 200
2025-06-06 05:00:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34918 - "GET /health HTTP/1.1" 200
2025-06-06 05:01:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58468 - "GET /health HTTP/1.1" 200
2025-06-06 05:01:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42572 - "GET /health HTTP/1.1" 200
2025-06-06 05:02:12 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42874 - "GET /health HTTP/1.1" 200
2025-06-06 05:02:42 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47166 - "GET /health HTTP/1.1" 200
2025-06-06 05:03:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51798 - "GET /health HTTP/1.1" 200
2025-06-06 05:03:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50352 - "GET /health HTTP/1.1" 200
2025-06-06 05:04:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48128 - "GET /health HTTP/1.1" 200
2025-06-06 05:04:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53998 - "GET /health HTTP/1.1" 200
2025-06-06 05:05:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44540 - "GET /health HTTP/1.1" 200
2025-06-06 05:05:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49356 - "GET /health HTTP/1.1" 200
2025-06-06 05:06:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39248 - "GET /health HTTP/1.1" 200
2025-06-06 05:06:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58662 - "GET /health HTTP/1.1" 200
2025-06-06 05:07:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55448 - "GET /health HTTP/1.1" 200
2025-06-06 05:07:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57156 - "GET /health HTTP/1.1" 200
2025-06-06 05:08:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52644 - "GET /health HTTP/1.1" 200
2025-06-06 05:08:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57884 - "GET /health HTTP/1.1" 200
2025-06-06 05:09:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38598 - "GET /health HTTP/1.1" 200
2025-06-06 05:09:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52922 - "GET /health HTTP/1.1" 200
2025-06-06 05:10:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46240 - "GET /health HTTP/1.1" 200
2025-06-06 05:10:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48304 - "GET /health HTTP/1.1" 200
2025-06-06 05:11:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39442 - "GET /health HTTP/1.1" 200
2025-06-06 05:11:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41160 - "GET /health HTTP/1.1" 200
2025-06-06 05:12:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51702 - "GET /health HTTP/1.1" 200
2025-06-06 05:12:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39948 - "GET /health HTTP/1.1" 200
2025-06-06 05:13:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51412 - "GET /health HTTP/1.1" 200
2025-06-06 05:13:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52042 - "GET /health HTTP/1.1" 200
2025-06-06 05:14:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47810 - "GET /health HTTP/1.1" 200
2025-06-06 05:14:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44652 - "GET /health HTTP/1.1" 200
2025-06-06 05:15:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35232 - "GET /health HTTP/1.1" 200
2025-06-06 05:15:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59034 - "GET /health HTTP/1.1" 200
2025-06-06 05:16:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56414 - "GET /health HTTP/1.1" 200
2025-06-06 05:16:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43982 - "GET /health HTTP/1.1" 200
2025-06-06 05:17:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56678 - "GET /health HTTP/1.1" 200
2025-06-06 05:17:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47668 - "GET /health HTTP/1.1" 200
2025-06-06 05:18:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57264 - "GET /health HTTP/1.1" 200
2025-06-06 05:18:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49922 - "GET /health HTTP/1.1" 200
2025-06-06 05:19:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33006 - "GET /health HTTP/1.1" 200
2025-06-06 05:19:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37350 - "GET /health HTTP/1.1" 200
2025-06-06 05:20:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45760 - "GET /health HTTP/1.1" 200
2025-06-06 05:20:43 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35508 - "GET /health HTTP/1.1" 200
2025-06-06 05:21:13 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39068 - "GET /health HTTP/1.1" 200
2025-06-06 05:21:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40452 - "GET /health HTTP/1.1" 200
2025-06-06 05:22:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51852 - "GET /health HTTP/1.1" 200
2025-06-06 05:22:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46422 - "GET /health HTTP/1.1" 200
2025-06-06 05:23:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51094 - "GET /health HTTP/1.1" 200
2025-06-06 05:23:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38820 - "GET /health HTTP/1.1" 200
2025-06-06 05:24:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39508 - "GET /health HTTP/1.1" 200
2025-06-06 05:24:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55502 - "GET /health HTTP/1.1" 200
2025-06-06 05:25:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50190 - "GET /health HTTP/1.1" 200
2025-06-06 05:25:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56210 - "GET /health HTTP/1.1" 200
2025-06-06 05:26:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39836 - "GET /health HTTP/1.1" 200
2025-06-06 05:26:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42406 - "GET /health HTTP/1.1" 200
2025-06-06 05:27:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47494 - "GET /health HTTP/1.1" 200
2025-06-06 05:27:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33198 - "GET /health HTTP/1.1" 200
2025-06-06 05:28:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56148 - "GET /health HTTP/1.1" 200
2025-06-06 05:28:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35612 - "GET /health HTTP/1.1" 200
2025-06-06 05:29:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34768 - "GET /health HTTP/1.1" 200
2025-06-06 05:29:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36222 - "GET /health HTTP/1.1" 200
2025-06-06 05:30:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37458 - "GET /health HTTP/1.1" 200
2025-06-06 05:30:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59656 - "GET /health HTTP/1.1" 200
2025-06-06 05:31:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38930 - "GET /health HTTP/1.1" 200
2025-06-06 05:31:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41056 - "GET /health HTTP/1.1" 200
2025-06-06 05:32:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48844 - "GET /health HTTP/1.1" 200
2025-06-06 05:32:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58018 - "GET /health HTTP/1.1" 200
2025-06-06 05:33:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52406 - "GET /health HTTP/1.1" 200
2025-06-06 05:33:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59866 - "GET /health HTTP/1.1" 200
2025-06-06 05:34:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35674 - "GET /health HTTP/1.1" 200
2025-06-06 05:34:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58274 - "GET /health HTTP/1.1" 200
2025-06-06 05:35:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37966 - "GET /health HTTP/1.1" 200
2025-06-06 05:35:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52292 - "GET /health HTTP/1.1" 200
2025-06-06 05:36:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55388 - "GET /health HTTP/1.1" 200
2025-06-06 05:36:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59982 - "GET /health HTTP/1.1" 200
2025-06-06 05:37:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49582 - "GET /health HTTP/1.1" 200
2025-06-06 05:37:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60334 - "GET /health HTTP/1.1" 200
2025-06-06 05:38:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40058 - "GET /health HTTP/1.1" 200
2025-06-06 05:38:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33216 - "GET /health HTTP/1.1" 200
2025-06-06 05:39:14 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53630 - "GET /health HTTP/1.1" 200
2025-06-06 05:39:44 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60544 - "GET /health HTTP/1.1" 200
2025-06-06 05:40:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51862 - "GET /health HTTP/1.1" 200
2025-06-06 05:40:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60952 - "GET /health HTTP/1.1" 200
2025-06-06 05:41:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43764 - "GET /health HTTP/1.1" 200
2025-06-06 05:41:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33070 - "GET /health HTTP/1.1" 200
2025-06-06 05:42:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37718 - "GET /health HTTP/1.1" 200
2025-06-06 05:42:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44094 - "GET /health HTTP/1.1" 200
2025-06-06 05:43:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42472 - "GET /health HTTP/1.1" 200
2025-06-06 05:43:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46078 - "GET /health HTTP/1.1" 200
2025-06-06 05:44:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53440 - "GET /health HTTP/1.1" 200
2025-06-06 05:44:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45130 - "GET /health HTTP/1.1" 200
2025-06-06 05:45:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34780 - "GET /health HTTP/1.1" 200
2025-06-06 05:45:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57342 - "GET /health HTTP/1.1" 200
2025-06-06 05:46:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43264 - "GET /health HTTP/1.1" 200
2025-06-06 05:46:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49298 - "GET /health HTTP/1.1" 200
2025-06-06 05:47:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:43382 - "GET /health HTTP/1.1" 200
2025-06-06 05:47:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36756 - "GET /health HTTP/1.1" 200
2025-06-06 05:48:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44158 - "GET /health HTTP/1.1" 200
2025-06-06 05:48:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47748 - "GET /health HTTP/1.1" 200
2025-06-06 05:49:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45304 - "GET /health HTTP/1.1" 200
2025-06-06 05:49:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60088 - "GET /health HTTP/1.1" 200
2025-06-06 05:50:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52544 - "GET /health HTTP/1.1" 200
2025-06-06 05:50:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33216 - "GET /health HTTP/1.1" 200
2025-06-06 05:51:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39058 - "GET /health HTTP/1.1" 200
2025-06-06 05:51:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58592 - "GET /health HTTP/1.1" 200
2025-06-06 05:52:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42728 - "GET /health HTTP/1.1" 200
2025-06-06 05:52:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49438 - "GET /health HTTP/1.1" 200
2025-06-06 05:53:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39548 - "GET /health HTTP/1.1" 200
2025-06-06 05:53:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57704 - "GET /health HTTP/1.1" 200
2025-06-06 05:54:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36070 - "GET /health HTTP/1.1" 200
2025-06-06 05:54:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44532 - "GET /health HTTP/1.1" 200
2025-06-06 05:55:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37824 - "GET /health HTTP/1.1" 200
2025-06-06 05:55:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40380 - "GET /health HTTP/1.1" 200
2025-06-06 05:56:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46980 - "GET /health HTTP/1.1" 200
2025-06-06 05:56:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36682 - "GET /health HTTP/1.1" 200
2025-06-06 05:57:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:55310 - "GET /health HTTP/1.1" 200
2025-06-06 05:57:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35620 - "GET /health HTTP/1.1" 200
2025-06-06 05:58:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35878 - "GET /health HTTP/1.1" 200
2025-06-06 05:58:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49324 - "GET /health HTTP/1.1" 200
2025-06-06 05:59:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35240 - "GET /health HTTP/1.1" 200
2025-06-06 05:59:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37718 - "GET /health HTTP/1.1" 200
2025-06-06 06:00:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35508 - "GET /health HTTP/1.1" 200
2025-06-06 06:00:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:38340 - "GET /health HTTP/1.1" 200
2025-06-06 06:01:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41296 - "GET /health HTTP/1.1" 200
2025-06-06 06:01:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39552 - "GET /health HTTP/1.1" 200
2025-06-06 06:02:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53714 - "GET /health HTTP/1.1" 200
2025-06-06 06:02:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40446 - "GET /health HTTP/1.1" 200
2025-06-06 06:03:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34982 - "GET /health HTTP/1.1" 200
2025-06-06 06:03:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45872 - "GET /health HTTP/1.1" 200
2025-06-06 06:04:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47868 - "GET /health HTTP/1.1" 200
2025-06-06 06:04:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50980 - "GET /health HTTP/1.1" 200
2025-06-06 06:05:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35094 - "GET /health HTTP/1.1" 200
2025-06-06 06:05:46 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48092 - "GET /health HTTP/1.1" 200
2025-06-06 06:06:16 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41824 - "GET /health HTTP/1.1" 200
2025-06-06 06:06:19 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 06:06:20 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 06:06:20 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 06:06:20 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 06:06:20 [INFO] uvicorn.error [server.py:86] - Finished server process [11]
2025-06-06 06:06:20 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 06:06:20 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 06:06:20 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 06:06:20 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 06:06:20 [INFO] uvicorn.error [server.py:86] - Finished server process [12]
2025-06-06 06:06:21 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 06:06:21 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 06:06:21 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 06:06:21 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 06:06:21 [INFO] uvicorn.error [server.py:86] - Finished server process [13]
2025-06-06 06:06:21 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-06 06:06:21 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-06 06:06:21 [INFO] main [main.py:121] - CPU服务关闭
2025-06-06 06:06:21 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-06 06:06:21 [INFO] uvicorn.error [server.py:86] - Finished server process [14]
2025-06-06 06:06:53 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 06:06:53 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 06:06:53 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 06:06:53 [INFO] uvicorn.error [server.py:76] - Started server process [13]
2025-06-06 06:06:53 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 06:06:53 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 06:06:53 [INFO] main [main.py:83] - 环境: production
2025-06-06 06:06:53 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 06:06:53 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 06:06:53 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 06:06:53 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 06:06:53 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 06:06:53 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 06:06:53 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 06:06:53 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 06:06:53 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 06:06:53 [INFO] uvicorn.error [server.py:76] - Started server process [11]
2025-06-06 06:06:53 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 06:06:53 [INFO] uvicorn.error [server.py:76] - Started server process [12]
2025-06-06 06:06:53 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'scipy'
2025-06-06 06:06:53 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 06:06:53 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 06:06:53 [INFO] main [main.py:83] - 环境: production
2025-06-06 06:06:53 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 06:06:53 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 06:06:53 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 06:06:53 [INFO] main [main.py:83] - 环境: production
2025-06-06 06:06:53 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 06:06:53 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 06:06:53 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 06:06:53 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 06:06:53 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 06:06:53 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 06:06:53 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'scipy'
2025-06-06 06:06:53 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'scipy'
2025-06-06 06:06:53 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 06:06:53 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 06:06:53 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 06:06:53 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 06:06:53 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-06 06:06:53 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs/cpu-service-2025-06-06.log
2025-06-06 06:06:53 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs/cpu-service-error-2025-06-06.log
2025-06-06 06:06:53 [INFO] uvicorn.error [server.py:76] - Started server process [14]
2025-06-06 06:06:53 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-06 06:06:53 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-06 06:06:53 [INFO] main [main.py:83] - 环境: production
2025-06-06 06:06:53 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8000
2025-06-06 06:06:53 [INFO] main [main.py:85] - GPU服务地址: http://gpu-service:8001
2025-06-06 06:06:53 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-06 06:06:53 [WARNING] main [main.py:112] - ⚠️ 服务预热失败: No module named 'scipy'
2025-06-06 06:06:53 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-06 06:06:53 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-06 06:06:55 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40610 - "GET /health HTTP/1.1" 200
2025-06-06 06:07:10 [INFO] uvicorn.access [httptools_impl.py:496] - **********:58930 - "GET /v2/simple_test/ HTTP/1.1" 404
2025-06-06 06:07:25 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53700 - "GET /health HTTP/1.1" 200
2025-06-06 06:07:33 [INFO] uvicorn.access [httptools_impl.py:496] - **********:36078 - "GET /v2/status HTTP/1.1" 404
2025-06-06 06:07:55 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48786 - "GET /health HTTP/1.1" 200
2025-06-06 06:07:58 [INFO] uvicorn.access [httptools_impl.py:496] - **********:33862 - "GET /v2/status HTTP/1.1" 404
2025-06-06 06:08:22 [INFO] uvicorn.access [httptools_impl.py:496] - **********:50316 - "GET /v2/ HTTP/1.1" 200
2025-06-06 06:08:25 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:34326 - "GET /health HTTP/1.1" 200
2025-06-06 06:08:55 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58626 - "GET /health HTTP/1.1" 200
2025-06-06 06:09:25 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:47152 - "GET /health HTTP/1.1" 200
2025-06-06 06:09:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40678 - "GET /health HTTP/1.1" 200
2025-06-06 06:10:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37088 - "GET /health HTTP/1.1" 200
2025-06-06 06:10:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59202 - "GET /health HTTP/1.1" 200
2025-06-06 06:11:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59506 - "GET /health HTTP/1.1" 200
2025-06-06 06:11:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44972 - "GET /health HTTP/1.1" 200
2025-06-06 06:12:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60668 - "GET /health HTTP/1.1" 200
2025-06-06 06:12:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48076 - "GET /health HTTP/1.1" 200
2025-06-06 06:13:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56100 - "GET /health HTTP/1.1" 200
2025-06-06 06:13:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35400 - "GET /health HTTP/1.1" 200
2025-06-06 06:14:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59110 - "GET /health HTTP/1.1" 200
2025-06-06 06:14:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37170 - "GET /health HTTP/1.1" 200
2025-06-06 06:15:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60826 - "GET /health HTTP/1.1" 200
2025-06-06 06:15:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35348 - "GET /health HTTP/1.1" 200
2025-06-06 06:16:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50458 - "GET /health HTTP/1.1" 200
2025-06-06 06:16:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52714 - "GET /health HTTP/1.1" 200
2025-06-06 06:17:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53340 - "GET /health HTTP/1.1" 200
2025-06-06 06:17:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:32958 - "GET /health HTTP/1.1" 200
2025-06-06 06:18:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54634 - "GET /health HTTP/1.1" 200
2025-06-06 06:18:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41684 - "GET /health HTTP/1.1" 200
2025-06-06 06:19:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33484 - "GET /health HTTP/1.1" 200
2025-06-06 06:19:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37204 - "GET /health HTTP/1.1" 200
2025-06-06 06:20:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44292 - "GET /health HTTP/1.1" 200
2025-06-06 06:20:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33322 - "GET /health HTTP/1.1" 200
2025-06-06 06:21:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:35766 - "GET /health HTTP/1.1" 200
2025-06-06 06:21:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60986 - "GET /health HTTP/1.1" 200
2025-06-06 06:22:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58922 - "GET /health HTTP/1.1" 200
2025-06-06 06:22:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:41584 - "GET /health HTTP/1.1" 200
2025-06-06 06:23:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:50168 - "GET /health HTTP/1.1" 200
2025-06-06 06:23:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54460 - "GET /health HTTP/1.1" 200
2025-06-06 06:24:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59862 - "GET /health HTTP/1.1" 200
2025-06-06 06:24:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36078 - "GET /health HTTP/1.1" 200
2025-06-06 06:25:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:60036 - "GET /health HTTP/1.1" 200
2025-06-06 06:25:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40180 - "GET /health HTTP/1.1" 200
2025-06-06 06:26:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53060 - "GET /health HTTP/1.1" 200
2025-06-06 06:26:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39656 - "GET /health HTTP/1.1" 200
2025-06-06 06:27:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39152 - "GET /health HTTP/1.1" 200
2025-06-06 06:27:56 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44324 - "GET /health HTTP/1.1" 200
2025-06-06 06:28:26 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:48374 - "GET /health HTTP/1.1" 200
2025-06-06 06:28:57 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:52778 - "GET /health HTTP/1.1" 200
2025-06-06 06:29:27 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53760 - "GET /health HTTP/1.1" 200
2025-06-06 06:29:57 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42550 - "GET /health HTTP/1.1" 200
2025-06-06 06:30:27 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53934 - "GET /health HTTP/1.1" 200
2025-06-06 06:30:57 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51028 - "GET /health HTTP/1.1" 200
2025-06-06 06:31:27 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:40230 - "GET /health HTTP/1.1" 200
2025-06-06 06:31:57 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:37692 - "GET /health HTTP/1.1" 200
2025-06-06 06:32:27 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:53952 - "GET /health HTTP/1.1" 200
2025-06-06 06:32:57 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59234 - "GET /health HTTP/1.1" 200
2025-06-06 06:33:27 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51682 - "GET /health HTTP/1.1" 200
2025-06-06 06:33:57 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:36220 - "GET /health HTTP/1.1" 200
2025-06-06 06:34:27 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49492 - "GET /health HTTP/1.1" 200
2025-06-06 06:34:57 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33196 - "GET /health HTTP/1.1" 200
2025-06-06 06:35:27 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58162 - "GET /health HTTP/1.1" 200
2025-06-06 06:35:57 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:51006 - "GET /health HTTP/1.1" 200
2025-06-06 06:36:27 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:49098 - "GET /health HTTP/1.1" 200
2025-06-06 06:36:57 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:42892 - "GET /health HTTP/1.1" 200
2025-06-06 06:37:27 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56590 - "GET /health HTTP/1.1" 200
2025-06-06 06:37:57 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:39278 - "GET /health HTTP/1.1" 200
2025-06-06 06:38:27 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:46098 - "GET /health HTTP/1.1" 200
2025-06-06 06:38:57 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:44350 - "GET /health HTTP/1.1" 200
2025-06-06 06:39:27 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54308 - "GET /health HTTP/1.1" 200
2025-06-06 06:39:57 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:33196 - "GET /health HTTP/1.1" 200
2025-06-06 06:40:02 [INFO] uvicorn.access [httptools_impl.py:496] - **********:60318 - "GET /v2/status HTTP/1.1" 404
2025-06-06 06:40:11 [INFO] uvicorn.access [httptools_impl.py:496] - **********:34114 - "GET /docs HTTP/1.1" 200
2025-06-06 06:40:19 [INFO] uvicorn.access [httptools_impl.py:496] - **********:38744 - "GET /docs HTTP/1.1" 200
2025-06-06 06:40:19 [INFO] uvicorn.access [httptools_impl.py:496] - **********:38744 - "GET /openapi.json HTTP/1.1" 200
2025-06-06 06:40:27 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56156 - "GET /health HTTP/1.1" 200
2025-06-06 06:40:33 [INFO] uvicorn.access [httptools_impl.py:496] - **********:42484 - "GET / HTTP/1.1" 200
2025-06-06 06:40:42 [INFO] uvicorn.access [httptools_impl.py:496] - **********:35732 - "GET /v2/ HTTP/1.1" 200
2025-06-06 06:40:52 [INFO] uvicorn.access [httptools_impl.py:496] - **********:34594 - "GET /v2/status HTTP/1.1" 404
2025-06-06 06:40:57 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:56754 - "GET /health HTTP/1.1" 200
2025-06-06 06:41:14 [INFO] uvicorn.access [httptools_impl.py:496] - **********:33090 - "GET /v2/status HTTP/1.1" 404
2025-06-06 06:41:27 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:45530 - "GET /health HTTP/1.1" 200
2025-06-06 06:41:57 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:58706 - "GET /health HTTP/1.1" 200
2025-06-06 06:42:24 [INFO] uvicorn.access [httptools_impl.py:496] - **********:40618 - "GET /v2/status HTTP/1.1" 404
2025-06-06 06:42:27 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:54980 - "GET /health HTTP/1.1" 200
2025-06-06 06:42:57 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57356 - "GET /health HTTP/1.1" 200
