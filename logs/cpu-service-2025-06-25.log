2025-06-25 11:04:47 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 11:04:47 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 11:04:47 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 11:04:47 [INFO] __main__ [main.py:188] - 启动CPU服务: 0.0.0.0:8009
2025-06-25 11:04:47 [INFO] __main__ [main.py:194] - 启动模式: development
2025-06-25 11:04:47 [INFO] __main__ [main.py:195] - 工作进程数: 1
2025-06-25 11:04:47 [INFO] __main__ [main.py:196] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-25 11:04:47 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 11:04:47 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 11:04:47 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 11:04:47 [INFO] uvicorn.error [server.py:76] - Started server process [36924]
2025-06-25 11:04:47 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-25 11:04:47 [INFO] main [main.py:86] - 🚀 初始化CPU服务...
2025-06-25 11:04:47 [INFO] main [main.py:87] - 环境: development
2025-06-25 11:04:47 [INFO] main [main.py:88] - 服务地址: http://0.0.0.0:8009
2025-06-25 11:04:47 [INFO] main [main.py:89] - GPU服务地址: http://localhost:8001
2025-06-25 11:04:47 [INFO] main [main.py:92] - 🔥 开始预热服务组件...
2025-06-25 11:04:47 [INFO] cores.retrieval_service [retrieval_service.py:65] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-25 11:04:47 [INFO] cores.retrieval_service [retrieval_service.py:624] - 创建检索服务实例
2025-06-25 11:04:47 [INFO] main [main.py:103] - 正在预热Milvus客户端...
2025-06-25 11:04:47 [INFO] main [main.py:113] - ✅ 服务预热已启动（后台进行）
2025-06-25 11:04:47 [INFO] cores.retrieval_service [retrieval_service.py:72] - 正在初始化Milvus客户端...
2025-06-25 11:04:47 [INFO] main [main.py:118] - ✅ CPU服务初始化完成
2025-06-25 11:04:47 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:309] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-25 11:04:47 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-25 11:04:47 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:54] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-25 11:04:47 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:55] - 批处理配置: batch_size=32
2025-06-25 11:04:47 [ERROR] uvicorn.error [server.py:168] - [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8009): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-25 11:04:47 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-25 11:04:47 [INFO] main [main.py:125] - CPU服务关闭
2025-06-25 11:04:47 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-25 11:09:23 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 11:09:23 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 11:09:23 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 11:09:23 [INFO] __main__ [main.py:188] - 启动CPU服务: 0.0.0.0:8009
2025-06-25 11:09:23 [INFO] __main__ [main.py:194] - 启动模式: development
2025-06-25 11:09:23 [INFO] __main__ [main.py:195] - 工作进程数: 1
2025-06-25 11:09:23 [INFO] __main__ [main.py:196] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-25 11:09:23 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 11:09:23 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 11:09:23 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 11:09:23 [INFO] uvicorn.error [server.py:76] - Started server process [15844]
2025-06-25 11:09:23 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-25 11:09:23 [INFO] main [main.py:86] - 初始化CPU服务...
2025-06-25 11:09:23 [INFO] main [main.py:87] - 环境: development
2025-06-25 11:09:23 [INFO] main [main.py:88] - 服务地址: http://0.0.0.0:8009
2025-06-25 11:09:23 [INFO] main [main.py:89] - GPU服务地址: http://localhost:8001
2025-06-25 11:09:23 [INFO] main [main.py:92] - 开始预热服务组件...
2025-06-25 11:09:23 [INFO] cores.retrieval_service [retrieval_service.py:65] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-25 11:09:23 [INFO] cores.retrieval_service [retrieval_service.py:624] - 创建检索服务实例
2025-06-25 11:09:23 [INFO] main [main.py:103] - 正在预热Milvus客户端...
2025-06-25 11:09:23 [INFO] main [main.py:113] - 服务预热已启动（后台进行）
2025-06-25 11:09:23 [INFO] main [main.py:118] - CPU服务初始化完成
2025-06-25 11:09:23 [INFO] cores.retrieval_service [retrieval_service.py:72] - 正在初始化Milvus客户端...
2025-06-25 11:09:23 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-25 11:09:23 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:309] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-25 11:09:23 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:54] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-25 11:09:23 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:55] - 批处理配置: batch_size=32
2025-06-25 11:09:23 [INFO] uvicorn.error [server.py:218] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-25 11:09:23 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:88] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '28%', 'memory_utilization': '26%', 'temperature': '41°C', 'power_usage': '6.8W'}}
2025-06-25 11:09:23 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:229] - GPU服务稀疏嵌入客户端初始化成功
2025-06-25 11:09:23 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:233] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-25 11:09:24 [INFO] shared.clients.milvus_client [milvus_client.py:218] - 成功连接到Milvus: http://172.24.255.5:19530
2025-06-25 11:09:24 [INFO] shared.clients.milvus_client [milvus_client.py:189] - Milvus客户端初始化完成: http://172.24.255.5:19530
2025-06-25 11:09:24 [INFO] shared.clients.milvus_client [milvus_client.py:724] - 创建Milvus客户端实例
2025-06-25 11:09:24 [INFO] cores.retrieval_service [retrieval_service.py:74] - Milvus客户端初始化成功
2025-06-25 11:09:24 [INFO] main [main.py:105] - Milvus客户端预热完成
2025-06-25 11:15:07 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 11:15:07 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 11:15:07 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 11:15:07 [INFO] __main__ [main.py:188] - 启动CPU服务: 0.0.0.0:8009
2025-06-25 11:15:07 [INFO] __main__ [main.py:194] - 启动模式: development
2025-06-25 11:15:07 [INFO] __main__ [main.py:195] - 工作进程数: 1
2025-06-25 11:15:07 [INFO] __main__ [main.py:196] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-25 11:15:07 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 11:15:07 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 11:15:07 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 11:15:07 [INFO] uvicorn.error [server.py:76] - Started server process [17880]
2025-06-25 11:15:07 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-25 11:15:07 [INFO] main [main.py:86] - 初始化CPU服务...
2025-06-25 11:15:07 [INFO] main [main.py:87] - 环境: development
2025-06-25 11:15:07 [INFO] main [main.py:88] - 服务地址: http://0.0.0.0:8009
2025-06-25 11:15:07 [INFO] main [main.py:89] - GPU服务地址: http://localhost:8001
2025-06-25 11:15:07 [INFO] main [main.py:92] - 开始预热服务组件...
2025-06-25 11:15:07 [INFO] cores.retrieval_service [retrieval_service.py:65] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-25 11:15:07 [INFO] cores.retrieval_service [retrieval_service.py:624] - 创建检索服务实例
2025-06-25 11:15:07 [INFO] main [main.py:103] - 正在预热Milvus客户端...
2025-06-25 11:15:07 [INFO] main [main.py:113] - 服务预热已启动（后台进行）
2025-06-25 11:15:07 [INFO] cores.retrieval_service [retrieval_service.py:72] - 正在初始化Milvus客户端...
2025-06-25 11:15:07 [INFO] main [main.py:118] - CPU服务初始化完成
2025-06-25 11:15:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:309] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-25 11:15:07 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-25 11:15:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:54] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-25 11:15:07 [ERROR] uvicorn.error [server.py:168] - [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8009): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-25 11:15:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:55] - 批处理配置: batch_size=32
2025-06-25 11:15:07 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-25 11:15:07 [INFO] main [main.py:125] - CPU服务关闭
2025-06-25 11:15:07 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-25 11:16:34 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 11:16:34 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 11:16:34 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 11:16:34 [INFO] __main__ [main.py:188] - 启动CPU服务: 0.0.0.0:8009
2025-06-25 11:16:34 [INFO] __main__ [main.py:194] - 启动模式: development
2025-06-25 11:16:34 [INFO] __main__ [main.py:195] - 工作进程数: 1
2025-06-25 11:16:34 [INFO] __main__ [main.py:196] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-25 11:16:34 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 11:16:34 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 11:16:34 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 11:16:34 [INFO] uvicorn.error [server.py:76] - Started server process [12464]
2025-06-25 11:16:34 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-25 11:16:34 [INFO] main [main.py:86] - 初始化CPU服务...
2025-06-25 11:16:34 [INFO] main [main.py:87] - 环境: development
2025-06-25 11:16:34 [INFO] main [main.py:88] - 服务地址: http://0.0.0.0:8009
2025-06-25 11:16:34 [INFO] main [main.py:89] - GPU服务地址: http://localhost:8001
2025-06-25 11:16:34 [INFO] main [main.py:92] - 开始预热服务组件...
2025-06-25 11:16:34 [INFO] cores.retrieval_service [retrieval_service.py:65] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-25 11:16:34 [INFO] cores.retrieval_service [retrieval_service.py:624] - 创建检索服务实例
2025-06-25 11:16:34 [INFO] main [main.py:103] - 正在预热Milvus客户端...
2025-06-25 11:16:34 [INFO] main [main.py:113] - 服务预热已启动（后台进行）
2025-06-25 11:16:34 [INFO] cores.retrieval_service [retrieval_service.py:72] - 正在初始化Milvus客户端...
2025-06-25 11:16:34 [INFO] main [main.py:118] - CPU服务初始化完成
2025-06-25 11:16:34 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:309] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-25 11:16:34 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-25 11:16:34 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:54] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-25 11:16:34 [INFO] uvicorn.error [server.py:218] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-25 11:16:34 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:55] - 批处理配置: batch_size=32
2025-06-25 11:16:34 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:88] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '30%', 'memory_utilization': '24%', 'temperature': '44°C', 'power_usage': '6.6W'}}
2025-06-25 11:16:34 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:229] - GPU服务稀疏嵌入客户端初始化成功
2025-06-25 11:16:34 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:233] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-25 11:16:34 [INFO] shared.clients.milvus_client [milvus_client.py:218] - 成功连接到Milvus: http://172.24.255.5:19530
2025-06-25 11:16:34 [INFO] shared.clients.milvus_client [milvus_client.py:189] - Milvus客户端初始化完成: http://172.24.255.5:19530
2025-06-25 11:16:34 [INFO] shared.clients.milvus_client [milvus_client.py:724] - 创建Milvus客户端实例
2025-06-25 11:16:34 [INFO] cores.retrieval_service [retrieval_service.py:74] - Milvus客户端初始化成功
2025-06-25 11:16:34 [INFO] main [main.py:105] - Milvus客户端预热完成
