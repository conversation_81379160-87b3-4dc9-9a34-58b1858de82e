# CPU Service Dependencies - Main Business Service
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.3
pydantic-settings>=2.1.0,<3.0.0
requests>=2.31.0
python-multipart==0.0.6
python-dotenv==1.0.0
pyyaml>=6.0

# LlamaIndex Core (Compatible Versions)
llama-index==0.10.57
llama-index-core==0.10.57
llama-index-embeddings-openai==0.1.10
llama-index-vector-stores-milvus==0.1.20
llama-index-llms-openai==0.1.24

# Vector Database
pymilvus>=2.3.0

# Data Processing
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.11.0

# Cache and Storage
redis>=4.5.0
minio>=7.1.0

# Logging and Monitoring
structlog>=23.1.0

# Utility Libraries
aiofiles>=23.1.0
httpx>=0.24.0
deprecated>=1.2.14

# High Performance Dependencies (Optional, Linux/Mac only)
# uvloop>=0.19.0  # Not supported on Windows
httptools>=0.6.0

# Additional Vector Store Support
faiss-cpu>=1.7.4
