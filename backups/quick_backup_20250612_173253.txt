# 快速备份 - 2025-06-12 17:32:53.563263
accelerate==1.7.0
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.12.4
aiosignal==1.3.2
aiosqlite==0.21.0
annotated-types==0.7.0
anyio==3.7.1
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
asttokens==3.0.0
attrs==25.3.0
autogen-agentchat==0.5.7
autogen-core==0.5.7
autogen-ext==0.5.7
banks==2.1.2
beautifulsoup4==4.13.4
build==1.2.2.post1
cbor==1.0.0
certifi==2025.4.26
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.1
cloudpickle==3.1.1
colorama==0.4.6
dask==2025.5.1
dataclasses-json==0.6.7
datasets==3.6.0
debugpy==1.8.14
decorator==5.2.1
Deprecated==1.2.18
dill==0.3.8
dirtyjson==1.0.8
distro==1.9.0
environs==9.5.0
executing==2.2.0
faiss-cpu==1.11.0
fastapi==0.104.1
filelock==3.18.0
filetype==1.2.0
FlagEmbedding==1.3.5
frozenlist==1.6.0
fsspec==2025.3.0
greenlet==3.2.2
griffe==1.7.3
grpcio==1.67.1
h11==0.16.0
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.32.2
idna==3.10
ijson==3.4.0
importlib_metadata==8.6.1
iniconfig==2.1.0
inscriptis==2.6.0
ipython==9.3.0
ipython_pygments_lexers==1.1.1
ir_datasets==0.5.10
jedi==0.19.2
Jinja2==3.1.6
jiter==0.10.0
joblib==1.5.1
jsonref==1.1.0
llama-cloud==0.1.26
llama-cloud-services==0.6.31
llama-index==0.12.42
llama-index-agent-openai==0.4.11
llama-index-cli==0.4.3
llama-index-core==0.12.42
llama-index-embeddings-openai==0.3.1
llama-index-indices-managed-llama-cloud==0.7.7
llama-index-legacy==0.9.48.post4
llama-index-llms-openai==0.4.5
llama-index-multi-modal-llms-openai==0.5.1
llama-index-program-openai==0.3.2
llama-index-question-gen-openai==0.3.1
llama-index-readers-file==0.4.9
llama-index-readers-llama-parse==0.4.0
llama-index-vector-stores-milvus==0.1.20
llama-parse==0.6.31
locket==1.0.0
lxml==5.4.0
lz4==4.4.4
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib-inline==0.1.7
minio==7.2.15
mpmath==1.3.0
multidict==6.4.4
multiprocess==0.70.16
mypy_extensions==1.1.0
nest-asyncio==1.6.0
networkx==3.4.2
nltk==3.9.1
numpy==1.26.4
openai==1.82.0
opentelemetry-api==1.33.1
packaging==25.0
pandas==2.2.3
parso==0.8.4
partd==1.4.2
peft==0.15.2
pillow==11.2.1
pip-tools==7.4.1
platformdirs==4.3.8
pluggy==1.6.0
prompt_toolkit==3.0.51
propcache==0.3.1
protobuf==5.29.5
psutil==7.0.0
pure_eval==0.2.3
pyarrow==20.0.0
pycparser==2.22
pycryptodome==3.23.0
pydantic==2.11.5
pydantic-settings==2.2.1
pydantic_core==2.33.2
Pygments==2.19.1
pymilvus==2.4.9
pypdf==5.6.0
pyproject_hooks==1.2.0
pytest==8.4.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-multipart==0.0.6
pytz==2025.2
PyYAML==6.0.2
redis==6.2.0
regex==2024.11.6
requests==2.32.3
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.3
sentence-transformers==4.1.0
sentencepiece==0.2.0
six==1.17.0
sniffio==1.3.1
soupsieve==2.7
SQLAlchemy==2.0.41
stack-data==0.6.3
starlette==0.27.0
striprtf==0.0.26
structlog==25.4.0
sympy==1.14.0
tenacity==8.5.0
threadpoolctl==3.6.0
tiktoken==0.9.0
tokenizers==0.21.1
toolz==1.0.0
torch==2.7.0+cu126
torchaudio==2.7.0+cu126
torchvision==0.22.0+cu126
tqdm==4.67.1
traitlets==5.14.3
transformers==4.52.3
trec-car-tools==2.6
typing-inspect==0.9.0
typing-inspection==0.4.1
typing_extensions==4.13.2
tzdata==2025.2
ujson==5.10.0
unlzw3==0.2.3
urllib3==2.4.0
uvicorn==0.24.0
warc3-wet==0.2.5
warc3-wet-clueweb09==0.2.5
watchfiles==1.0.5
wcwidth==0.2.13
websockets==15.0.1
wrapt==1.17.2
xxhash==3.5.0
yarl==1.20.0
zipp==3.22.0
zlib-state==0.1.9
