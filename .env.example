# AI接诉即办助手 v3.0 - 环境配置模板
# 复制此文件为 .env 并根据实际环境修改配置

# ================================
# 基础配置
# ================================
ENVIRONMENT=development
DEBUG=false
LOG_LEVEL=INFO

# ================================
# API嵌入配置（密集嵌入）
# ================================
API_EMBED_MODEL=BAAI/bge-m3
API_EMBED_KEY=your_api_key_here
API_EMBED_BASE=https://api.siliconflow.cn/v1
API_EMBED_BATCH_SIZE=8

# ================================
# BGE-M3模型配置（稀疏嵌入）
# ================================
BGE_M3_MODEL_PATH=BAAI/bge-m3

# ================================
# GPU服务配置
# ================================
GPU_SERVICE_HOST=0.0.0.0
GPU_SERVICE_PORT=8001
GPU_SERVICE_WORKERS=1
# GPU服务URL（跨服务器部署时修改此项）
GPU_SERVICE_URL=http://gpu-service:8001

# ================================
# CPU服务配置
# ================================
CPU_SERVICE_HOST=0.0.0.0
CPU_SERVICE_PORT=8000
CPU_SERVICE_WORKERS=2

# ================================
# LLM配置
# ================================
LLM_MODEL=Qwen/Qwen2.5-32B-Instruct
LLM_API_KEY=your_llm_api_key_here
LLM_BASE_URL=https://api.siliconflow.cn/v1

# ================================
# 数据库配置
# ================================
MILVUS_URI=http://localhost:19530
MILVUS_DIMENSION=1024

# ================================
# Redis配置
# ================================
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=

# ================================
# MinIO配置
# ================================
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=ai-jiesujiban

# ================================
# 检索配置
# ================================
RETRIEVAL_TOP_K_DEPARTMENT=3
RETRIEVAL_TOP_K_GUIDELINE=3
RETRIEVAL_TOP_K_HISTORICAL=5
RETRIEVAL_TOP_K_DELEGATED=3

# ================================
# 重排序配置
# ================================
RERANK_MODEL=BAAI/bge-reranker-v2-m3
RERANK_API_KEY=your_rerank_api_key_here
RERANK_BASE_URL=https://api.siliconflow.cn/v1

# ================================
# 安全配置
# ================================
ADMIN_API_KEY=sk-de18e2cce5326daf8fbb6af4fb67aa71

# ================================
# CORS配置
# ================================
CORS_ORIGINS=*
CORS_METHODS=GET,POST,PUT,DELETE
CORS_HEADERS=*

# ================================
# 性能配置
# ================================
REQUEST_TIMEOUT=300
GPU_REQUEST_TIMEOUT=60
MAX_CONCURRENT_REQUESTS=100

# ================================
# 监控配置
# ================================
ENABLE_PERFORMANCE_LOGGING=true
PERFORMANCE_LOG_LEVEL=INFO
HEALTH_CHECK_INTERVAL=30
