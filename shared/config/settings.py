"""
共享配置管理

统一管理GPU服务和CPU服务的配置
"""

import os
import json
from typing import Optional, List, Union, Any
from functools import lru_cache

try:
    # Pydantic v2
    from pydantic_settings import BaseSettings
    from pydantic import Field, field_validator
except ImportError:
    try:
        # Pydantic v1 fallback
        from pydantic import BaseSettings, Field, validator as field_validator
    except ImportError:
        raise ImportError("需要安装 pydantic 或 pydantic-settings")


class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    ENVIRONMENT: str = "development"
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    
    # API嵌入配置（密集嵌入）
    API_EMBED_MODEL: str = "BAAI/bge-m3"
    API_EMBED_KEY: Optional[str] = "sk-hconcoielayrcqyfoqibziztdnchplrhkmupsvddcfkwehrd"
    API_EMBED_BASE: str = "https://api.siliconflow.cn/v1"
    API_EMBED_BATCH_SIZE: int = 8

    # GPU稀疏嵌入优化配置（基于测试结果）
    GPU_SPARSE_BATCH_SIZE: int = 32   # RTX 4060最优批处理大小（295.6文档/秒）
    GPU_SPARSE_TIMEOUT: int = 180     # 适中的超时时间

    # 简化GPU配置
    GPU_CONFIG_TYPE: str = "simplified"  # 使用简化配置系统
    
    # BGE-M3模型配置（稀疏嵌入）
    # 优先使用环境变量，如果没有则使用默认的Hugging Face模型名
    BGE_M3_MODEL_PATH: str = Field(default="BAAI/bge-m3")
    
    # GPU服务配置
    GPU_SERVICE_HOST: str = "0.0.0.0"
    GPU_SERVICE_PORT: int = 8001
    GPU_SERVICE_WORKERS: int = 1
    GPU_SERVICE_URL: Optional[str] = None
    
    # CPU服务配置
    CPU_SERVICE_HOST: str = "0.0.0.0"
    CPU_SERVICE_PORT: int = 8009
    CPU_SERVICE_WORKERS: int = 2
    
    # LLM配置
    LLM_MODEL: str = "Qwen/Qwen2.5-32B-Instruct"
    LLM_API_KEY: Optional[str] = None
    LLM_BASE_URL: str = "https://api.siliconflow.cn/v1"
    
    # 数据库配置
    MILVUS_URI: str = "http://************:19530"
    MILVUS_DIMENSION: int = 1024
    MILVUS_HOST: str = "************"
    MILVUS_PORT: int = 19530
    MILVUS_USER: str = ""
    MILVUS_PASSWORD: str = ""
    MILVUS_DB_NAME: str = "default"
    MILVUS_TIMEOUT: int = 30
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_PASSWORD: Optional[str] = None
    
    # MinIO配置
    MINIO_ENDPOINT: str = "localhost:9000"
    MINIO_ACCESS_KEY: str = "minioadmin"
    MINIO_SECRET_KEY: str = "minioadmin"
    MINIO_BUCKET_NAME: str = "ai-jiesujiban"
    
    # 检索配置
    RETRIEVAL_TOP_K_DEPARTMENT: int = 3
    RETRIEVAL_TOP_K_GUIDELINE: int = 3
    RETRIEVAL_TOP_K_HISTORICAL: int = 5
    RETRIEVAL_TOP_K_DELEGATED: int = 3
    
    # 重排序配置
    RERANK_MODEL: str = "BAAI/bge-reranker-v2-m3"
    RERANK_API_KEY: Optional[str] = None
    RERANK_BASE_URL: str = "https://api.siliconflow.cn/v1"
    
    # 安全配置
    ADMIN_API_KEY: str = "sk-de18e2cce5326daf8fbb6af4fb67aa71"
    
    # CORS配置（使用Union类型支持多种输入格式）
    CORS_ORIGINS: Union[str, List[str]] = Field(default="*")
    CORS_METHODS: Union[str, List[str]] = Field(default="GET,POST,PUT,DELETE,OPTIONS")
    CORS_HEADERS: Union[str, List[str]] = Field(default="*")


    
    # 性能配置
    REQUEST_TIMEOUT: int = 300
    GPU_REQUEST_TIMEOUT: int = 60
    GPU_SERVICE_TIMEOUT: int = 30
    MAX_CONCURRENT_REQUESTS: int = 100
    
    # 监控配置
    ENABLE_PERFORMANCE_LOGGING: bool = True
    PERFORMANCE_LOG_LEVEL: str = "INFO"
    HEALTH_CHECK_INTERVAL: int = 30
    HEALTH_CHECK_TIMEOUT: int = 10
    
    def get_cors_origins_list(self) -> List[str]:
        """获取CORS_ORIGINS的列表格式"""
        return self._parse_cors_value(self.CORS_ORIGINS)

    def get_cors_methods_list(self) -> List[str]:
        """获取CORS_METHODS的列表格式"""
        return self._parse_cors_value(self.CORS_METHODS)

    def get_cors_headers_list(self) -> List[str]:
        """获取CORS_HEADERS的列表格式"""
        return self._parse_cors_value(self.CORS_HEADERS)

    @staticmethod
    def _parse_cors_value(value: Union[str, List[str]]) -> List[str]:
        """优雅地解析CORS配置列表，支持多种输入格式"""
        if value is None:
            return ["*"]

        if isinstance(value, list):
            return value

        if isinstance(value, str):
            # 处理JSON格式
            if value.strip().startswith('[') and value.strip().endswith(']'):
                try:
                    parsed = json.loads(value.strip())
                    return parsed if isinstance(parsed, list) else [str(parsed)]
                except json.JSONDecodeError:
                    pass

            # 处理逗号分隔格式
            if ',' in value:
                return [item.strip() for item in value.split(',') if item.strip()]

            # 单个值
            return [value.strip()]

        # 其他类型转为字符串
        return [str(value)]

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": True
    }


@lru_cache()
def get_settings() -> Settings:
    """获取配置实例（单例模式）"""
    # 根据环境加载不同的配置文件
    env = os.getenv("ENVIRONMENT", "development")
    
    if env == "production":
        env_file = "configs/.env.prod"
    elif env == "testing":
        env_file = "configs/.env.test"
    else:
        env_file = "configs/.env.local"
    
    # 检查配置文件是否存在
    if os.path.exists(env_file):
        return Settings(_env_file=env_file)
    else:
        # 回退到默认配置
        return Settings()


# 全局配置实例（延迟初始化）
_settings_instance = None

def get_global_settings() -> Settings:
    """获取全局配置实例（延迟初始化）"""
    global _settings_instance
    if _settings_instance is None:
        _settings_instance = get_settings()
    return _settings_instance

# 为了向后兼容，保留settings变量，但使用延迟初始化
class SettingsProxy:
    """配置代理类，实现延迟初始化"""
    def __getattr__(self, name):
        return getattr(get_global_settings(), name)

settings = SettingsProxy()
