"""
GPU服务客户端

CPU服务通过此客户端调用GPU服务的BGE-M3稀疏嵌入功能
"""

import logging
import requests
import time
from typing import List, Dict, Optional
from llama_index.vector_stores.milvus.utils import BaseSparseEmbeddingFunction

from shared.config.settings import get_settings

logger = logging.getLogger(__name__)


class GPUServiceSparseEmbeddingFunction(BaseSparseEmbeddingFunction):
    """
    GPU服务稀疏嵌入函数
    
    通过HTTP调用GPU容器的BGE-M3稀疏嵌入服务
    """
    
    def __init__(
        self,
        gpu_service_url: str = None,
        batch_size: int = None,
        max_retries: int = None,
        timeout: int = None
    ):
        """
        初始化GPU服务稀疏嵌入函数

        Args:
            gpu_service_url: GPU服务URL
            batch_size: 批处理大小（None=使用配置值）
            max_retries: 最大重试次数（None=使用配置值）
            timeout: 请求超时时间（秒）（None=使用配置值）
        """
        settings = get_settings()

        self.gpu_service_url = gpu_service_url or self._get_gpu_service_url()
        self.batch_size = batch_size or getattr(settings, 'GPU_SPARSE_BATCH_SIZE', 32)
        self.max_retries = max_retries or 3
        self.timeout = timeout or getattr(settings, 'GPU_SPARSE_TIMEOUT', 120)
        
        # 构建API端点
        if self.gpu_service_url.endswith('/'):
            self.gpu_service_url = self.gpu_service_url[:-1]
        self.sparse_endpoint = f"{self.gpu_service_url}/embeddings/sparse"
        self.health_endpoint = f"{self.gpu_service_url}/health"

        logger.info(f"初始化GPU服务稀疏嵌入客户端: {self.gpu_service_url}")
        logger.info(f"批处理配置: batch_size={self.batch_size}")

        # 检查服务可用性
        self._check_service_health()
    
    def _get_gpu_service_url(self) -> str:
        """获取GPU服务URL"""
        settings = get_settings()
        gpu_service_url = settings.GPU_SERVICE_URL

        if not gpu_service_url:
            # 根据环境选择默认配置
            if settings.ENVIRONMENT == "development":
                # 本地开发环境使用localhost
                gpu_service_url = "http://localhost:8001"
                logger.info(f"使用本地开发GPU服务URL: {gpu_service_url}")
            else:
                # 生产环境使用Docker服务名
                gpu_service_url = "http://gpu-service:8001"
                logger.info(f"使用生产环境GPU服务URL: {gpu_service_url}")

        return gpu_service_url
    
    def _check_service_health(self):
        """检查GPU服务健康状态"""
        try:
            response = requests.get(
                self.health_endpoint,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                health_data = response.json()
                logger.info(f"GPU服务健康检查通过: {health_data}")
            else:
                logger.warning(f"GPU服务健康检查失败: {response.status_code}")
                
        except Exception as e:
            logger.warning(f"GPU服务健康检查异常: {str(e)}")
    
    def _call_gpu_service(self, texts: List[str]) -> List[Dict[int, float]]:
        """
        调用GPU服务获取稀疏嵌入
        
        Args:
            texts: 文本列表
            
        Returns:
            稀疏嵌入列表
        """
        request_data = {
            "texts": texts,
            "batch_size": self.batch_size
        }

        for attempt in range(self.max_retries):
            try:
                logger.debug(f"GPU服务调用尝试 {attempt + 1}/{self.max_retries}: {len(texts)} 个文本")

                response = requests.post(
                    self.sparse_endpoint,
                    json=request_data,
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    embeddings = result.get("embeddings", [])
                    processing_time = result.get("processing_time", 0)
                    
                    logger.debug(f"GPU服务调用成功: {len(embeddings)} 个嵌入, 耗时: {processing_time:.3f}秒")
                    return embeddings
                else:
                    logger.error(f"GPU服务调用失败，状态码: {response.status_code}, 响应: {response.text}")
                    
            except requests.exceptions.Timeout:
                logger.warning(f"GPU服务调用超时 (尝试 {attempt + 1}/{self.max_retries})")
            except requests.exceptions.RequestException as e:
                logger.error(f"GPU服务调用异常 (尝试 {attempt + 1}/{self.max_retries}): {str(e)}")
            except Exception as e:
                logger.error(f"处理GPU服务响应时出错 (尝试 {attempt + 1}/{self.max_retries}): {str(e)}")
            
            if attempt < self.max_retries - 1:
                wait_time = 2 ** attempt  # 指数退避
                logger.info(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
        
        # 所有重试都失败
        logger.error(f"GPU服务调用最终失败，尝试了 {self.max_retries} 次")
        raise RuntimeError(f"GPU服务稀疏嵌入调用失败，尝试了{self.max_retries}次")
    
    def encode_queries(self, queries: List[str]) -> List[Dict[int, float]]:
        """
        编码查询文本为稀疏嵌入
        
        Args:
            queries: 查询文本列表
            
        Returns:
            稀疏嵌入列表
        """
        if not queries:
            return []
        
        # 过滤空查询
        valid_queries = [q for q in queries if q and q.strip()]
        if len(valid_queries) != len(queries):
            logger.warning(f"过滤了 {len(queries) - len(valid_queries)} 个空查询")
        
        if not valid_queries:
            logger.warning("没有有效的查询文本")
            return [{}] * len(queries)
        
        logger.debug(f"编码 {len(valid_queries)} 个查询")
        
        try:
            embeddings = self._call_gpu_service(valid_queries)
            logger.debug(f"查询编码完成: {len(embeddings)} 个结果")
            return embeddings
        except Exception as e:
            logger.error(f"查询编码失败: {str(e)}")
            raise
    
    def encode_documents(self, documents: List[str]) -> List[Dict[int, float]]:
        """
        编码文档文本为稀疏嵌入（优化版本）

        Args:
            documents: 文档文本列表

        Returns:
            稀疏嵌入列表
        """
        if not documents:
            return []

        # 过滤空文档
        valid_documents = [doc for doc in documents if doc and doc.strip()]
        if len(valid_documents) != len(documents):
            logger.warning(f"过滤了 {len(documents) - len(valid_documents)} 个空文档")

        if not valid_documents:
            logger.warning("没有有效的文档文本")
            return [{}] * len(documents)

        logger.debug(f"编码 {len(valid_documents)} 个文档")

        try:
            # 简化处理：直接调用GPU服务
            embeddings = self._call_gpu_service(valid_documents)
            logger.debug(f"文档编码完成: {len(embeddings)} 个结果")
            return embeddings
        except Exception as e:
            logger.error(f"文档编码失败: {str(e)}")
            raise




class HybridSparseEmbeddingFunction(BaseSparseEmbeddingFunction):
    """
    混合稀疏嵌入函数
    
    优先使用GPU服务，失败时回退到本地模型
    """
    
    def __init__(self, **kwargs):
        """初始化混合稀疏嵌入函数"""
        self.gpu_service_function = None
        self.local_function = None
        
        # 尝试初始化GPU服务客户端
        try:
            self.gpu_service_function = GPUServiceSparseEmbeddingFunction(**kwargs)
            logger.info("GPU服务稀疏嵌入客户端初始化成功")
        except Exception as e:
            logger.warning(f"GPU服务稀疏嵌入客户端初始化失败: {str(e)}")
        
        logger.info("初始化混合稀疏嵌入函数（GPU服务优先，本地回退）")
    
    def _get_local_function(self):
        """获取本地稀疏嵌入函数（延迟初始化）"""
        if self.local_function is None:
            try:
                # 这里需要导入v2的本地稀疏嵌入函数
                # 由于我们在v3中，需要从v2复制相关代码
                from shared.utils.local_sparse_embedding import get_local_sparse_embedding_function
                self.local_function = get_local_sparse_embedding_function()
                logger.info("本地BGE-M3稀疏嵌入函数初始化成功")
            except Exception as e:
                logger.error(f"本地BGE-M3稀疏嵌入函数初始化失败: {str(e)}")
                self.local_function = None
        
        return self.local_function
    
    def _try_local_fallback(self, texts: List[str], method_name: str) -> List[Dict[int, float]]:
        """尝试本地回退"""
        local_func = self._get_local_function()
        if local_func:
            try:
                logger.warning(f"使用本地模型回退处理 {len(texts)} 个文本")
                if method_name == "encode_queries":
                    return local_func.encode_queries(texts)
                else:
                    return local_func.encode_documents(texts)
            except Exception as e:
                logger.error(f"本地回退也失败: {str(e)}")
        
        # 最终回退：返回空嵌入
        logger.error(f"所有稀疏嵌入策略都失败，返回空嵌入")
        return [{}] * len(texts)
    
    def encode_queries(self, queries: List[str]) -> List[Dict[int, float]]:
        """编码查询（GPU服务优先，本地回退）"""
        if self.gpu_service_function:
            try:
                return self.gpu_service_function.encode_queries(queries)
            except Exception as e:
                logger.warning(f"GPU服务查询编码失败，尝试本地回退: {str(e)}")
                return self._try_local_fallback(queries, "encode_queries")
        else:
            logger.warning("GPU服务不可用，直接使用本地回退")
            return self._try_local_fallback(queries, "encode_queries")
    
    def encode_documents(self, documents: List[str]) -> List[Dict[int, float]]:
        """编码文档（GPU服务优先，本地回退）"""
        if self.gpu_service_function:
            try:
                return self.gpu_service_function.encode_documents(documents)
            except Exception as e:
                logger.warning(f"GPU服务文档编码失败，尝试本地回退: {str(e)}")
                return self._try_local_fallback(documents, "encode_documents")
        else:
            logger.warning("GPU服务不可用，直接使用本地回退")
            return self._try_local_fallback(documents, "encode_documents")


# 工厂函数
def get_gpu_service_sparse_embedding_function(**kwargs) -> GPUServiceSparseEmbeddingFunction:
    """获取GPU服务稀疏嵌入函数"""
    return GPUServiceSparseEmbeddingFunction(**kwargs)


def get_hybrid_sparse_embedding_function(**kwargs) -> HybridSparseEmbeddingFunction:
    """获取混合稀疏嵌入函数（推荐）"""
    return HybridSparseEmbeddingFunction(**kwargs)


def get_production_sparse_embedding_function() -> HybridSparseEmbeddingFunction:
    """
    获取生产环境推荐的稀疏嵌入函数
    
    在生产环境中，优先使用GPU服务，失败时回退到本地模型
    """
    logger.info("使用生产环境稀疏嵌入策略：GPU服务优先，本地回退")
    return HybridSparseEmbeddingFunction()
