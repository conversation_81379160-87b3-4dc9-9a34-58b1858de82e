"""
嵌入客户端

支持多种嵌入模型的统一接口
"""

import logging
import requests
from typing import List, Optional, Dict, Any
from functools import lru_cache

from shared.config.settings import get_settings

# 导入LlamaIndex的BaseEmbedding
try:
    from llama_index.core.embeddings import BaseEmbedding
    LLAMA_INDEX_AVAILABLE = True
except ImportError:
    # 如果LlamaIndex不可用，创建一个基础类
    class BaseEmbedding:
        pass
    LLAMA_INDEX_AVAILABLE = False

logger = logging.getLogger(__name__)


class CustomAPIEmbedding(BaseEmbedding):
    """
    自定义API嵌入模型

    支持BGE-M3等非OpenAI模型
    """

    # Pydantic 模型配置
    model_config = {"arbitrary_types_allowed": True}

    def __init__(
        self,
        model: str,
        api_key: str,
        api_base: str,
        dimensions: int = 1024,
        max_retries: int = 3,
        timeout: int = 30,
        **kwargs
    ):
        # 调用父类构造函数
        super().__init__(**kwargs)

        # 设置私有属性（避免Pydantic字段验证）
        object.__setattr__(self, '_model', model)
        object.__setattr__(self, '_api_key', api_key)
        object.__setattr__(self, '_api_base', api_base.rstrip('/'))
        object.__setattr__(self, '_dimensions', dimensions)
        object.__setattr__(self, '_max_retries', max_retries)
        object.__setattr__(self, '_timeout', timeout)
        object.__setattr__(self, 'model_name', model)
        object.__setattr__(self, 'embed_batch_size', 64)  # API限制为64

        logger.info(f"初始化自定义API嵌入模型: {model}")

    @property
    def model(self) -> str:
        return getattr(self, '_model', 'unknown')

    @property
    def api_key(self) -> str:
        return getattr(self, '_api_key', '')

    @property
    def api_base(self) -> str:
        return getattr(self, '_api_base', '')

    @property
    def dimensions(self) -> int:
        return getattr(self, '_dimensions', 1024)

    def _get_text_embedding(self, text: str) -> List[float]:
        """LlamaIndex BaseEmbedding要求的抽象方法"""
        try:
            embeddings = get_dense_embeddings([text])
            return embeddings[0] if embeddings else [0.0] * self.dimensions
        except Exception as e:
            logger.error(f"获取文本嵌入失败: {str(e)}")
            return [0.0] * self.dimensions

    def _get_query_embedding(self, query: str) -> List[float]:
        """LlamaIndex BaseEmbedding要求的查询嵌入方法"""
        return self._get_text_embedding(query)

    async def _aget_query_embedding(self, query: str) -> List[float]:
        """LlamaIndex BaseEmbedding要求的异步查询嵌入方法"""
        return self._get_query_embedding(query)

    def get_text_embedding(self, text: str) -> List[float]:
        """获取单个文本的嵌入"""
        return self._get_text_embedding(text)
    
    def get_text_embedding_batch(self, texts: List[str], **kwargs) -> List[List[float]]:
        """批量获取文本嵌入"""
        try:
            # 忽略额外的参数如show_progress
            return get_dense_embeddings(texts)
        except Exception as e:
            logger.error(f"批量嵌入获取失败: {str(e)}")
            return [[0.0] * self.dimensions] * len(texts)
    
    async def aget_text_embedding(self, text: str) -> List[float]:
        """异步获取单个文本的嵌入"""
        return self.get_text_embedding(text)
    
    async def aget_text_embedding_batch(self, texts: List[str]) -> List[List[float]]:
        """异步批量获取文本嵌入"""
        return self.get_text_embedding_batch(texts)


def get_dense_embeddings(texts: List[str], batch_size: int = 64) -> List[List[float]]:
    """
    获取密集嵌入（支持批次处理）

    Args:
        texts: 文本列表
        batch_size: 批次大小，默认64（API限制）

    Returns:
        嵌入向量列表
    """
    if not texts:
        return []

    try:
        # 获取配置
        settings = get_settings()
        all_embeddings = []

        # 分批处理
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            logger.debug(f"处理批次 {i//batch_size + 1}: {len(batch_texts)} 个文本")

            # 构建请求
            headers = {
                "Authorization": f"Bearer {settings.API_EMBED_KEY}",
                "Content-Type": "application/json"
            }

            data = {
                "model": settings.API_EMBED_MODEL,
                "input": batch_texts,
                "encoding_format": "float"
            }

            # 发送请求
            response = requests.post(
                f"{settings.API_EMBED_BASE}/embeddings",
                headers=headers,
                json=data,
                timeout=60  # 增加超时时间
            )

            if response.status_code == 200:
                result = response.json()
                batch_embeddings = []

                # 提取嵌入向量
                for item in result.get("data", []):
                    embedding = item.get("embedding", [])
                    batch_embeddings.append(embedding)

                all_embeddings.extend(batch_embeddings)
                logger.debug(f"批次 {i//batch_size + 1} 成功获取 {len(batch_embeddings)} 个嵌入")

            else:
                logger.error(f"批次 {i//batch_size + 1} 密集嵌入API请求失败: {response.status_code}, {response.text}")
                # 为失败的批次添加零向量
                fallback_embeddings = [[0.0] * settings.MILVUS_DIMENSION] * len(batch_texts)
                all_embeddings.extend(fallback_embeddings)

        logger.info(f"成功获取 {len(all_embeddings)} 个密集嵌入（共 {len(texts)} 个文本）")
        return all_embeddings

    except Exception as e:
        logger.error(f"密集嵌入获取异常: {str(e)}")
        # 获取配置用于回退
        try:
            settings = get_settings()
            return [[0.0] * settings.MILVUS_DIMENSION] * len(texts)
        except:
            # 如果连配置都获取不到，使用默认维度
            return [[0.0] * 1024] * len(texts)


def get_rerank_scores(query: str, documents: List[str]) -> List[float]:
    """
    获取重排序分数

    Args:
        query: 查询文本
        documents: 文档列表

    Returns:
        重排序分数列表
    """
    try:
        # 获取配置
        settings = get_settings()

        # 构建请求
        headers = {
            "Authorization": f"Bearer {settings.RERANK_API_KEY}",
            "Content-Type": "application/json"
        }

        data = {
            "model": settings.RERANK_MODEL,
            "query": query,
            "documents": documents,
            "return_documents": False
        }

        # 发送请求
        response = requests.post(
            f"{settings.RERANK_BASE_URL}/rerank",
            headers=headers,
            json=data,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            scores = []

            # 提取重排序分数
            for item in result.get("results", []):
                score = item.get("relevance_score", 0.0)
                scores.append(score)

            logger.debug(f"成功获取 {len(scores)} 个重排序分数")
            return scores
        else:
            logger.error(f"重排序API请求失败: {response.status_code}, {response.text}")
            return [0.5] * len(documents)  # 返回中性分数

    except Exception as e:
        logger.error(f"重排序分数获取异常: {str(e)}")
        return [0.5] * len(documents)


@lru_cache()
def get_embedding_client() -> CustomAPIEmbedding:
    """获取嵌入客户端实例"""
    settings = get_settings()
    return CustomAPIEmbedding(
        model=settings.API_EMBED_MODEL,
        api_key=settings.API_EMBED_KEY,
        api_base=settings.API_EMBED_BASE,
        dimensions=settings.MILVUS_DIMENSION
    )


def test_embedding_client():
    """测试嵌入客户端"""
    try:
        client = get_embedding_client()
        
        # 测试单个文本嵌入
        test_text = "这是一个测试文本"
        embedding = client.get_text_embedding(test_text)
        
        logger.info(f"单个文本嵌入测试成功: 维度 {len(embedding)}")
        
        # 测试批量文本嵌入
        test_texts = ["文本1", "文本2", "文本3"]
        embeddings = client.get_text_embedding_batch(test_texts)
        
        logger.info(f"批量文本嵌入测试成功: {len(embeddings)} 个嵌入")
        
        return True
        
    except Exception as e:
        logger.error(f"嵌入客户端测试失败: {str(e)}")
        return False


def test_dense_embeddings():
    """测试密集嵌入功能"""
    try:
        test_texts = ["测试文本1", "测试文本2"]
        embeddings = get_dense_embeddings(test_texts)
        
        if embeddings and len(embeddings) == len(test_texts):
            logger.info(f"密集嵌入测试成功: {len(embeddings)} 个嵌入，维度 {len(embeddings[0])}")
            return True
        else:
            logger.error("密集嵌入测试失败: 返回结果不正确")
            return False
            
    except Exception as e:
        logger.error(f"密集嵌入测试失败: {str(e)}")
        return False


def test_rerank_scores():
    """测试重排序功能"""
    try:
        query = "食品安全问题"
        documents = [
            "市场监督管理局负责食品安全监管",
            "城市管理委员会负责环境卫生",
            "教育局负责学校管理"
        ]
        
        scores = get_rerank_scores(query, documents)
        
        if scores and len(scores) == len(documents):
            logger.info(f"重排序测试成功: {len(scores)} 个分数")
            return True
        else:
            logger.error("重排序测试失败: 返回结果不正确")
            return False
            
    except Exception as e:
        logger.error(f"重排序测试失败: {str(e)}")
        return False


if __name__ == "__main__":
    # 运行测试
    logging.basicConfig(level=logging.INFO)
    
    logger.info("开始测试嵌入客户端...")
    
    # 测试嵌入客户端
    client_test = test_embedding_client()
    
    # 测试密集嵌入
    dense_test = test_dense_embeddings()
    
    # 测试重排序
    rerank_test = test_rerank_scores()
    
    # 汇总结果
    logger.info(f"测试结果: 客户端={client_test}, 密集嵌入={dense_test}, 重排序={rerank_test}")
    
    if all([client_test, dense_test, rerank_test]):
        logger.info("✅ 所有测试通过")
    else:
        logger.error("❌ 部分测试失败")
