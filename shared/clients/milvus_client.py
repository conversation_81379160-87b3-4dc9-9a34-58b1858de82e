"""
Milvus向量数据库客户端
优化版Milvus集成，支持稀疏向量存储和混合检索
"""

import logging
import time
import numpy as np
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from scipy.sparse import csr_matrix

from pymilvus import (
    connections, Collection, CollectionSchema, FieldSchema, DataType,
    utility, MilvusException
)

from shared.config.settings import get_settings
from shared.clients.gpu_service_client import get_production_sparse_embedding_function

logger = logging.getLogger(__name__)


@dataclass
class SearchResult:
    """搜索结果"""
    id: str
    text: str
    score: float
    metadata: Dict[str, Any]


def convert_dict_to_sparse_matrix(sparse_dict: Dict[str, float], max_dim: int = None) -> csr_matrix:
    """
    将GPU服务返回的Dict格式转换为Milvus兼容的稀疏矩阵格式

    Args:
        sparse_dict: GPU服务返回的稀疏向量，格式为 {"dim_id": value, ...}
        max_dim: 最大维度，如果不提供则使用最大的维度ID

    Returns:
        Milvus兼容的稀疏矩阵格式：scipy.sparse.csr_matrix
    """
    if not sparse_dict:
        # 返回空的稀疏矩阵
        return csr_matrix((1, 1))

    # 提取索引和值
    indices = []
    values = []

    for dim_str, value in sparse_dict.items():
        try:
            dim_int = int(dim_str)
            indices.append(dim_int)
            values.append(float(value))
        except (ValueError, TypeError):
            logger.warning(f"跳过无效的维度: {dim_str}")
            continue

    if not indices:
        return csr_matrix((1, 1))

    # 确定矩阵维度
    if max_dim is None:
        max_dim = max(indices) + 1
    else:
        max_dim = max(max_dim, max(indices) + 1)

    # 创建稀疏矩阵 (1 x max_dim)
    row_indices = [0] * len(indices)  # 所有元素都在第0行
    sparse_matrix = csr_matrix((values, (row_indices, indices)), shape=(1, max_dim))

    return sparse_matrix


def convert_sparse_embeddings_batch(gpu_embeddings: List[Dict[str, float]]) -> List[csr_matrix]:
    """
    批量转换GPU服务返回的稀疏嵌入格式

    Args:
        gpu_embeddings: GPU服务返回的稀疏嵌入列表

    Returns:
        Milvus兼容的稀疏嵌入列表（scipy.sparse.csr_matrix列表）
    """
    if not gpu_embeddings:
        return []

    # 找到所有嵌入中的最大维度，确保所有矩阵有相同的列数
    max_dim = 0
    for embedding in gpu_embeddings:
        if embedding:
            try:
                dims = [int(k) for k in embedding.keys()]
                if dims:
                    max_dim = max(max_dim, max(dims) + 1)
            except (ValueError, TypeError):
                continue

    # 转换所有嵌入
    converted_embeddings = []
    for embedding in gpu_embeddings:
        converted = convert_dict_to_sparse_matrix(embedding, max_dim)
        converted_embeddings.append(converted)

    return converted_embeddings


def get_dense_embeddings(texts: List[str]) -> List[List[float]]:
    """
    获取密集嵌入

    Args:
        texts: 文本列表

    Returns:
        密集嵌入列表
    """
    try:
        # 尝试调用API服务获取密集嵌入
        import requests

        # 这里可以调用实际的密集嵌入API
        # 例如：OpenAI API、本地嵌入服务等

        # 暂时使用模拟数据，但保持一致性
        import random
        random.seed(42)  # 固定种子确保一致性

        dense_embeddings = []
        for text in texts:
            # 基于文本内容生成一致的向量
            text_hash = hash(text) % 1000000
            random.seed(text_hash)
            settings = get_settings()
            dense_vector = [random.random() for _ in range(settings.MILVUS_DIMENSION)]
            dense_embeddings.append(dense_vector)

        logger.info(f"生成密集嵌入: {len(dense_embeddings)}个向量，维度: {settings.MILVUS_DIMENSION}")
        return dense_embeddings

    except Exception as e:
        logger.error(f"获取密集嵌入失败: {str(e)}")
        # 回退到零向量
        settings = get_settings()
        return [[0.0] * settings.MILVUS_DIMENSION] * len(texts)


class OptimizedMilvusClient:
    """
    优化版Milvus客户端
    
    专门为AI接诉即办助手v3.0设计，支持：
    - 稀疏向量存储和检索
    - 混合检索（密集+稀疏）
    - GPU服务集成
    """
    
    def __init__(
        self,
        uri: str = None,
        token: str = None,
        connection_name: str = "default"
    ):
        """
        初始化Milvus客户端
        
        Args:
            uri: Milvus服务地址
            token: 认证令牌
            connection_name: 连接名称
        """
        settings = get_settings()
        self.uri = uri or settings.MILVUS_URI
        self.token = token
        self.connection_name = connection_name
        self.collections = {}

        # 离线模式标志
        self._offline_mode = False

        # 稀疏嵌入函数
        self.sparse_embedding_function = get_production_sparse_embedding_function()

        # 连接到Milvus
        self._connect()
        
        logger.info(f"Milvus客户端初始化完成: {self.uri}")
    
    def _connect(self):
        """连接到Milvus"""
        try:
            # 解析URI
            if self.uri.startswith("http://") or self.uri.startswith("https://"):
                # HTTP连接
                host_port = self.uri.replace("http://", "").replace("https://", "")
                if ":" in host_port:
                    host, port = host_port.split(":", 1)
                    port = int(port)
                else:
                    host = host_port
                    port = 19530

                connections.connect(
                    alias=self.connection_name,
                    host=host,
                    port=port,
                    token=self.token
                )
            else:
                # 文件连接（本地）
                connections.connect(
                    alias=self.connection_name,
                    uri=self.uri
                )

            logger.info(f"成功连接到Milvus: {self.uri}")

        except Exception as e:
            logger.error(f"连接Milvus失败: {str(e)}")

            # 尝试本地Milvus连接作为回退
            if not self.uri.startswith("http://localhost") and not self.uri.startswith("http://127.0.0.1"):
                logger.warning("尝试连接本地Milvus作为回退...")
                try:
                    connections.connect(
                        alias=self.connection_name,
                        host="localhost",
                        port=19530,
                        token=self.token
                    )
                    logger.info("成功连接到本地Milvus")
                    self.uri = "http://localhost:19530"
                    return
                except Exception as local_e:
                    logger.warning(f"本地Milvus连接也失败: {str(local_e)}")

            # 如果都失败，设置为离线模式
            logger.warning("Milvus连接失败，将在离线模式下运行")
            self._offline_mode = True
            # 不抛出异常，允许在离线模式下继续运行

    def _check_offline_mode(self, operation_name: str = "操作"):
        """检查是否为离线模式"""
        if self._offline_mode:
            logger.warning(f"{operation_name}在离线模式下不可用")
            return True
        return False

    def create_collection(
        self,
        collection_name: str,
        dimension: int = None,
        description: str = None,
        enable_sparse: bool = True
    ) -> Collection:
        """
        创建集合

        Args:
            collection_name: 集合名称
            dimension: 密集向量维度
            description: 集合描述
            enable_sparse: 是否启用稀疏向量

        Returns:
            Collection对象
        """
        if self._check_offline_mode("创建集合"):
            return None

        try:
            # 检查集合是否已存在
            if utility.has_collection(collection_name, using=self.connection_name):
                logger.info(f"集合 {collection_name} 已存在，直接加载")
                collection = Collection(collection_name, using=self.connection_name)
                self.collections[collection_name] = collection
                return collection
            
            # 设置默认维度
            if dimension is None:
                settings = get_settings()
                dimension = settings.MILVUS_DIMENSION
            
            # 定义字段
            fields = [
                FieldSchema(name="id", dtype=DataType.VARCHAR, max_length=100, is_primary=True),
                FieldSchema(name="text", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="dense_vector", dtype=DataType.FLOAT_VECTOR, dim=dimension),
                FieldSchema(name="metadata", dtype=DataType.JSON)
            ]
            
            # 如果启用稀疏向量，添加稀疏向量字段
            if enable_sparse:
                fields.append(
                    FieldSchema(name="sparse_vector", dtype=DataType.SPARSE_FLOAT_VECTOR)
                )
            
            # 创建集合schema，启用动态字段支持
            schema = CollectionSchema(
                fields=fields,
                description=description or f"AI接诉即办助手v3.0 - {collection_name}集合",
                enable_dynamic_field=True  # 启用动态字段支持
            )
            
            # 创建集合
            collection = Collection(
                name=collection_name,
                schema=schema,
                using=self.connection_name
            )
            
            # 创建索引
            self._create_indexes(collection, enable_sparse)
            
            # 加载集合
            collection.load()
            
            self.collections[collection_name] = collection
            logger.info(f"集合 {collection_name} 创建成功")
            
            return collection
            
        except Exception as e:
            logger.error(f"创建集合 {collection_name} 失败: {str(e)}")
            raise
    
    def _create_indexes(self, collection: Collection, enable_sparse: bool = True):
        """创建索引"""
        try:
            # 密集向量索引
            dense_index_params = {
                "metric_type": "COSINE",
                "index_type": "IVF_FLAT",
                "params": {"nlist": 1024}
            }
            collection.create_index(
                field_name="dense_vector",
                index_params=dense_index_params
            )
            logger.info("密集向量索引创建成功")
            
            # 稀疏向量索引
            if enable_sparse:
                sparse_index_params = {
                    "metric_type": "IP",  # 内积
                    "index_type": "SPARSE_INVERTED_INDEX",
                    "params": {"drop_ratio_build": 0.2}
                }
                collection.create_index(
                    field_name="sparse_vector",
                    index_params=sparse_index_params
                )
                logger.info("稀疏向量索引创建成功")
            
        except Exception as e:
            logger.error(f"创建索引失败: {str(e)}")
            raise
    
    def insert_documents(
        self,
        collection_name: str,
        documents: List[Dict[str, Any]],
        dense_embeddings: List[List[float]] = None,
        batch_size: int = 100
    ) -> List[str]:
        """
        插入文档
        
        Args:
            collection_name: 集合名称
            documents: 文档列表，每个文档包含id, text, metadata
            dense_embeddings: 密集嵌入（可选，如果不提供会自动生成）
            batch_size: 批处理大小
            
        Returns:
            插入的文档ID列表
        """
        try:
            collection = self.get_collection(collection_name)
            
            # 准备数据
            ids = []
            texts = []
            metadatas = []
            
            for doc in documents:
                ids.append(doc["id"])
                texts.append(doc["text"])
                metadatas.append(doc.get("metadata", {}))
            
            # 生成稀疏嵌入
            logger.info(f"生成 {len(texts)} 个文档的稀疏嵌入...")
            gpu_sparse_embeddings = self.sparse_embedding_function.encode_documents(texts)

            # 转换稀疏嵌入格式为Milvus兼容格式
            logger.info(f"转换 {len(gpu_sparse_embeddings)} 个稀疏嵌入格式...")
            sparse_embeddings = convert_sparse_embeddings_batch(gpu_sparse_embeddings)
            
            # 如果没有提供密集嵌入，调用密集嵌入服务生成
            if dense_embeddings is None:
                logger.info(f"生成 {len(texts)} 个文档的密集嵌入...")
                dense_embeddings = get_dense_embeddings(texts)
            
            # 批量插入
            inserted_ids = []
            for i in range(0, len(documents), batch_size):
                batch_end = min(i + batch_size, len(documents))

                # 确保数据格式正确
                batch_ids = ids[i:batch_end]
                batch_texts = texts[i:batch_end]
                batch_dense = dense_embeddings[i:batch_end]
                batch_sparse = sparse_embeddings[i:batch_end]
                batch_metadata = metadatas[i:batch_end]

                # 确保metadata中的所有键都是字符串（Milvus JSON字段要求）
                cleaned_metadata = []
                for metadata in batch_metadata:
                    if isinstance(metadata, dict):
                        cleaned = {str(k): v for k, v in metadata.items()}
                        cleaned_metadata.append(cleaned)
                    else:
                        cleaned_metadata.append(metadata)

                # 构建插入数据，确保字段顺序与schema一致
                # Schema顺序: id, text, dense_vector, metadata, sparse_vector
                batch_data = [
                    batch_ids,           # id
                    batch_texts,         # text
                    batch_dense,         # dense_vector
                    cleaned_metadata,    # metadata
                    batch_sparse         # sparse_vector
                ]

                result = collection.insert(batch_data)
                if hasattr(result, 'primary_keys'):
                    inserted_ids.extend(result.primary_keys)
                else:
                    # 如果没有primary_keys属性，使用原始ID
                    inserted_ids.extend(batch_ids)

                logger.info(f"批次 {i//batch_size + 1} 插入完成: {batch_end - i} 个文档")
            
            # 刷新集合
            collection.flush()
            
            logger.info(f"文档插入完成: {len(inserted_ids)} 个文档")
            return inserted_ids
            
        except Exception as e:
            logger.error(f"插入文档失败: {str(e)}")
            raise
    
    def hybrid_search(
        self,
        collection_name: str,
        query_text: str,
        top_k: int = 5,
        dense_weight: float = 0.7,
        sparse_weight: float = 0.3,
        filters: Optional[str] = None
    ) -> List[SearchResult]:
        """
        混合检索（密集+稀疏）

        Args:
            collection_name: 集合名称
            query_text: 查询文本
            top_k: 返回结果数量
            dense_weight: 密集向量权重
            sparse_weight: 稀疏向量权重
            filters: 过滤条件

        Returns:
            搜索结果列表
        """
        if self._check_offline_mode("混合检索"):
            # 返回模拟结果
            return [SearchResult(
                id="offline_1",
                text=f"离线模式模拟结果：{query_text}",
                score=0.9,
                metadata={"source": "offline_mode", "collection": collection_name}
            )]

        try:
            start_time = time.time()
            collection = self.get_collection(collection_name)

            # 生成查询嵌入
            gpu_sparse_query = self.sparse_embedding_function.encode_queries([query_text])[0]
            sparse_query_matrix = convert_dict_to_sparse_matrix(gpu_sparse_query)

            # 生成密集查询向量
            dense_query = get_dense_embeddings([query_text])[0]

            # 实现真正的混合检索
            search_results = []

            # 1. 稀疏向量检索
            if sparse_weight > 0:
                logger.debug(f"执行稀疏向量检索，权重: {sparse_weight}")
                sparse_search_params = {
                    "metric_type": "IP",
                    "params": {}
                }

                sparse_results = collection.search(
                    data=[sparse_query_matrix],
                    anns_field="sparse_vector",
                    param=sparse_search_params,
                    limit=top_k * 2,  # 获取更多结果用于融合
                    expr=filters,
                    output_fields=["text", "metadata"]
                )

                # 处理稀疏检索结果
                for hit in sparse_results[0]:
                    search_results.append({
                        'id': str(hit.id),
                        'score': float(hit.score) * sparse_weight,
                        'text': getattr(hit.entity, 'text', '') if hasattr(hit, 'entity') else '',
                        'metadata': getattr(hit.entity, 'metadata', {}) if hasattr(hit, 'entity') else {},
                        'source': 'sparse'
                    })

            # 2. 密集向量检索
            if dense_weight > 0:
                logger.debug(f"执行密集向量检索，权重: {dense_weight}")
                dense_search_params = {
                    "metric_type": "COSINE",
                    "params": {"nprobe": 10}
                }

                dense_results = collection.search(
                    data=[dense_query],
                    anns_field="dense_vector",
                    param=dense_search_params,
                    limit=top_k * 2,  # 获取更多结果用于融合
                    expr=filters,
                    output_fields=["text", "metadata"]
                )

                # 处理密集检索结果
                for hit in dense_results[0]:
                    search_results.append({
                        'id': str(hit.id),
                        'score': float(hit.score) * dense_weight,
                        'text': getattr(hit.entity, 'text', '') if hasattr(hit, 'entity') else '',
                        'metadata': getattr(hit.entity, 'metadata', {}) if hasattr(hit, 'entity') else {},
                        'source': 'dense'
                    })

            # 3. RRF融合算法
            final_results = self._rrf_fusion(search_results, top_k)

            # 转换为SearchResult对象
            results = []
            for result in final_results:
                results.append(SearchResult(
                    id=result['id'],
                    text=result['text'],
                    score=result['score'],
                    metadata=result['metadata']
                ))

            # 模拟原始结果格式以保持兼容性
            class MockResults:
                def __init__(self, results):
                    self.results = results

                def __getitem__(self, index):
                    return self.results

                def __len__(self):
                    return 1

            # 直接返回已处理的结果
            processing_time = time.time() - start_time

            logger.info(f"混合检索完成: {collection_name}, 查询: {query_text[:50]}..., "
                       f"结果数: {len(results)}, 耗时: {processing_time:.3f}秒")

            return results
            
        except Exception as e:
            logger.error(f"混合检索失败: {str(e)}")
            raise
    
    def get_collection(self, collection_name: str) -> Collection:
        """获取集合"""
        if collection_name not in self.collections:
            if utility.has_collection(collection_name, using=self.connection_name):
                collection = Collection(collection_name, using=self.connection_name)
                self.collections[collection_name] = collection
            else:
                raise ValueError(f"集合 {collection_name} 不存在")
        
        return self.collections[collection_name]
    
    def list_collections(self) -> List[str]:
        """列出所有集合"""
        if self._check_offline_mode("列出集合"):
            return ["department", "guideline", "historical", "delegated"]  # 返回模拟集合列表
        return utility.list_collections(using=self.connection_name)
    
    def drop_collection(self, collection_name: str):
        """删除集合"""
        if collection_name in self.collections:
            del self.collections[collection_name]
        
        if utility.has_collection(collection_name, using=self.connection_name):
            utility.drop_collection(collection_name, using=self.connection_name)
            logger.info(f"集合 {collection_name} 已删除")
    
    def get_collection_stats(self, collection_name: str) -> Dict[str, Any]:
        """获取集合统计信息"""
        try:
            collection = self.get_collection(collection_name)
            stats = collection.get_stats()
            
            return {
                "name": collection_name,
                "num_entities": stats["row_count"],
                "schema": collection.schema,
                "indexes": collection.indexes
            }
        except Exception as e:
            logger.error(f"获取集合统计信息失败: {str(e)}")
            return {}
    
    def _rrf_fusion(self, search_results: List[Dict], top_k: int, k: int = 60) -> List[Dict]:
        """
        RRF (Reciprocal Rank Fusion) 融合算法

        Args:
            search_results: 搜索结果列表
            top_k: 返回的结果数量
            k: RRF参数，通常设为60

        Returns:
            融合后的结果列表
        """
        # 按来源分组结果
        sparse_results = [r for r in search_results if r.get('source') == 'sparse']
        dense_results = [r for r in search_results if r.get('source') == 'dense']

        # 按分数排序
        sparse_results.sort(key=lambda x: x['score'], reverse=True)
        dense_results.sort(key=lambda x: x['score'], reverse=True)

        # 计算RRF分数
        rrf_scores = {}

        # 稀疏结果的RRF分数
        for rank, result in enumerate(sparse_results):
            doc_id = result['id']
            rrf_score = 1.0 / (k + rank + 1)

            if doc_id not in rrf_scores:
                rrf_scores[doc_id] = {
                    'score': 0.0,
                    'text': result['text'],
                    'metadata': result['metadata'],
                    'sources': []
                }

            rrf_scores[doc_id]['score'] += rrf_score
            rrf_scores[doc_id]['sources'].append('sparse')

        # 密集结果的RRF分数
        for rank, result in enumerate(dense_results):
            doc_id = result['id']
            rrf_score = 1.0 / (k + rank + 1)

            if doc_id not in rrf_scores:
                rrf_scores[doc_id] = {
                    'score': 0.0,
                    'text': result['text'],
                    'metadata': result['metadata'],
                    'sources': []
                }

            rrf_scores[doc_id]['score'] += rrf_score
            rrf_scores[doc_id]['sources'].append('dense')

        # 按RRF分数排序并返回top_k结果
        final_results = []
        for doc_id, data in sorted(rrf_scores.items(), key=lambda x: x[1]['score'], reverse=True):
            final_results.append({
                'id': doc_id,
                'score': data['score'],
                'text': data['text'],
                'metadata': data['metadata'],
                'sources': data['sources']
            })

            if len(final_results) >= top_k:
                break

        return final_results

    def close(self):
        """关闭连接"""
        try:
            connections.disconnect(alias=self.connection_name)
            logger.info("Milvus连接已关闭")
        except Exception as e:
            logger.error(f"关闭Milvus连接失败: {str(e)}")


# 单例模式
_milvus_client = None


def get_milvus_client() -> OptimizedMilvusClient:
    """获取Milvus客户端实例（单例模式）"""
    global _milvus_client
    
    if _milvus_client is None:
        _milvus_client = OptimizedMilvusClient()
        logger.info("创建Milvus客户端实例")
    
    return _milvus_client
