"""
本地稀疏嵌入回退函数

当GPU服务不可用时，使用本地BGE-M3模型作为回退
"""

import logging
import sys
from typing import List, Dict, Optional
from llama_index.vector_stores.milvus.utils import BaseSparseEmbeddingFunction, BM25BuiltInFunction

from shared.config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class LocalBGEM3SparseEmbeddingFunction(BaseSparseEmbeddingFunction):
    """
    本地BGE-M3稀疏嵌入函数
    
    作为GPU服务的回退方案
    """
    
    def __init__(self, model_path: str = None) -> None:
        """
        初始化本地BGE-M3稀疏嵌入函数
        
        Args:
            model_path: 模型路径
        """
        from shared.config.settings import get_settings
        settings = get_settings()
        self.model_path = model_path or settings.BGE_M3_MODEL_PATH
        self.model = None
        
        try:
            from FlagEmbedding import BGEM3FlagModel
            # 使用配置中的模型路径，支持本地路径或在线地址
            self.model = BGEM3FlagModel(self.model_path, use_fp16=False)
            logger.info(f"本地BGE-M3稀疏嵌入函数初始化成功: {self.model_path}")

        except ImportError as e:
            error_info = (
                "Cannot import BGEM3FlagModel from FlagEmbedding. It seems it is not installed. "
                "Please install it using:\n"
                "pip install FlagEmbedding\n"
            )
            logger.error(error_info)
            raise ImportError(error_info) from e
        except Exception as e:
            error_info = f"Failed to initialize local BGE-M3 model: {str(e)}"
            logger.error(error_info)
            raise RuntimeError(error_info) from e

    def encode_queries(self, queries: List[str]) -> List[Dict[int, float]]:
        """
        编码查询为稀疏嵌入
        
        Args:
            queries: 查询文本列表
            
        Returns:
            稀疏嵌入列表
        """
        if not self.model:
            raise RuntimeError("本地BGE-M3模型未初始化")
        
        try:
            # 使用BGE-M3模型进行稀疏编码
            embeddings = self.model.encode(
                queries,
                return_dense=False,
                return_sparse=True,
                return_colbert_vecs=False
            )
            
            # 转换为标准格式
            sparse_embeddings = []
            for embedding in embeddings['lexical_weights']:
                sparse_dict = {}
                # 检查嵌入格式并适配
                if hasattr(embedding, 'indices') and hasattr(embedding, 'values'):
                    # 稀疏张量格式
                    for idx, weight in zip(embedding.indices, embedding.values):
                        sparse_dict[int(idx)] = float(weight)
                elif isinstance(embedding, dict):
                    # 字典格式
                    sparse_dict = {int(k): float(v) for k, v in embedding.items()}
                else:
                    # 其他格式，尝试直接转换
                    try:
                        sparse_dict = dict(embedding)
                    except:
                        logger.warning(f"无法解析稀疏嵌入格式: {type(embedding)}")
                        sparse_dict = {}
                sparse_embeddings.append(sparse_dict)
            
            logger.debug(f"本地BGE-M3查询编码完成: {len(queries)} -> {len(sparse_embeddings)}")
            return sparse_embeddings
            
        except Exception as e:
            logger.error(f"本地BGE-M3查询编码失败: {str(e)}")
            raise

    def encode_documents(self, documents: List[str]) -> List[Dict[int, float]]:
        """
        编码文档为稀疏嵌入
        
        Args:
            documents: 文档文本列表
            
        Returns:
            稀疏嵌入列表
        """
        if not self.model:
            raise RuntimeError("本地BGE-M3模型未初始化")
        
        try:
            # 使用BGE-M3模型进行稀疏编码
            embeddings = self.model.encode(
                documents,
                return_dense=False,
                return_sparse=True,
                return_colbert_vecs=False
            )
            
            # 转换为标准格式
            sparse_embeddings = []
            for embedding in embeddings['lexical_weights']:
                sparse_dict = {}
                # 检查嵌入格式并适配
                if hasattr(embedding, 'indices') and hasattr(embedding, 'values'):
                    # 稀疏张量格式
                    for idx, weight in zip(embedding.indices, embedding.values):
                        sparse_dict[int(idx)] = float(weight)
                elif isinstance(embedding, dict):
                    # 字典格式
                    sparse_dict = {int(k): float(v) for k, v in embedding.items()}
                else:
                    # 其他格式，尝试直接转换
                    try:
                        sparse_dict = dict(embedding)
                    except:
                        logger.warning(f"无法解析稀疏嵌入格式: {type(embedding)}")
                        sparse_dict = {}
                sparse_embeddings.append(sparse_dict)
            
            logger.debug(f"本地BGE-M3文档编码完成: {len(documents)} -> {len(sparse_embeddings)}")
            return sparse_embeddings
            
        except Exception as e:
            logger.error(f"本地BGE-M3文档编码失败: {str(e)}")
            raise


class SmartSparseEmbeddingFunction(BaseSparseEmbeddingFunction):
    """
    智能稀疏嵌入函数
    
    自动选择最佳的稀疏嵌入策略：
    1. 本地BGE-M3稀疏嵌入
    2. BM25内置函数（回退）
    """
    
    def __init__(self, prefer_local: bool = True):
        """
        初始化智能稀疏嵌入函数
        
        Args:
            prefer_local: 是否优先使用本地模型
        """
        self.prefer_local = prefer_local
        
        # 延迟初始化各种嵌入函数
        self.local_function = None
        self.bm25_function = None
        
        # 记录当前使用的策略
        self.current_strategy = None
        
        logger.info(f"初始化智能稀疏嵌入函数: 本地优先={prefer_local}")
        
        # 自动检测最佳策略
        self._detect_best_strategy()
    
    def _detect_best_strategy(self):
        """检测并设置最佳的稀疏嵌入策略"""
        
        # 跳过API稀疏嵌入检测，因为API服务不支持稀疏嵌入
        logger.info("跳过API稀疏嵌入检测（API服务不支持稀疏嵌入）")
        
        # 1. 尝试本地BGE-M3稀疏嵌入
        if self.prefer_local:
            try:
                self.local_function = LocalBGEM3SparseEmbeddingFunction()
                
                # 测试本地模型是否可用
                test_result = self.local_function.encode_queries(["测试"])
                if test_result and len(test_result) > 0:
                    self.current_strategy = "local"
                    logger.info("✅ 检测到本地BGE-M3稀疏嵌入可用，使用本地策略")
                    return
                    
            except Exception as e:
                logger.warning(f"本地BGE-M3稀疏嵌入不可用: {str(e)}")
        
        # 2. 最终回退到BM25
        try:
            self.bm25_function = BM25BuiltInFunction()
            self.current_strategy = "bm25"
            logger.info("✅ 使用BM25内置函数作为稀疏嵌入策略")
            
        except Exception as e:
            logger.error(f"BM25内置函数初始化失败: {str(e)}")
            self.current_strategy = "none"
            logger.error("❌ 所有稀疏嵌入策略都不可用")
    
    def get_current_strategy(self) -> str:
        """获取当前使用的策略"""
        return self.current_strategy or "none"
    
    def encode_queries(self, queries: List[str]) -> List[Dict[int, float]]:
        """
        编码查询文本为稀疏嵌入
        
        Args:
            queries: 查询文本列表
            
        Returns:
            稀疏嵌入列表
        """
        if not queries:
            return []
        
        logger.debug(f"使用{self.current_strategy}策略编码 {len(queries)} 个查询")
        
        try:
            if self.current_strategy == "local" and self.local_function:
                return self.local_function.encode_queries(queries)
            elif self.current_strategy == "bm25" and self.bm25_function:
                return self._encode_with_bm25(queries, "queries")
            else:
                logger.warning("没有可用的稀疏嵌入策略，返回空嵌入")
                return [{}] * len(queries)
                
        except Exception as e:
            logger.error(f"稀疏嵌入编码失败: {str(e)}")
            # 尝试降级策略
            return self._fallback_encode(queries, "queries")
    
    def encode_documents(self, documents: List[str]) -> List[Dict[int, float]]:
        """
        编码文档文本为稀疏嵌入
        
        Args:
            documents: 文档文本列表
            
        Returns:
            稀疏嵌入列表
        """
        if not documents:
            return []
        
        logger.debug(f"使用{self.current_strategy}策略编码 {len(documents)} 个文档")
        
        try:
            if self.current_strategy == "local" and self.local_function:
                return self.local_function.encode_documents(documents)
            elif self.current_strategy == "bm25" and self.bm25_function:
                return self._encode_with_bm25(documents, "documents")
            else:
                logger.warning("没有可用的稀疏嵌入策略，返回空嵌入")
                return [{}] * len(documents)
                
        except Exception as e:
            logger.error(f"稀疏嵌入编码失败: {str(e)}")
            # 尝试降级策略
            return self._fallback_encode(documents, "documents")
    
    def _encode_with_bm25(self, texts: List[str], text_type: str) -> List[Dict[int, float]]:
        """
        使用BM25编码文本
        
        注意：BM25BuiltInFunction可能有特殊的接口要求
        """
        try:
            # BM25内置函数通常在Milvus内部处理，这里返回空字典表示使用内置处理
            logger.debug(f"使用BM25内置函数处理 {len(texts)} 个{text_type}")
            return [{}] * len(texts)
        except Exception as e:
            logger.error(f"BM25编码失败: {str(e)}")
            return [{}] * len(texts)
    
    def _fallback_encode(self, texts: List[str], text_type: str) -> List[Dict[int, float]]:
        """
        降级编码策略
        
        当当前策略失败时，尝试其他可用策略
        """
        logger.warning(f"当前策略失败，尝试降级策略处理 {len(texts)} 个{text_type}")
        
        # 最终回退到空嵌入
        logger.warning(f"所有策略都失败，返回空嵌入")
        return [{}] * len(texts)


# 缓存的稀疏嵌入函数实例
_cached_sparse_embedding_function = None


def get_local_sparse_embedding_function() -> LocalBGEM3SparseEmbeddingFunction:
    """获取本地BGE-M3稀疏嵌入函数"""
    return LocalBGEM3SparseEmbeddingFunction()


def get_smart_sparse_embedding_function(**kwargs) -> SmartSparseEmbeddingFunction:
    """获取智能稀疏嵌入函数"""
    return SmartSparseEmbeddingFunction(**kwargs)


def get_cached_sparse_embedding_function() -> SmartSparseEmbeddingFunction:
    """
    获取缓存的稀疏嵌入函数（单例模式）
    
    Returns:
        缓存的稀疏嵌入函数实例
    """
    global _cached_sparse_embedding_function
    
    if _cached_sparse_embedding_function is None:
        _cached_sparse_embedding_function = SmartSparseEmbeddingFunction()
        logger.info("创建缓存的稀疏嵌入函数实例")
    
    return _cached_sparse_embedding_function
