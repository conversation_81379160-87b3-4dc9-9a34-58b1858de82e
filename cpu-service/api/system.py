"""
系统管理API

统一的系统信息、健康检查和状态管理端点
"""

import logging
import requests
from typing import Dict, Any
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from shared.config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

# 创建路由器
system_router = APIRouter(prefix="/v2/system", tags=["系统管理"])


class SystemInfoResponse(BaseModel):
    """系统信息响应模型"""
    service_name: str  # 服务名称，标识当前服务的完整名称和版本信息
    version: str  # 服务版本号，用于版本管理和兼容性检查
    status: str  # 服务当前运行状态，如：正常运行、维护中、异常等
    architecture: str  # 系统架构描述，说明服务的技术架构模式
    description: str  # 服务功能描述，简要说明服务的主要用途和特点
    core_features: list  # 核心功能列表，详细列出服务提供的主要功能特性
    tech_stack: dict  # 技术栈信息，包含使用的框架、数据库、模型等技术组件
    quick_navigation: dict  # 快速导航链接，提供常用API端点的快速访问路径


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str  # 服务整体运行状态，如：健康、异常、离线等
    service_name: str  # 服务名称，标识当前服务的完整名称
    version: str  # 服务版本号，用于版本管理和兼容性检查
    architecture: str  # 系统架构描述，如：GPU+CPU分离架构
    component_status: Dict[str, Dict[str, str]]  # 各组件详细状态信息，包含每个组件的状态和详细信息
    dependencies: Dict[str, str]  # 依赖服务状态映射，记录所有外部依赖的健康状态
    check_time: str  # 健康检查执行时间，ISO格式时间戳


class StatusResponse(BaseModel):
    """服务状态响应模型"""
    service_name: str  # 服务名称，标识当前服务的完整名称和版本信息
    version: str  # 服务版本号，用于版本管理和兼容性检查
    status: str  # 服务当前运行状态，如：正常运行、维护中、异常等
    architecture: str  # 系统架构描述，说明服务的技术架构模式
    component_status: Dict[str, Dict[str, str]]  # 各组件详细状态信息，包含每个组件的状态和详细信息
    configuration: Dict[str, str]  # 配置信息，包含服务的关键配置参数和设置
    performance_metrics: Dict[str, str]  # 性能指标，包含CPU、内存、磁盘等系统性能数据
    runtime_info: Dict[str, Any]  # 运行时信息，包含启动时间、运行时长、请求统计等动态信息


@system_router.get("/info", response_model=SystemInfoResponse, summary="获取系统信息", description="获取CPU服务的基本信息和功能介绍")
async def get_system_info():
    """获取系统信息（合并原有的根路径和v2信息）"""
    return SystemInfoResponse(
        service_name="AI接诉即办助手 v3.0 - CPU服务",
        version="3.0.0",
        status="正常运行",
        architecture="GPU+CPU分离架构",
        description="主业务服务，处理API密集嵌入调用和业务逻辑",
        core_features=[
            "智能检索：混合向量检索（密集+稀疏）",
            "智能索引：自动文档向量化和索引构建",
            "智能回退：多层容错和离线模式支持",
            "高性能：GPU+CPU分离架构，支持并发处理"
        ],
        tech_stack={
            "框架": "FastAPI",
            "向量数据库": "Milvus",
            "嵌入模型": "BGE-M3 (API + GPU)",
            "架构模式": "微服务 + GPU加速"
        },
        quick_navigation={
            "健康检查": "/v2/system/health",
            "服务状态": "/v2/system/status",
            "检索服务": "/v2/retrieval/",
            "索引服务": "/v2/indexing/",
            "文件服务": "/v2/files/",
            "API文档": "/docs",
            "ReDoc文档": "/redoc"
        }
    )


@system_router.get("/health", response_model=HealthResponse, summary="健康检查", description="检查CPU服务及相关组件的健康状态")
async def get_system_health():
    """统一的健康检查（合并原有的health和health/detailed）"""
    from datetime import datetime
    
    # 检查GPU服务状态
    gpu_service_status = "未知"
    gpu_details = {}
    if settings.GPU_SERVICE_URL:
        try:
            gpu_health_url = f"{settings.GPU_SERVICE_URL}/health"
            response = requests.get(gpu_health_url, timeout=5)
            if response.status_code == 200:
                gpu_service_status = "健康"
                gpu_details = {"消息": "GPU服务连接正常", "响应时间": "正常"}
            else:
                gpu_service_status = "异常"
                gpu_details = {"消息": f"HTTP {response.status_code}", "响应时间": "异常"}
        except Exception as e:
            logger.warning(f"GPU服务健康检查失败: {str(e)}")
            gpu_service_status = "无法连接"
            gpu_details = {"错误": str(e)[:100]}
    
    # 检查Milvus连接状态
    milvus_status = "未知"
    milvus_details = {}
    try:
        from cores.retrieval_service import get_retrieval_service
        retrieval_service = get_retrieval_service()
        if retrieval_service.milvus_client:
            if getattr(retrieval_service.milvus_client, '_offline_mode', False):
                milvus_status = "离线模式"
                milvus_details = {"消息": "运行在离线模式，使用GPU+API向量计算", "功能": "受限"}
            else:
                milvus_status = "健康"
                milvus_details = {"消息": "Milvus连接正常", "功能": "完整"}
        else:
            milvus_status = "未初始化"
            milvus_details = {"消息": "Milvus客户端未初始化", "功能": "不可用"}
    except Exception as e:
        logger.warning(f"Milvus健康检查失败: {str(e)}")
        milvus_status = "检查失败"
        milvus_details = {"错误": str(e)[:100]}
    
    # 检查API嵌入服务
    api_status = "健康"
    api_details = {"消息": "API嵌入服务配置正常"}
    
    return HealthResponse(
        status="健康",
        service_name="AI接诉即办助手 v3.0 - CPU服务",
        version="3.0.0",
        architecture="GPU+CPU分离架构",
        component_status={
            "GPU服务": {
                "状态": gpu_service_status,
                **gpu_details
            },
            "Milvus向量数据库": {
                "状态": milvus_status,
                **milvus_details
            },
            "API嵌入服务": {
                "状态": api_status,
                **api_details
            }
        },
        dependencies={
            "GPU服务": gpu_service_status,
            "Milvus向量数据库": milvus_status,
            "API嵌入服务": api_status,
            "文件存储": "未配置"
        },
        check_time=datetime.now().isoformat()
    )


@system_router.get("/status", response_model=StatusResponse, summary="获取服务状态", description="获取CPU服务的详细状态信息")
async def get_system_status():
    """获取详细的服务状态"""
    import psutil
    import time
    from datetime import datetime
    
    # 获取组件状态（复用健康检查逻辑）
    health_response = await get_system_health()
    
    # 获取系统性能指标
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        performance_metrics = {
            "CPU使用率": f"{cpu_percent:.1f}%",
            "内存使用率": f"{memory.percent:.1f}%",
            "磁盘使用率": f"{disk.percent:.1f}%",
            "可用内存": f"{memory.available / (1024**3):.1f}GB"
        }
    except Exception as e:
        logger.warning(f"获取系统指标失败: {str(e)}")
        performance_metrics = {
            "CPU使用率": "无法获取",
            "内存使用率": "无法获取",
            "磁盘使用率": "无法获取",
            "可用内存": "无法获取"
        }
    
    # 运行时信息
    runtime_info = {
        "启动时间": "未知",  # 可以在应用启动时记录
        "运行时长": "未知",
        "处理请求数": "未统计",
        "平均响应时间": "未统计",
        "当前时间": datetime.now().isoformat()
    }
    
    return StatusResponse(
        service_name="AI接诉即办助手 v3.0 - CPU服务",
        version="3.0.0",
        status="正常运行",
        architecture="GPU+CPU分离架构",
        component_status=health_response.component_status,
        configuration={
            "API嵌入模型": settings.API_EMBED_MODEL,
            "GPU服务地址": settings.GPU_SERVICE_URL,
            "Milvus地址": settings.MILVUS_URI,
            "运行环境": settings.ENVIRONMENT,
            "向量维度": str(settings.MILVUS_DIMENSION)
        },
        performance_metrics=performance_metrics,
        runtime_info=runtime_info
    )


