"""
Kubernetes健康检查API

提供CPU服务的就绪检查和存活检查接口
主要健康检查功能已迁移到 /v2/system/health
"""

import logging
from fastapi import APIRouter, HTTPException
import requests

from shared.config.settings import get_settings

logger = logging.getLogger(__name__)

health_router = APIRouter()


# 健康检查端点已迁移到 /v2/system/health
# 保留 /ready 和 /live 端点用于 Kubernetes 健康检查


@health_router.get("/ready")
async def readiness_check():
    """就绪检查"""
    
    # 检查关键依赖是否就绪
    settings = _get_settings()
    ready = True
    checks = {}

    # 检查GPU服务
    if settings.GPU_SERVICE_URL:
        try:
            gpu_health_url = f"{settings.GPU_SERVICE_URL}/health"
            response = requests.get(gpu_health_url, timeout=5)
            checks["gpu_service"] = response.status_code == 200
        except Exception:
            checks["gpu_service"] = False
            ready = False
    else:
        checks["gpu_service"] = True  # 如果没有配置GPU服务，认为就绪
    
    # 这里可以添加其他就绪检查
    # 例如：数据库连接、缓存连接等
    
    if not ready:
        raise HTTPException(status_code=503, detail="Service not ready")
    
    return {
        "status": "ready",
        "checks": checks
    }


@health_router.get("/live")
async def liveness_check():
    """存活检查"""
    return {
        "status": "alive",
        "service": "cpu-service",
        "version": "3.0.0"
    }
