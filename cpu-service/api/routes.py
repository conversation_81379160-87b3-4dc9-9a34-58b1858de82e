"""
主要API路由

包含所有业务API接口
"""

import logging
from fastapi import APIRouter

from api.retrieval import retrieval_router
from api.indexing import indexing_router
from api.files import files_router
from api.audit import audit_router

logger = logging.getLogger(__name__)

# 创建主路由器
api_router = APIRouter()

# 包含子路由
api_router.include_router(retrieval_router, prefix="/retrieval", tags=["检索"])
api_router.include_router(indexing_router, prefix="/indexing", tags=["索引"])
api_router.include_router(files_router, prefix="/files", tags=["文件"])
api_router.include_router(audit_router, prefix="/audit", tags=["文件"])

