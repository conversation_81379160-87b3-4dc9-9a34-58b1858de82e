"""
索引API

提供索引管理功能
"""

import logging
import os
from typing import Dict, Any, List, Optional

import pandas as pd
from fastapi import APIRouter, HTTPException, BackgroundTasks
from llama_index.core import Document
from pydantic import BaseModel, Field

from shared.config.settings import get_settings
from cores.indexing_service import get_indexing_service

logger = logging.getLogger(__name__)

indexing_router = APIRouter()


class IndexBuildRequest(BaseModel):
    """索引构建请求模型"""
    collection_name: Optional[str] = Field(
        None,
        description="集合名称，如果为空则构建所有集合",
        example="department"
    )
    force_rebuild: bool = Field(
        False,
        description="是否强制重建：False=追加到现有集合（不重置数据），True=重置集合并重建（删除现有数据）",
        example=False
    )
    enable_sparse: Optional[bool] = Field(
        None,
        description="是否启用稀疏嵌入（GPU服务），None=使用默认配置",
        example=True
    )

    class Config:
        json_schema_extra = {
            "example": {
                "collection_name": "department",
                "force_rebuild": False,
                "enable_sparse": True
            }
        }


class IndexBuildResponse(BaseModel):
    """索引构建响应模型"""
    status: str
    message: str
    collection_name: str
    task_id: Optional[str] = None
    processing_time: Optional[float] = None


class IndexStatusResponse(BaseModel):
    """索引状态响应模型"""
    collection_name: str
    status: str
    document_count: int
    last_updated: Optional[str] = None
    metadata: Dict[str, Any]


async def build_department_csv():
    """
    CSV文档构建函数

    基于性能分析，CSV处理仅占索引构建总时间的0.4%，
    保持简单可靠的原始方法，专注于优化真正的瓶颈（GPU稀疏嵌入）
    """
    csv_path = os.path.join(os.path.dirname(__file__), "..", "data", "historical", "历史工单.csv")

    import logging

    logger = logging.getLogger(__name__)

    # 简单的编码检测和处理
    encoding = 'gbk'  # 项目默认编码

    df = pd.read_csv(csv_path, encoding=encoding)
    df = df.fillna("")  # 将NaN替换为空字符串
    docs = []

    for idx, row in df.iterrows():
        content = f"""类别: {row.get("类别", "")}
        事项: {row.get("事项", "")}
        具体事项: {row.get("具体事项", "")}
        具体内容: {row.get("具体内容", "")}
        责任单位: {row.get("责任单位", "")}
        备注: {row.get("备注", "")}"""

        # 创建元数据
        metadata = {
            "department": row.get("责任单位", ""),
            "collection_type": "guideline"
        }

        # 创建文档对象
        doc = Document(text=content, metadata=metadata)
        docs.append(doc)

    logger.info(f"CSV处理完成，文档数量: {len(docs)}")
    return docs



@indexing_router.post("/build", response_model=IndexBuildResponse)
async def build_index(request: IndexBuildRequest, background_tasks: BackgroundTasks):
    """构建索引"""

    try:
        # 获取索引服务
        indexing_service = get_indexing_service()
        
        if request.collection_name:
            # 构建单个集合的索引
            if request.force_rebuild:
                # 同步重建（强制重置集合）
                result = indexing_service.rebuild_index_sync(
                    collection_name=request.collection_name,
                    enable_sparse=request.enable_sparse
                )

                return IndexBuildResponse(
                    status="completed",
                    message=f"索引 {request.collection_name} 重建完成（集合已重置）",
                    collection_name=request.collection_name,
                    processing_time=result.get("processing_time")
                )
            else:
                documents = await build_department_csv()
                # 异步追加构建（不重置集合）
                task_id = await indexing_service.build_index_async(
                    collection_name=request.collection_name,
                    enable_sparse=request.enable_sparse,
                    force_rebuild=False,  # 明确指定为追加模式
                    documents=documents
                )

                return IndexBuildResponse(
                    status="started",
                    message=f"索引 {request.collection_name} 追加构建已启动（不会重置现有数据）",
                    collection_name=request.collection_name,
                    task_id=task_id
                )
        else:
            # 构建所有索引
            if request.force_rebuild:
                # 同步重建所有索引
                background_tasks.add_task(
                    indexing_service.rebuild_all_indexes_sync
                )
                
                return IndexBuildResponse(
                    status="started",
                    message="所有索引重建已启动（后台任务）",
                    collection_name="all"
                )
            else:
                # 异步构建所有索引
                task_ids = await indexing_service.build_all_indexes_async()
                
                return IndexBuildResponse(
                    status="started",
                    message="所有索引构建已启动",
                    collection_name="all",
                    task_id=",".join(task_ids)
                )
        
    except Exception as e:
        logger.error(f"索引构建失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"索引构建失败: {str(e)}")


@indexing_router.get("/status/{collection_name}", response_model=IndexStatusResponse)
async def get_index_status(collection_name: str):
    """获取索引状态"""
    
    try:
        # 获取索引服务
        indexing_service = get_indexing_service()
        
        # 获取索引状态
        status = indexing_service.get_index_status(collection_name)
        
        return IndexStatusResponse(
            collection_name=collection_name,
            status=status["status"],
            document_count=status["document_count"],
            last_updated=status.get("last_updated"),
            metadata={
                "architecture": "GPU+CPU分离架构",
                "sparse_embedding": "GPU服务",
                "dense_embedding": "API调用",
                **status.get("metadata", {})
            }
        )
        
    except Exception as e:
        logger.error(f"获取索引状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取索引状态失败: {str(e)}")


@indexing_router.get("/status", response_model=List[IndexStatusResponse])
async def get_all_index_status():
    """获取所有索引状态"""
    
    try:
        # 获取索引服务
        indexing_service = get_indexing_service()
        
        # 获取所有索引状态
        all_status = indexing_service.get_all_index_status()
        
        return [
            IndexStatusResponse(
                collection_name=name,
                status=status["status"],
                document_count=status["document_count"],
                last_updated=status.get("last_updated"),
                metadata={
                    "architecture": "GPU+CPU分离架构",
                    "sparse_embedding": "GPU服务",
                    "dense_embedding": "API调用",
                    **status.get("metadata", {})
                }
            )
            for name, status in all_status.items()
        ]
        
    except Exception as e:
        logger.error(f"获取所有索引状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取所有索引状态失败: {str(e)}")


@indexing_router.delete("/delete/{collection_name}")
async def delete_index(collection_name: str):
    """删除索引"""
    
    try:
        # 获取索引服务
        indexing_service = get_indexing_service()
        
        # 删除索引
        result = indexing_service.delete_index(collection_name)
        
        return {
            "status": "success",
            "message": f"索引 {collection_name} 删除成功",
            "collection_name": collection_name,
            "result": result
        }
        
    except Exception as e:
        logger.error(f"删除索引失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除索引失败: {str(e)}")


@indexing_router.get("/config")
async def get_indexing_config():
    """获取索引配置"""
    
    try:
        # 获取索引服务
        indexing_service = get_indexing_service()
        
        # 获取配置信息
        config = indexing_service.get_indexing_config()
        
        return {
            "indexing_config": config,
            "architecture": "GPU+CPU分离架构",
            "features": [
                "混合索引（密集+稀疏）",
                "API密集嵌入",
                "GPU稀疏嵌入",
                "智能回退机制",
                "异步索引构建"
            ]
        }
        
    except Exception as e:
        logger.error(f"获取索引配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取索引配置失败: {str(e)}")


@indexing_router.post("/test")
async def test_indexing():
    """测试索引功能"""
    
    try:
        # 获取索引服务
        indexing_service = get_indexing_service()
        
        # 执行测试
        test_result = indexing_service.test_indexing()
        
        return {
            "status": "success",
            "test_result": test_result,
            "architecture": "GPU+CPU分离架构",
            "message": "索引功能测试通过"
        }
        
    except Exception as e:
        logger.error(f"索引功能测试失败: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "message": "索引功能测试失败"
        }
