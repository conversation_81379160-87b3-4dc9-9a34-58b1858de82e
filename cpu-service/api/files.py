"""
文件API

提供文件上传和管理功能
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from pydantic import BaseModel

from shared.config.settings import get_settings

logger = logging.getLogger(__name__)

files_router = APIRouter()


class FileUploadResponse(BaseModel):
    """文件上传响应模型"""
    status: str
    message: str
    file_id: str
    filename: str
    file_size: int
    collection_type: str
    processing_time: float


class FileListResponse(BaseModel):
    """文件列表响应模型"""
    files: List[Dict[str, Any]]
    total_count: int
    collection_type: Optional[str] = None


@files_router.post("/upload", response_model=FileUploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    collection_type: str = Form(default="attachments"),
    description: Optional[str] = Form(default=None)
):
    """上传文件"""
    
    if not file.filename:
        raise HTTPException(status_code=400, detail="文件名不能为空")
    
    # 检查文件类型
    allowed_extensions = {'.txt', '.pdf', '.doc', '.docx', '.csv', '.xlsx'}
    file_extension = '.' + file.filename.split('.')[-1].lower()
    
    if file_extension not in allowed_extensions:
        raise HTTPException(
            status_code=400, 
            detail=f"不支持的文件类型: {file_extension}。支持的类型: {', '.join(allowed_extensions)}"
        )
    
    try:
        # 读取文件内容
        content = await file.read()
        file_size = len(content)
        
        # 这里应该调用文件处理服务
        # 由于我们在创建基础架构，先返回模拟响应
        
        # 生成文件ID
        import uuid
        file_id = str(uuid.uuid4())
        
        # 模拟处理时间
        import time
        start_time = time.time()
        
        # 这里应该包含实际的文件处理逻辑：
        # 1. 保存文件到MinIO
        # 2. 解析文件内容
        # 3. 创建向量索引
        # 4. 保存到数据库
        
        processing_time = time.time() - start_time
        
        logger.info(f"文件上传成功: {file.filename}, 大小: {file_size}, 集合: {collection_type}")
        
        return FileUploadResponse(
            status="success",
            message="文件上传成功",
            file_id=file_id,
            filename=file.filename,
            file_size=file_size,
            collection_type=collection_type,
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")


@files_router.get("/list", response_model=FileListResponse)
async def list_files(
    collection_type: Optional[str] = None,
    limit: int = 50,
    offset: int = 0
):
    """获取文件列表"""
    
    try:
        # 这里应该从数据库查询文件列表
        # 目前返回模拟数据
        
        mock_files = [
            {
                "file_id": "file-001",
                "filename": "部门职责.txt",
                "file_size": 1024,
                "collection_type": "department",
                "upload_time": "2024-01-01T10:00:00Z",
                "status": "processed"
            },
            {
                "file_id": "file-002", 
                "filename": "派单导则.pdf",
                "file_size": 2048,
                "collection_type": "guideline",
                "upload_time": "2024-01-01T11:00:00Z",
                "status": "processed"
            }
        ]
        
        # 根据collection_type过滤
        if collection_type:
            mock_files = [f for f in mock_files if f["collection_type"] == collection_type]
        
        # 分页
        total_count = len(mock_files)
        files = mock_files[offset:offset + limit]
        
        return FileListResponse(
            files=files,
            total_count=total_count,
            collection_type=collection_type
        )
        
    except Exception as e:
        logger.error(f"获取文件列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取文件列表失败: {str(e)}")


@files_router.get("/{file_id}")
async def get_file_info(file_id: str):
    """获取文件信息"""
    
    try:
        # 这里应该从数据库查询文件信息
        # 目前返回模拟数据
        
        file_info = {
            "file_id": file_id,
            "filename": "示例文件.txt",
            "file_size": 1024,
            "collection_type": "department",
            "upload_time": "2024-01-01T10:00:00Z",
            "status": "processed",
            "metadata": {
                "architecture": "GPU+CPU分离架构",
                "processing_method": "混合向量化",
                "dense_embedding": "API调用",
                "sparse_embedding": "GPU服务"
            }
        }
        
        return file_info
        
    except Exception as e:
        logger.error(f"获取文件信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取文件信息失败: {str(e)}")


@files_router.delete("/{file_id}")
async def delete_file(file_id: str):
    """删除文件"""
    
    try:
        # 这里应该包含实际的文件删除逻辑：
        # 1. 从MinIO删除文件
        # 2. 从向量数据库删除索引
        # 3. 从数据库删除记录
        
        logger.info(f"文件删除成功: {file_id}")
        
        return {
            "status": "success",
            "message": "文件删除成功",
            "file_id": file_id
        }
        
    except Exception as e:
        logger.error(f"文件删除失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件删除失败: {str(e)}")


@files_router.get("/collections/types")
async def get_collection_types():
    """获取支持的集合类型"""
    
    return {
        "collection_types": [
            {
                "name": "department",
                "display_name": "部门职责",
                "description": "部门职责相关文档"
            },
            {
                "name": "guideline", 
                "display_name": "派单导则",
                "description": "派单导则相关文档"
            },
            {
                "name": "historical",
                "display_name": "历史工单",
                "description": "历史工单数据"
            },
            {
                "name": "delegated",
                "display_name": "下放职责", 
                "description": "下放职责相关文档"
            },
            {
                "name": "attachments",
                "display_name": "附件",
                "description": "通用附件文档"
            }
        ],
        "architecture": "GPU+CPU分离架构"
    }
