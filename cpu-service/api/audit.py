"""
审核相关路由
包括工单退回审核、相似处办查询和处置方案推荐功能
"""
import asyncio
import logging
import time
import uuid
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field

from shared.config.settings import get_settings


# 设置日志记录器
logger = logging.getLogger(__name__)

# 创建路由器
audit_router = APIRouter()


class AuditRequest(BaseModel):
    """退单审核模型"""
    content: str = Field(None, description="集合名称，如果为空则构建所有集合",
        example="department"
    )
    department_name: str = Field(
        False,
        description="是否强制重建：False=追加到现有集合（不重置数据），True=重置集合并重建（删除现有数据）",
        example=False
    )
    region: Optional[str] = Field(
        None,
        description="是否启用稀疏嵌入（GPU服务），None=使用默认配置",
        example=True
    )


class AuditResponse(BaseModel):
    """索引构建响应模型"""
    status: str
    message: str
    data: str
    success: str

@audit_router.post(
    "/workorder",
    response_model=AuditResponse,
    summary="审核工单退回是否合理"
)
async def audit_workorder(request: AuditRequest, background_tasks: BackgroundTasks):
    # 生成请求ID（内部使用，不暴露给客户端）
    request_id = cancel_tracker.generate_request_id()

    # 创建性能统计对象
    stats = PerformanceStats(f"API-工单退回审核({request.department_name})")

    # 记录基本信息
    logger.info(f"开始处理工单退回审核请求: 部门={request.department_name}, 区域={request.region or '未指定'}, 请求ID={request_id}")
    performance_logger.info(f"工单退回审核请求: 部门={request.department_name}, 内容长度={len(request.content)}, 请求ID={request_id}")

    try:
        # 创建服务实例并处理请求
        with stats.measure("处理退回工单"):
            service = AuditService(
                query=request.content,
                department_name=request.department_name,
                region=request.region
            )

            # 传递请求ID和取消检查函数，但不改变返回值
            # 创建一个异步包装函数，因为service.handle_returned_work_order期望一个异步函数
            async def async_is_cancelled(req_id):
                return cancel_tracker.is_cancelled(req_id)

            result = await service.handle_returned_work_order(
                request_id=request_id,
                check_cancelled=async_is_cancelled
            )

        # 计算总耗时但不记录汇总日志
        total_time = stats.log_summary()

        # 记录API层总耗时
        logger.info(f"工单退回审核完成: {request.department_name}, 耗时: {total_time:.2f}秒")

        # 清理请求
        background_tasks.add_task(cancel_tracker.cleanup, request_id)

        return success_response(
            data=result,
            message="工单退回审核完成"
        )

    except asyncio.CancelledError:
        # 请求被取消
        logger.info(f"工单退回审核请求被取消: {request.department_name}, 请求ID={request_id}")

        # 清理请求
        background_tasks.add_task(cancel_tracker.cleanup, request_id)

        # 返回错误响应
        return error_response(
            message="工单退回审核请求已取消"
        )
    except Exception as e:
        # 处理其他异常
        logger.error(f"工单退回审核请求失败: {request.department_name}, 请求ID={request_id}, 错误: {str(e)}")

        # 清理请求
        background_tasks.add_task(cancel_tracker.cleanup, request_id)

        # 返回错误响应
        return error_response(
            message=f"工单退回审核失败: {str(e)}"
        )

# @router.post(
#     "/similar_cases",
#     response_model=ApiResponse,
#     summary="查询相似处办案例"
# )
# async def get_similar_cases(request: WorkOrderBase, background_tasks: BackgroundTasks):
#     # 生成请求ID（内部使用，不暴露给客户端）
#     request_id = cancel_tracker.generate_request_id()
#
#     # 创建性能统计对象
#     stats = PerformanceStats(f"API-相似处办查询({request.department_name})")
#
#     # 记录基本信息
#     logger.info(f"开始处理相似处办查询请求: {request.department_name}, 内容长度={len(request.content)}, 请求ID={request_id}")
#     performance_logger.info(f"相似处办查询请求: 部门={request.department_name}, 区域={request.region or '未指定'}, 请求ID={request_id}")
#
#     try:
#         # 创建服务实例并处理请求
#         with stats.measure("查找相似工单"):
#             service = AuditService(
#                 query=request.content,
#                 department_name=request.department_name,
#                 region=request.region
#             )
#             # 传递请求ID和取消检查函数
#             # 创建一个异步包装函数
#             async def async_is_cancelled(req_id):
#                 return cancel_tracker.is_cancelled(req_id)
#
#             result = await service.find_similar_work_orders(
#                 request_id=request_id,
#                 check_cancelled=async_is_cancelled
#             )
#
#         # 计算总耗时但不记录汇总日志
#         total_time = stats.log_summary()
#
#         # 记录API层总耗时
#         logger.info(f"相似工单查询完成: 部门={request.department_name}, 耗时={total_time:.2f}秒, 请求ID={request_id}")
#
#         # 清理请求
#         background_tasks.add_task(cancel_tracker.cleanup, request_id)
#
#         return success_response(
#             data=result,
#             message="相似处办查询完成"
#         )
#
#     except asyncio.CancelledError:
#         # 请求被取消
#         logger.info(f"相似处办查询请求被取消: {request.department_name}, 请求ID={request_id}")
#
#         # 清理请求
#         background_tasks.add_task(cancel_tracker.cleanup, request_id)
#
#         # 返回错误响应
#         return error_response(
#             message="相似处办查询请求已取消"
#         )
#     except Exception as e:
#         # 处理其他异常
#         logger.error(f"相似处办查询请求失败: {request.department_name}, 请求ID={request_id}, 错误: {str(e)}")
#
#         # 清理请求
#         background_tasks.add_task(cancel_tracker.cleanup, request_id)
#
#         # 返回错误响应
#         return error_response(
#             message=f"相似处办查询失败: {str(e)}"
#         )
#
# @router.post(
#     "/recommend_solution",
#     response_model=ApiResponse,
#     summary="推荐工单处置方案"
# )
# async def recommend_solution(request: WorkOrderBase, background_tasks: BackgroundTasks):
#     # 生成请求ID（内部使用，不暴露给客户端）
#     request_id = cancel_tracker.generate_request_id()
#
#     # 创建性能统计对象
#     stats = PerformanceStats(f"API-处置方案推荐({request.department_name})")
#
#     # 记录基本信息
#     logger.info(f"开始处理工单处置方案推荐请求: {request.department_name}, 内容长度={len(request.content)}, 请求ID={request_id}")
#     performance_logger.info(f"处置方案推荐请求: 部门={request.department_name}, 区域={request.region or '未指定'}, 请求ID={request_id}")
#
#     try:
#         # 创建服务实例并处理请求
#         with stats.measure("推荐处置方案"):
#             service = AuditService(
#                 query=request.content,
#                 department_name=request.department_name,
#                 region=request.region
#             )
#             # 传递请求ID和取消检查函数
#             # 创建一个异步包装函数
#             async def async_is_cancelled(req_id):
#                 return cancel_tracker.is_cancelled(req_id)
#
#             result = await service.recommend_resolution_plan(
#                 request_id=request_id,
#                 check_cancelled=async_is_cancelled
#             )
#
#         # 计算总耗时但不记录汇总日志
#         total_time = stats.log_summary()
#
#         # 记录API层总耗时
#         logger.info(f"工单处置方案推荐完成: {request.department_name}, 耗时: {total_time:.2f}秒, 请求ID={request_id}")
#
#         # 清理请求
#         background_tasks.add_task(cancel_tracker.cleanup, request_id)
#
#         return success_response(
#             data=result,
#             message="处置方案推荐完成"
#         )
#
#
#     except asyncio.CancelledError:
#         # 请求被取消
#         logger.info(f"工单处置方案推荐请求被取消: {request.department_name}, 请求ID={request_id}")
#
#         # 清理请求
#         background_tasks.add_task(cancel_tracker.cleanup, request_id)
#
#         # 返回错误响应
#         return error_response(
#             message="工单处置方案推荐请求已取消"
#         )
#     except Exception as e:
#         # 处理其他异常
#         logger.error(f"工单处置方案推荐请求失败: {request.department_name}, 请求ID={request_id}, 错误: {str(e)}")
#
#         # 清理请求
#         background_tasks.add_task(cancel_tracker.cleanup, request_id)
#
#         # 返回错误响应
#         return error_response(
#             message=f"工单处置方案推荐失败: {str(e)}"
#         )
