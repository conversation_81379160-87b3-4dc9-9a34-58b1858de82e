2025-06-02 21:10:29 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-02 21:10:29 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-02.log
2025-06-02 21:10:29 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-02.log
2025-06-02 21:10:29 [INFO] __main__ [main.py:140] - 启动CPU服务: localhost:8009
2025-06-02 21:10:32 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-02 21:10:32 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-02.log
2025-06-02 21:10:32 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-02.log
2025-06-02 21:10:32 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-02 21:10:32 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-02.log
2025-06-02 21:10:32 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-02.log
2025-06-02 21:10:32 [INFO] uvicorn.error [server.py:76] - Started server process [45352]
2025-06-02 21:10:32 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-02 21:10:32 [INFO] main [main.py:80] - 初始化CPU服务...
2025-06-02 21:10:32 [INFO] main [main.py:85] - CPU服务初始化完成
2025-06-02 21:10:32 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-02 21:10:34 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:59652 - "GET /health HTTP/1.1" 200
2025-06-02 21:10:36 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:59654 - "GET / HTTP/1.1" 200
2025-06-02 21:10:38 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:59656 - "GET /health HTTP/1.1" 200
2025-06-02 21:10:41 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:59658 - "GET /v2/ HTTP/1.1" 200
2025-06-02 21:10:43 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:65] - 使用默认GPU服务URL: http://gpu-service:8001
2025-06-02 21:10:43 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-02 21:10:45 [WARNING] shared.clients.gpu_service_client [gpu_service_client.py:84] - GPU服务健康检查异常: HTTPConnectionPool(host='gpu-service', port=8001): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000014C387A6BD0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-06-02 21:10:45 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:59661 - "GET /v2/status HTTP/1.1" 200
2025-06-02 21:10:47 [INFO] cores.indexing_service [indexing_service.py:55] - 优化版索引服务初始化完成（GPU+CPU分离架构）
2025-06-02 21:10:47 [INFO] cores.indexing_service [indexing_service.py:407] - 创建索引服务实例
2025-06-02 21:10:47 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:59667 - "GET /v2/indexing/config HTTP/1.1" 200
2025-06-02 21:10:49 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:59670 - "GET /v2/indexing/status HTTP/1.1" 200
2025-06-02 21:10:51 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-02 21:10:51 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:65] - 使用默认GPU服务URL: http://gpu-service:8001
2025-06-02 21:10:51 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-02 21:10:54 [WARNING] shared.clients.gpu_service_client [gpu_service_client.py:84] - GPU服务健康检查异常: HTTPConnectionPool(host='gpu-service', port=8001): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000014C390B0190>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-06-02 21:10:54 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-02 21:10:54 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-02 21:10:54 [INFO] cores.indexing_service [indexing_service.py:72] - 为 test_collection 启用生产环境稀疏嵌入函数（GPU服务优先）
2025-06-02 21:11:04 [ERROR] cores.indexing_service [indexing_service.py:114] - 混合索引 test_collection 创建失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-02 21:11:04 [ERROR] cores.indexing_service [indexing_service.py:167] - 索引构建失败: test_collection, 错误: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>, 耗时: 12.916秒
2025-06-02 21:11:04 [ERROR] cores.indexing_service [indexing_service.py:388] - 索引功能测试失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-02 21:11:04 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:59672 - "POST /v2/indexing/test HTTP/1.1" 200
2025-06-02 21:11:06 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-02 21:11:06 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:65] - 使用默认GPU服务URL: http://gpu-service:8001
2025-06-02 21:11:06 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-02 21:12:01 [WARNING] shared.clients.gpu_service_client [gpu_service_client.py:84] - GPU服务健康检查异常: HTTPConnectionPool(host='gpu-service', port=8001): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000014C39081D90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-06-02 21:12:01 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-02 21:12:01 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
