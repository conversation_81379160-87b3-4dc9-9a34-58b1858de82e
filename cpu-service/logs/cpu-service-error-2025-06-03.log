2025-06-03 07:46:41 [ERROR] cores.indexing_service [indexing_service.py:114] - 混合索引 test_collection 创建失败: <MilvusException: (code=2, message=Fail connecting to server on 172.24.255.5:19530, illegal connection params or server unavailable)>
2025-06-03 07:46:41 [ERROR] cores.indexing_service [indexing_service.py:167] - 索引构建失败: test_collection, 错误: <MilvusException: (code=2, message=Fail connecting to server on 172.24.255.5:19530, illegal connection params or server unavailable)>, 耗时: 12.863秒
2025-06-03 07:46:41 [ERROR] cores.indexing_service [indexing_service.py:388] - 索引功能测试失败: <MilvusException: (code=2, message=Fail connecting to server on 172.24.255.5:19530, illegal connection params or server unavailable)>
2025-06-03 09:49:05 [ERROR] uvicorn.error [on.py:134] - Traceback (most recent call last):
  File "D:\ProgramFiles\python311\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ProgramFiles\python311\Lib\asyncio\base_events.py", line 654, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\ProgramFiles\python311\Lib\asyncio\runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "D:\ProgramFiles\python311\Lib\asyncio\runners.py", line 123, in run
    raise KeyboardInterrupt()
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Project\Backend\ai\ai-jiesujiban-v3\.venv\Lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "D:\Project\Backend\ai\ai-jiesujiban-v3\.venv\Lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ProgramFiles\python311\Lib\asyncio\queues.py", line 158, in get
    await getter
asyncio.exceptions.CancelledError

2025-06-03 15:00:32 [ERROR] uvicorn.error [on.py:134] - Traceback (most recent call last):
  File "D:\ProgramFiles\python311\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ProgramFiles\python311\Lib\asyncio\base_events.py", line 654, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\ProgramFiles\python311\Lib\asyncio\runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "D:\ProgramFiles\python311\Lib\asyncio\runners.py", line 123, in run
    raise KeyboardInterrupt()
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Project\Backend\ai\ai-jiesujiban-v3\.venv\Lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "D:\Project\Backend\ai\ai-jiesujiban-v3\.venv\Lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ProgramFiles\python311\Lib\asyncio\queues.py", line 158, in get
    await getter
asyncio.exceptions.CancelledError

