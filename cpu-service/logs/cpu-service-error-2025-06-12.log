2025-06-12 15:03:45 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on 172.24.255.5:19530, illegal connection params or server unavailable)>
2025-06-12 15:14:51 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on 172.24.255.5:19530, illegal connection params or server unavailable)>
2025-06-12 16:35:17 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on 172.24.255.5:19530, illegal connection params or server unavailable)>
2025-06-12 17:34:28 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on 172.24.255.5:19530, illegal connection params or server unavailable)>
2025-06-12 18:45:45 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on 172.24.255.5:19530, illegal connection params or server unavailable)>
2025-06-12 18:49:10 [ERROR] shared.clients.embedding_client [embedding_client.py:168] - 密集嵌入获取异常: name 'settings' is not defined
2025-06-12 18:49:10 [ERROR] shared.clients.embedding_client [embedding_client.py:109] - 批量嵌入获取失败: name 'settings' is not defined
2025-06-12 18:50:30 [ERROR] shared.clients.embedding_client [embedding_client.py:168] - 密集嵌入获取异常: name 'settings' is not defined
2025-06-12 18:50:30 [ERROR] shared.clients.embedding_client [embedding_client.py:109] - 批量嵌入获取失败: name 'settings' is not defined
2025-06-12 18:52:46 [ERROR] uvicorn.error [server.py:168] - [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8009): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-12 18:54:56 [ERROR] shared.clients.embedding_client [embedding_client.py:167] - 密集嵌入API请求失败: 413, {"code":20042,"message":"input batch size 2048 \u003e maximum allowed batch size 64","data":null}
2025-06-12 18:56:18 [ERROR] shared.clients.embedding_client [embedding_client.py:167] - 密集嵌入API请求失败: 413, {"code":20042,"message":"input batch size 531 \u003e maximum allowed batch size 64","data":null}
2025-06-12 18:59:58 [ERROR] cores.indexing_service [indexing_service.py:236] - 索引追加构建失败: guideline, 错误: <DataNotMatchException: (code=1, message=Attempt to insert an unexpected field `source` to collection without enabling dynamic field)>, 耗时: 0.308秒
2025-06-12 18:59:58 [ERROR] cores.indexing_service [indexing_service.py:317] - 异步索引追加构建任务失败: d054ff88-ad7f-4252-923b-e9d1932529d4, 错误: <DataNotMatchException: (code=1, message=Attempt to insert an unexpected field `source` to collection without enabling dynamic field)>
2025-06-12 18:59:59 [ERROR] cores.indexing_service [indexing_service.py:236] - 索引追加构建失败: historical, 错误: <DataNotMatchException: (code=1, message=Attempt to insert an unexpected field `source` to collection without enabling dynamic field)>, 耗时: 0.424秒
2025-06-12 18:59:59 [ERROR] cores.indexing_service [indexing_service.py:317] - 异步索引追加构建任务失败: 312159e8-8dff-4e4d-bfbf-75494379f735, 错误: <DataNotMatchException: (code=1, message=Attempt to insert an unexpected field `source` to collection without enabling dynamic field)>
2025-06-12 18:59:59 [ERROR] cores.indexing_service [indexing_service.py:236] - 索引追加构建失败: delegated, 错误: <DataNotMatchException: (code=1, message=Attempt to insert an unexpected field `source` to collection without enabling dynamic field)>, 耗时: 0.261秒
2025-06-12 18:59:59 [ERROR] cores.indexing_service [indexing_service.py:317] - 异步索引追加构建任务失败: e8b72aa0-66c6-4bcd-9928-72aa11d2e14e, 错误: <DataNotMatchException: (code=1, message=Attempt to insert an unexpected field `source` to collection without enabling dynamic field)>
