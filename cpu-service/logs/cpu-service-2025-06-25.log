2025-06-25 09:15:35 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 09:15:35 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 09:15:35 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 09:15:35 [INFO] __main__ [main.py:184] - 启动CPU服务: 0.0.0.0:8009
2025-06-25 09:15:35 [INFO] __main__ [main.py:190] - 启动模式: development
2025-06-25 09:15:35 [INFO] __main__ [main.py:191] - 工作进程数: 1
2025-06-25 09:15:35 [INFO] __main__ [main.py:192] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-25 09:15:35 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 09:15:35 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 09:15:35 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 09:15:35 [INFO] uvicorn.error [server.py:83] - Started server process [16928]
2025-06-25 09:15:35 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-25 09:15:35 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-25 09:15:35 [INFO] main [main.py:83] - 环境: development
2025-06-25 09:15:35 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8009
2025-06-25 09:15:35 [INFO] main [main.py:85] - GPU服务地址: None
2025-06-25 09:15:35 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-25 09:15:35 [INFO] cores.retrieval_service [retrieval_service.py:69] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-25 09:15:35 [INFO] cores.retrieval_service [retrieval_service.py:628] - 创建检索服务实例
2025-06-25 09:15:35 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-25 09:15:35 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-25 09:15:35 [INFO] cores.retrieval_service [retrieval_service.py:76] - 正在初始化Milvus客户端...
2025-06-25 09:15:35 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-25 09:15:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:313] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-25 09:15:35 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-25 09:15:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:74] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-25 09:15:35 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-25 09:15:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:58] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-25 09:15:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:59] - 批处理配置: batch_size=32
2025-06-25 09:15:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:92] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '4%', 'memory_utilization': '30%', 'temperature': '41°C', 'power_usage': '6.8W'}}
2025-06-25 09:15:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:233] - GPU服务稀疏嵌入客户端初始化成功
2025-06-25 09:15:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:237] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-25 09:15:36 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:63548 - "GET /docs HTTP/1.1" 200
2025-06-25 09:15:38 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:63548 - "GET /openapi.json HTTP/1.1" 200
2025-06-25 09:15:46 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-25 09:15:46 [WARNING] shared.clients.milvus_client [milvus_client.py:229] - 尝试连接本地Milvus作为回退...
2025-06-25 09:15:56 [WARNING] shared.clients.milvus_client [milvus_client.py:241] - 本地Milvus连接也失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-25 09:15:56 [WARNING] shared.clients.milvus_client [milvus_client.py:244] - Milvus连接失败，将在离线模式下运行
2025-06-25 09:15:56 [INFO] shared.clients.milvus_client [milvus_client.py:193] - Milvus客户端初始化完成: http://************:19530
2025-06-25 09:15:56 [INFO] shared.clients.milvus_client [milvus_client.py:728] - 创建Milvus客户端实例
2025-06-25 09:15:56 [INFO] cores.retrieval_service [retrieval_service.py:78] - Milvus客户端初始化成功
2025-06-25 09:15:56 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-25 09:21:42 [INFO] uvicorn.error [server.py:263] - Shutting down
2025-06-25 09:21:42 [INFO] uvicorn.error [on.py:67] - Waiting for application shutdown.
2025-06-25 09:21:42 [INFO] main [main.py:121] - CPU服务关闭
2025-06-25 09:21:42 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-25 09:21:42 [INFO] uvicorn.error [server.py:93] - Finished server process [16928]
2025-06-25 09:21:52 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 09:21:52 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 09:21:52 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 09:21:52 [INFO] __main__ [main.py:184] - 启动CPU服务: 0.0.0.0:8009
2025-06-25 09:21:52 [INFO] __main__ [main.py:190] - 启动模式: development
2025-06-25 09:21:52 [INFO] __main__ [main.py:191] - 工作进程数: 1
2025-06-25 09:21:52 [INFO] __main__ [main.py:192] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-25 09:21:52 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 09:21:52 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 09:21:52 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 09:21:52 [INFO] uvicorn.error [server.py:83] - Started server process [22604]
2025-06-25 09:21:52 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-25 09:21:52 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-25 09:21:52 [INFO] main [main.py:83] - 环境: development
2025-06-25 09:21:52 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8009
2025-06-25 09:21:52 [INFO] main [main.py:85] - GPU服务地址: None
2025-06-25 09:21:52 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-25 09:21:52 [INFO] cores.retrieval_service [retrieval_service.py:69] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-25 09:21:52 [INFO] cores.retrieval_service [retrieval_service.py:628] - 创建检索服务实例
2025-06-25 09:21:52 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-25 09:21:52 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-25 09:21:52 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-25 09:21:52 [INFO] cores.retrieval_service [retrieval_service.py:76] - 正在初始化Milvus客户端...
2025-06-25 09:21:52 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-25 09:21:52 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:313] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-25 09:21:52 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:74] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-25 09:21:52 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-25 09:21:52 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:58] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-25 09:21:52 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:59] - 批处理配置: batch_size=32
2025-06-25 09:21:52 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:92] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '30%', 'memory_utilization': '13%', 'temperature': '45°C', 'power_usage': '8.9W'}}
2025-06-25 09:21:52 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:233] - GPU服务稀疏嵌入客户端初始化成功
2025-06-25 09:21:52 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:237] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-25 09:21:53 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:64093 - "GET /docs HTTP/1.1" 200
2025-06-25 09:21:53 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:64093 - "GET /openapi.json HTTP/1.1" 200
2025-06-25 09:22:03 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-25 09:22:03 [WARNING] shared.clients.milvus_client [milvus_client.py:229] - 尝试连接本地Milvus作为回退...
2025-06-25 09:22:13 [WARNING] shared.clients.milvus_client [milvus_client.py:241] - 本地Milvus连接也失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-25 09:22:13 [WARNING] shared.clients.milvus_client [milvus_client.py:244] - Milvus连接失败，将在离线模式下运行
2025-06-25 09:22:13 [INFO] shared.clients.milvus_client [milvus_client.py:193] - Milvus客户端初始化完成: http://************:19530
2025-06-25 09:22:13 [INFO] shared.clients.milvus_client [milvus_client.py:728] - 创建Milvus客户端实例
2025-06-25 09:22:13 [INFO] cores.retrieval_service [retrieval_service.py:78] - Milvus客户端初始化成功
2025-06-25 09:22:13 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-25 09:22:18 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:64092 - "GET /health/detailed HTTP/1.1" 200
2025-06-25 09:22:28 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:64098 - "GET /v2/system/health HTTP/1.1" 200
2025-06-25 09:26:26 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:64375 - "GET /v2/system/info HTTP/1.1" 200
2025-06-25 09:30:59 [INFO] uvicorn.error [server.py:263] - Shutting down
2025-06-25 09:30:59 [INFO] uvicorn.error [on.py:67] - Waiting for application shutdown.
2025-06-25 09:30:59 [INFO] main [main.py:121] - CPU服务关闭
2025-06-25 09:30:59 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-25 09:30:59 [INFO] uvicorn.error [server.py:93] - Finished server process [22604]
2025-06-25 09:31:10 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 09:31:10 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 09:31:10 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 09:31:10 [INFO] __main__ [main.py:184] - 启动CPU服务: 0.0.0.0:8009
2025-06-25 09:31:10 [INFO] __main__ [main.py:190] - 启动模式: development
2025-06-25 09:31:10 [INFO] __main__ [main.py:191] - 工作进程数: 1
2025-06-25 09:31:10 [INFO] __main__ [main.py:192] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-25 09:31:10 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 09:31:10 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 09:31:10 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 09:31:10 [INFO] uvicorn.error [server.py:83] - Started server process [4104]
2025-06-25 09:31:10 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-25 09:31:10 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-25 09:31:10 [INFO] main [main.py:83] - 环境: development
2025-06-25 09:31:10 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8009
2025-06-25 09:31:10 [INFO] main [main.py:85] - GPU服务地址: None
2025-06-25 09:31:10 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-25 09:31:10 [INFO] cores.retrieval_service [retrieval_service.py:69] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-25 09:31:10 [INFO] cores.retrieval_service [retrieval_service.py:628] - 创建检索服务实例
2025-06-25 09:31:10 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-25 09:31:10 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-25 09:31:10 [INFO] cores.retrieval_service [retrieval_service.py:76] - 正在初始化Milvus客户端...
2025-06-25 09:31:10 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:313] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-25 09:31:10 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:74] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-25 09:31:10 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:58] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-25 09:31:10 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-25 09:31:10 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:59] - 批处理配置: batch_size=32
2025-06-25 09:31:10 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-25 09:31:10 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-25 09:31:10 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:92] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '15%', 'memory_utilization': '27%', 'temperature': '44°C', 'power_usage': '6.9W'}}
2025-06-25 09:31:10 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:233] - GPU服务稀疏嵌入客户端初始化成功
2025-06-25 09:31:10 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:237] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-25 09:31:11 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:64791 - "GET /docs HTTP/1.1" 200
2025-06-25 09:31:12 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:64791 - "GET /openapi.json HTTP/1.1" 200
2025-06-25 09:31:20 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-25 09:31:20 [WARNING] shared.clients.milvus_client [milvus_client.py:229] - 尝试连接本地Milvus作为回退...
2025-06-25 09:31:30 [WARNING] shared.clients.milvus_client [milvus_client.py:241] - 本地Milvus连接也失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-25 09:31:30 [WARNING] shared.clients.milvus_client [milvus_client.py:244] - Milvus连接失败，将在离线模式下运行
2025-06-25 09:31:30 [INFO] shared.clients.milvus_client [milvus_client.py:193] - Milvus客户端初始化完成: http://************:19530
2025-06-25 09:31:30 [INFO] shared.clients.milvus_client [milvus_client.py:728] - 创建Milvus客户端实例
2025-06-25 09:31:30 [INFO] cores.retrieval_service [retrieval_service.py:78] - Milvus客户端初始化成功
2025-06-25 09:31:30 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-25 09:31:47 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:64792 - "GET /health HTTP/1.1" 200
2025-06-25 09:32:06 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:64796 - "GET /live HTTP/1.1" 200
2025-06-25 09:32:10 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:64796 - "GET /ready HTTP/1.1" 200
2025-06-25 09:32:20 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:64871 - "GET /v2/system/health HTTP/1.1" 200
2025-06-25 09:38:39 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:65163 - "GET /v2/ HTTP/1.1" 200
2025-06-25 09:38:52 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:65164 - "GET /v2/ HTTP/1.1" 200
2025-06-25 09:40:06 [INFO] uvicorn.error [server.py:263] - Shutting down
2025-06-25 09:40:06 [INFO] uvicorn.error [on.py:67] - Waiting for application shutdown.
2025-06-25 09:40:06 [INFO] main [main.py:121] - CPU服务关闭
2025-06-25 09:40:06 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-25 09:40:06 [INFO] uvicorn.error [server.py:93] - Finished server process [4104]
2025-06-25 09:40:17 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 09:40:17 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 09:40:17 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 09:40:17 [INFO] __main__ [main.py:188] - 启动CPU服务: 0.0.0.0:8009
2025-06-25 09:40:17 [INFO] __main__ [main.py:194] - 启动模式: development
2025-06-25 09:40:17 [INFO] __main__ [main.py:195] - 工作进程数: 1
2025-06-25 09:40:17 [INFO] __main__ [main.py:196] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-25 09:40:17 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 09:40:17 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 09:40:17 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 09:40:17 [INFO] uvicorn.error [server.py:83] - Started server process [20168]
2025-06-25 09:40:17 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-25 09:40:17 [INFO] main [main.py:86] - 🚀 初始化CPU服务...
2025-06-25 09:40:17 [INFO] main [main.py:87] - 环境: development
2025-06-25 09:40:17 [INFO] main [main.py:88] - 服务地址: http://0.0.0.0:8009
2025-06-25 09:40:17 [INFO] main [main.py:89] - GPU服务地址: None
2025-06-25 09:40:17 [INFO] main [main.py:92] - 🔥 开始预热服务组件...
2025-06-25 09:40:17 [INFO] cores.retrieval_service [retrieval_service.py:69] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-25 09:40:17 [INFO] cores.retrieval_service [retrieval_service.py:628] - 创建检索服务实例
2025-06-25 09:40:17 [INFO] main [main.py:103] - 正在预热Milvus客户端...
2025-06-25 09:40:17 [INFO] main [main.py:113] - ✅ 服务预热已启动（后台进行）
2025-06-25 09:40:17 [INFO] cores.retrieval_service [retrieval_service.py:76] - 正在初始化Milvus客户端...
2025-06-25 09:40:17 [INFO] main [main.py:118] - ✅ CPU服务初始化完成
2025-06-25 09:40:17 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:313] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-25 09:40:17 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-25 09:40:17 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:74] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-25 09:40:17 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:58] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-25 09:40:17 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-25 09:40:17 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:59] - 批处理配置: batch_size=32
2025-06-25 09:40:17 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:92] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '7%', 'memory_utilization': '19%', 'temperature': '43°C', 'power_usage': '6.6W'}}
2025-06-25 09:40:17 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:233] - GPU服务稀疏嵌入客户端初始化成功
2025-06-25 09:40:17 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:237] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-25 09:40:19 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:65243 - "GET /docs HTTP/1.1" 200
2025-06-25 09:40:19 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:65243 - "GET /openapi.json HTTP/1.1" 200
2025-06-25 09:40:22 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:65243 - "GET /docs HTTP/1.1" 200
2025-06-25 09:40:22 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:65243 - "GET /openapi.json HTTP/1.1" 200
2025-06-25 09:40:27 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-25 09:40:27 [WARNING] shared.clients.milvus_client [milvus_client.py:229] - 尝试连接本地Milvus作为回退...
2025-06-25 09:40:37 [WARNING] shared.clients.milvus_client [milvus_client.py:241] - 本地Milvus连接也失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-25 09:40:37 [WARNING] shared.clients.milvus_client [milvus_client.py:244] - Milvus连接失败，将在离线模式下运行
2025-06-25 09:40:37 [INFO] shared.clients.milvus_client [milvus_client.py:193] - Milvus客户端初始化完成: http://************:19530
2025-06-25 09:40:37 [INFO] shared.clients.milvus_client [milvus_client.py:728] - 创建Milvus客户端实例
2025-06-25 09:40:37 [INFO] cores.retrieval_service [retrieval_service.py:78] - Milvus客户端初始化成功
2025-06-25 09:40:37 [INFO] main [main.py:105] - ✅ Milvus客户端预热完成
2025-06-25 09:40:42 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:65244 - "GET /docs HTTP/1.1" 200
2025-06-25 09:40:42 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:65244 - "GET /openapi.json HTTP/1.1" 200
2025-06-25 09:58:49 [INFO] uvicorn.error [server.py:263] - Shutting down
2025-06-25 09:58:49 [INFO] uvicorn.error [on.py:67] - Waiting for application shutdown.
2025-06-25 09:58:49 [INFO] main [main.py:125] - CPU服务关闭
2025-06-25 09:58:49 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-25 09:58:49 [INFO] uvicorn.error [server.py:93] - Finished server process [20168]
2025-06-25 09:59:23 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 09:59:23 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 09:59:23 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 09:59:23 [INFO] __main__ [main.py:188] - 启动CPU服务: 0.0.0.0:8009
2025-06-25 09:59:23 [INFO] __main__ [main.py:194] - 启动模式: development
2025-06-25 09:59:23 [INFO] __main__ [main.py:195] - 工作进程数: 1
2025-06-25 09:59:23 [INFO] __main__ [main.py:196] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-25 09:59:23 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 09:59:23 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 09:59:23 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 09:59:23 [INFO] uvicorn.error [server.py:83] - Started server process [4456]
2025-06-25 09:59:23 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-25 09:59:23 [INFO] main [main.py:86] - 🚀 初始化CPU服务...
2025-06-25 09:59:23 [INFO] main [main.py:87] - 环境: development
2025-06-25 09:59:23 [INFO] main [main.py:88] - 服务地址: http://0.0.0.0:8009
2025-06-25 09:59:23 [INFO] main [main.py:89] - GPU服务地址: None
2025-06-25 09:59:23 [INFO] main [main.py:92] - 🔥 开始预热服务组件...
2025-06-25 09:59:23 [INFO] cores.retrieval_service [retrieval_service.py:69] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-25 09:59:23 [INFO] cores.retrieval_service [retrieval_service.py:628] - 创建检索服务实例
2025-06-25 09:59:23 [INFO] main [main.py:103] - 正在预热Milvus客户端...
2025-06-25 09:59:23 [INFO] main [main.py:113] - ✅ 服务预热已启动（后台进行）
2025-06-25 09:59:23 [INFO] cores.retrieval_service [retrieval_service.py:76] - 正在初始化Milvus客户端...
2025-06-25 09:59:23 [INFO] main [main.py:118] - ✅ CPU服务初始化完成
2025-06-25 09:59:23 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:313] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-25 09:59:23 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-25 09:59:23 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:74] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-25 09:59:23 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-25 09:59:23 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:58] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-25 09:59:23 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:59] - 批处理配置: batch_size=32
2025-06-25 09:59:23 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:92] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '54%', 'memory_utilization': '24%', 'temperature': '43°C', 'power_usage': '8.7W'}}
2025-06-25 09:59:23 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:233] - GPU服务稀疏嵌入客户端初始化成功
2025-06-25 09:59:23 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:237] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-25 09:59:25 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:49547 - "GET /docs HTTP/1.1" 200
2025-06-25 09:59:25 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:49547 - "GET /openapi.json HTTP/1.1" 200
2025-06-25 09:59:33 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-25 09:59:33 [WARNING] shared.clients.milvus_client [milvus_client.py:229] - 尝试连接本地Milvus作为回退...
2025-06-25 09:59:43 [WARNING] shared.clients.milvus_client [milvus_client.py:241] - 本地Milvus连接也失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-25 09:59:43 [WARNING] shared.clients.milvus_client [milvus_client.py:244] - Milvus连接失败，将在离线模式下运行
2025-06-25 09:59:43 [INFO] shared.clients.milvus_client [milvus_client.py:193] - Milvus客户端初始化完成: http://************:19530
2025-06-25 09:59:43 [INFO] shared.clients.milvus_client [milvus_client.py:728] - 创建Milvus客户端实例
2025-06-25 09:59:43 [INFO] cores.retrieval_service [retrieval_service.py:78] - Milvus客户端初始化成功
2025-06-25 09:59:43 [INFO] main [main.py:105] - ✅ Milvus客户端预热完成
2025-06-25 10:00:17 [INFO] uvicorn.error [server.py:263] - Shutting down
2025-06-25 10:00:17 [INFO] uvicorn.error [on.py:67] - Waiting for application shutdown.
2025-06-25 10:00:17 [INFO] main [main.py:125] - CPU服务关闭
2025-06-25 10:00:17 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-25 10:00:17 [INFO] uvicorn.error [server.py:93] - Finished server process [4456]
2025-06-25 10:00:47 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 10:00:47 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 10:00:47 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 10:00:47 [INFO] __main__ [main.py:188] - 启动CPU服务: 0.0.0.0:8009
2025-06-25 10:00:47 [INFO] __main__ [main.py:194] - 启动模式: development
2025-06-25 10:00:47 [INFO] __main__ [main.py:195] - 工作进程数: 1
2025-06-25 10:00:47 [INFO] __main__ [main.py:196] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-25 10:00:47 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 10:00:47 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 10:00:47 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 10:00:47 [INFO] uvicorn.error [server.py:83] - Started server process [18012]
2025-06-25 10:00:47 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-25 10:00:47 [INFO] main [main.py:86] - 🚀 初始化CPU服务...
2025-06-25 10:00:47 [INFO] main [main.py:87] - 环境: development
2025-06-25 10:00:47 [INFO] main [main.py:88] - 服务地址: http://0.0.0.0:8009
2025-06-25 10:00:47 [INFO] main [main.py:89] - GPU服务地址: None
2025-06-25 10:00:47 [INFO] main [main.py:92] - 🔥 开始预热服务组件...
2025-06-25 10:00:47 [INFO] cores.retrieval_service [retrieval_service.py:69] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-25 10:00:47 [INFO] cores.retrieval_service [retrieval_service.py:628] - 创建检索服务实例
2025-06-25 10:00:47 [INFO] main [main.py:103] - 正在预热Milvus客户端...
2025-06-25 10:00:47 [INFO] main [main.py:113] - ✅ 服务预热已启动（后台进行）
2025-06-25 10:00:47 [INFO] cores.retrieval_service [retrieval_service.py:76] - 正在初始化Milvus客户端...
2025-06-25 10:00:47 [INFO] main [main.py:118] - ✅ CPU服务初始化完成
2025-06-25 10:00:47 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:313] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-25 10:00:47 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-25 10:00:47 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:74] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-25 10:00:47 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-25 10:00:47 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:58] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-25 10:00:47 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:59] - 批处理配置: batch_size=32
2025-06-25 10:00:47 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:92] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '41%', 'memory_utilization': '32%', 'temperature': '44°C', 'power_usage': '7.7W'}}
2025-06-25 10:00:47 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:233] - GPU服务稀疏嵌入客户端初始化成功
2025-06-25 10:00:47 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:237] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-25 10:00:50 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:49623 - "GET /docs HTTP/1.1" 200
2025-06-25 10:00:52 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:49623 - "GET /openapi.json HTTP/1.1" 200
2025-06-25 10:00:57 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-25 10:00:57 [WARNING] shared.clients.milvus_client [milvus_client.py:229] - 尝试连接本地Milvus作为回退...
2025-06-25 10:01:07 [WARNING] shared.clients.milvus_client [milvus_client.py:241] - 本地Milvus连接也失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-25 10:01:07 [WARNING] shared.clients.milvus_client [milvus_client.py:244] - Milvus连接失败，将在离线模式下运行
2025-06-25 10:01:07 [INFO] shared.clients.milvus_client [milvus_client.py:193] - Milvus客户端初始化完成: http://************:19530
2025-06-25 10:01:07 [INFO] shared.clients.milvus_client [milvus_client.py:728] - 创建Milvus客户端实例
2025-06-25 10:01:07 [INFO] cores.retrieval_service [retrieval_service.py:78] - Milvus客户端初始化成功
2025-06-25 10:01:07 [INFO] main [main.py:105] - ✅ Milvus客户端预热完成
2025-06-25 10:05:34 [INFO] uvicorn.error [server.py:263] - Shutting down
2025-06-25 10:05:34 [INFO] uvicorn.error [on.py:67] - Waiting for application shutdown.
2025-06-25 10:05:34 [INFO] main [main.py:125] - CPU服务关闭
2025-06-25 10:05:34 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-25 10:05:34 [INFO] uvicorn.error [server.py:93] - Finished server process [18012]
2025-06-25 10:05:44 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 10:05:44 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 10:05:44 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 10:05:44 [INFO] __main__ [main.py:188] - 启动CPU服务: 0.0.0.0:8009
2025-06-25 10:05:44 [INFO] __main__ [main.py:194] - 启动模式: development
2025-06-25 10:05:44 [INFO] __main__ [main.py:195] - 工作进程数: 1
2025-06-25 10:05:44 [INFO] __main__ [main.py:196] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-25 10:05:44 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 10:05:44 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 10:05:44 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 10:05:44 [INFO] uvicorn.error [server.py:83] - Started server process [15896]
2025-06-25 10:05:44 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-25 10:05:44 [INFO] main [main.py:86] - 🚀 初始化CPU服务...
2025-06-25 10:05:44 [INFO] main [main.py:87] - 环境: development
2025-06-25 10:05:44 [INFO] main [main.py:88] - 服务地址: http://0.0.0.0:8009
2025-06-25 10:05:44 [INFO] main [main.py:89] - GPU服务地址: None
2025-06-25 10:05:44 [INFO] main [main.py:92] - 🔥 开始预热服务组件...
2025-06-25 10:05:44 [INFO] cores.retrieval_service [retrieval_service.py:65] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-25 10:05:44 [INFO] cores.retrieval_service [retrieval_service.py:624] - 创建检索服务实例
2025-06-25 10:05:44 [INFO] main [main.py:103] - 正在预热Milvus客户端...
2025-06-25 10:05:44 [INFO] main [main.py:113] - ✅ 服务预热已启动（后台进行）
2025-06-25 10:05:44 [INFO] main [main.py:118] - ✅ CPU服务初始化完成
2025-06-25 10:05:44 [INFO] cores.retrieval_service [retrieval_service.py:72] - 正在初始化Milvus客户端...
2025-06-25 10:05:44 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-25 10:05:44 [WARNING] cores.retrieval_service [retrieval_service.py:76] - Milvus客户端初始化失败: name '_get_settings' is not defined
2025-06-25 10:05:44 [INFO] main [main.py:105] - ✅ Milvus客户端预热完成
2025-06-25 10:05:44 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-25 10:07:26 [INFO] uvicorn.error [server.py:263] - Shutting down
2025-06-25 10:07:26 [INFO] uvicorn.error [server.py:93] - Finished server process [15896]
2025-06-25 10:07:26 [INFO] main [main.py:125] - CPU服务关闭
2025-06-25 10:07:26 [ERROR] uvicorn.error [on.py:134] - Traceback (most recent call last):
  File "D:\ProgramFiles\python311\Lib\asyncio\runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "D:\ProgramFiles\python311\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ProgramFiles\python311\Lib\asyncio\base_events.py", line 641, in run_until_complete
    self.run_forever()
  File "D:\ProgramFiles\python311\Lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "D:\ProgramFiles\python311\Lib\asyncio\base_events.py", line 608, in run_forever
    self._run_once()
  File "D:\ProgramFiles\python311\Lib\asyncio\base_events.py", line 1936, in _run_once
    handle._run()
  File "D:\ProgramFiles\python311\Lib\asyncio\events.py", line 84, in _run
    self._context.run(self._callback, *self._args)
  File "D:\Project\Backend\ai\ai-jiesujiban-v3\.venv\Lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "D:\ProgramFiles\python311\Lib\contextlib.py", line 144, in __exit__
    next(self.gen)
  File "D:\Project\Backend\ai\ai-jiesujiban-v3\.venv\Lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
  File "D:\ProgramFiles\python311\Lib\asyncio\runners.py", line 157, in _on_sigint
    raise KeyboardInterrupt()
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Project\Backend\ai\ai-jiesujiban-v3\.venv\Lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "D:\Project\Backend\ai\ai-jiesujiban-v3\.venv\Lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ProgramFiles\python311\Lib\asyncio\queues.py", line 158, in get
    await getter
asyncio.exceptions.CancelledError

2025-06-25 10:07:39 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 10:07:39 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 10:07:39 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 10:07:39 [INFO] __main__ [main.py:188] - 启动CPU服务: 0.0.0.0:8009
2025-06-25 10:07:39 [INFO] __main__ [main.py:194] - 启动模式: development
2025-06-25 10:07:39 [INFO] __main__ [main.py:195] - 工作进程数: 1
2025-06-25 10:07:39 [INFO] __main__ [main.py:196] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-25 10:07:39 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 10:07:39 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 10:07:39 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 10:07:39 [INFO] uvicorn.error [server.py:83] - Started server process [26404]
2025-06-25 10:07:39 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-25 10:07:39 [INFO] main [main.py:86] - 🚀 初始化CPU服务...
2025-06-25 10:07:39 [INFO] main [main.py:87] - 环境: development
2025-06-25 10:07:39 [INFO] main [main.py:88] - 服务地址: http://0.0.0.0:8009
2025-06-25 10:07:39 [INFO] main [main.py:89] - GPU服务地址: None
2025-06-25 10:07:39 [INFO] main [main.py:92] - 🔥 开始预热服务组件...
2025-06-25 10:07:39 [INFO] cores.retrieval_service [retrieval_service.py:65] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-25 10:07:39 [INFO] cores.retrieval_service [retrieval_service.py:624] - 创建检索服务实例
2025-06-25 10:07:39 [INFO] main [main.py:103] - 正在预热Milvus客户端...
2025-06-25 10:07:39 [INFO] main [main.py:113] - ✅ 服务预热已启动（后台进行）
2025-06-25 10:07:39 [INFO] cores.retrieval_service [retrieval_service.py:72] - 正在初始化Milvus客户端...
2025-06-25 10:07:39 [INFO] main [main.py:118] - ✅ CPU服务初始化完成
2025-06-25 10:07:39 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:309] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-25 10:07:39 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-25 10:07:39 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:70] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-25 10:07:39 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-25 10:07:39 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:54] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-25 10:07:39 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:55] - 批处理配置: batch_size=32
2025-06-25 10:07:39 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:88] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '20%', 'memory_utilization': '27%', 'temperature': '44°C', 'power_usage': '6.8W'}}
2025-06-25 10:07:39 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:229] - GPU服务稀疏嵌入客户端初始化成功
2025-06-25 10:07:39 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:233] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-25 10:07:49 [ERROR] shared.clients.milvus_client [milvus_client.py:221] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-25 10:07:49 [WARNING] shared.clients.milvus_client [milvus_client.py:225] - 尝试连接本地Milvus作为回退...
2025-06-25 10:07:59 [WARNING] shared.clients.milvus_client [milvus_client.py:237] - 本地Milvus连接也失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-25 10:07:59 [WARNING] shared.clients.milvus_client [milvus_client.py:240] - Milvus连接失败，将在离线模式下运行
2025-06-25 10:07:59 [INFO] shared.clients.milvus_client [milvus_client.py:189] - Milvus客户端初始化完成: http://************:19530
2025-06-25 10:07:59 [INFO] shared.clients.milvus_client [milvus_client.py:724] - 创建Milvus客户端实例
2025-06-25 10:07:59 [INFO] cores.retrieval_service [retrieval_service.py:74] - Milvus客户端初始化成功
2025-06-25 10:07:59 [INFO] main [main.py:105] - ✅ Milvus客户端预热完成
2025-06-25 10:08:43 [INFO] uvicorn.error [server.py:263] - Shutting down
2025-06-25 10:08:43 [INFO] uvicorn.error [on.py:67] - Waiting for application shutdown.
2025-06-25 10:08:43 [INFO] main [main.py:125] - CPU服务关闭
2025-06-25 10:08:43 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-25 10:08:43 [INFO] uvicorn.error [server.py:93] - Finished server process [26404]
2025-06-25 10:08:53 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 10:08:53 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 10:08:53 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 10:08:53 [INFO] __main__ [main.py:188] - 启动CPU服务: 0.0.0.0:8009
2025-06-25 10:08:53 [INFO] __main__ [main.py:194] - 启动模式: development
2025-06-25 10:08:53 [INFO] __main__ [main.py:195] - 工作进程数: 1
2025-06-25 10:08:53 [INFO] __main__ [main.py:196] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-25 10:08:53 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 10:08:53 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 10:08:53 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 10:08:53 [INFO] uvicorn.error [server.py:83] - Started server process [21384]
2025-06-25 10:08:53 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-25 10:08:53 [INFO] main [main.py:86] - 🚀 初始化CPU服务...
2025-06-25 10:08:53 [INFO] main [main.py:87] - 环境: development
2025-06-25 10:08:53 [INFO] main [main.py:88] - 服务地址: http://0.0.0.0:8009
2025-06-25 10:08:53 [INFO] main [main.py:89] - GPU服务地址: None
2025-06-25 10:08:53 [INFO] main [main.py:92] - 🔥 开始预热服务组件...
2025-06-25 10:08:53 [INFO] cores.retrieval_service [retrieval_service.py:65] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-25 10:08:53 [INFO] cores.retrieval_service [retrieval_service.py:624] - 创建检索服务实例
2025-06-25 10:08:53 [INFO] main [main.py:103] - 正在预热Milvus客户端...
2025-06-25 10:08:53 [INFO] main [main.py:113] - ✅ 服务预热已启动（后台进行）
2025-06-25 10:08:53 [INFO] cores.retrieval_service [retrieval_service.py:72] - 正在初始化Milvus客户端...
2025-06-25 10:08:53 [INFO] main [main.py:118] - ✅ CPU服务初始化完成
2025-06-25 10:08:53 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:309] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-25 10:08:53 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-25 10:08:53 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:70] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-25 10:08:53 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:54] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-25 10:08:53 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-25 10:08:53 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:55] - 批处理配置: batch_size=32
2025-06-25 10:08:53 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:88] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '1%', 'memory_utilization': '25%', 'temperature': '43°C', 'power_usage': '6.2W'}}
2025-06-25 10:08:53 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:229] - GPU服务稀疏嵌入客户端初始化成功
2025-06-25 10:08:53 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:233] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-25 10:09:03 [ERROR] shared.clients.milvus_client [milvus_client.py:221] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-25 10:09:03 [WARNING] shared.clients.milvus_client [milvus_client.py:225] - 尝试连接本地Milvus作为回退...
2025-06-25 10:09:14 [WARNING] shared.clients.milvus_client [milvus_client.py:237] - 本地Milvus连接也失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-25 10:09:14 [WARNING] shared.clients.milvus_client [milvus_client.py:240] - Milvus连接失败，将在离线模式下运行
2025-06-25 10:09:14 [INFO] shared.clients.milvus_client [milvus_client.py:189] - Milvus客户端初始化完成: http://************:19530
2025-06-25 10:09:14 [INFO] shared.clients.milvus_client [milvus_client.py:724] - 创建Milvus客户端实例
2025-06-25 10:09:14 [INFO] cores.retrieval_service [retrieval_service.py:74] - Milvus客户端初始化成功
2025-06-25 10:09:14 [INFO] main [main.py:105] - ✅ Milvus客户端预热完成
2025-06-25 10:22:35 [INFO] uvicorn.error [server.py:263] - Shutting down
2025-06-25 10:22:35 [INFO] uvicorn.error [on.py:67] - Waiting for application shutdown.
2025-06-25 10:22:35 [INFO] main [main.py:125] - CPU服务关闭
2025-06-25 10:22:35 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-25 10:22:35 [INFO] uvicorn.error [server.py:93] - Finished server process [21384]
2025-06-25 10:22:44 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 10:22:44 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 10:22:44 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 10:22:44 [INFO] __main__ [main.py:188] - 启动CPU服务: 0.0.0.0:8009
2025-06-25 10:22:44 [INFO] __main__ [main.py:194] - 启动模式: development
2025-06-25 10:22:44 [INFO] __main__ [main.py:195] - 工作进程数: 1
2025-06-25 10:22:44 [INFO] __main__ [main.py:196] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-25 10:22:44 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 10:22:44 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 10:22:44 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 10:22:44 [INFO] uvicorn.error [server.py:83] - Started server process [21452]
2025-06-25 10:22:44 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-25 10:22:44 [INFO] main [main.py:86] - 🚀 初始化CPU服务...
2025-06-25 10:22:44 [INFO] main [main.py:87] - 环境: development
2025-06-25 10:22:44 [INFO] main [main.py:88] - 服务地址: http://0.0.0.0:8009
2025-06-25 10:22:44 [INFO] main [main.py:89] - GPU服务地址: None
2025-06-25 10:22:44 [INFO] main [main.py:92] - 🔥 开始预热服务组件...
2025-06-25 10:22:44 [INFO] cores.retrieval_service [retrieval_service.py:65] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-25 10:22:44 [INFO] cores.retrieval_service [retrieval_service.py:624] - 创建检索服务实例
2025-06-25 10:22:44 [INFO] main [main.py:103] - 正在预热Milvus客户端...
2025-06-25 10:22:44 [INFO] main [main.py:113] - ✅ 服务预热已启动（后台进行）
2025-06-25 10:22:44 [INFO] cores.retrieval_service [retrieval_service.py:72] - 正在初始化Milvus客户端...
2025-06-25 10:22:44 [INFO] main [main.py:118] - ✅ CPU服务初始化完成
2025-06-25 10:22:44 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:309] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-25 10:22:44 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-25 10:22:44 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:70] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-25 10:22:44 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:54] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-25 10:22:44 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-25 10:22:44 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:55] - 批处理配置: batch_size=32
2025-06-25 10:22:44 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:88] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '6%', 'memory_utilization': '27%', 'temperature': '42°C', 'power_usage': '6.5W'}}
2025-06-25 10:22:44 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:229] - GPU服务稀疏嵌入客户端初始化成功
2025-06-25 10:22:44 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:233] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-25 10:22:55 [ERROR] shared.clients.milvus_client [milvus_client.py:221] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-25 10:22:55 [WARNING] shared.clients.milvus_client [milvus_client.py:225] - 尝试连接本地Milvus作为回退...
2025-06-25 10:23:05 [WARNING] shared.clients.milvus_client [milvus_client.py:237] - 本地Milvus连接也失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-25 10:23:05 [WARNING] shared.clients.milvus_client [milvus_client.py:240] - Milvus连接失败，将在离线模式下运行
2025-06-25 10:23:05 [INFO] shared.clients.milvus_client [milvus_client.py:189] - Milvus客户端初始化完成: http://************:19530
2025-06-25 10:23:05 [INFO] shared.clients.milvus_client [milvus_client.py:724] - 创建Milvus客户端实例
2025-06-25 10:23:05 [INFO] cores.retrieval_service [retrieval_service.py:74] - Milvus客户端初始化成功
2025-06-25 10:23:05 [INFO] main [main.py:105] - ✅ Milvus客户端预热完成
2025-06-25 10:28:41 [INFO] uvicorn.error [server.py:263] - Shutting down
2025-06-25 10:28:41 [INFO] uvicorn.error [on.py:67] - Waiting for application shutdown.
2025-06-25 10:28:41 [INFO] main [main.py:125] - CPU服务关闭
2025-06-25 10:28:41 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-25 10:28:41 [INFO] uvicorn.error [server.py:93] - Finished server process [21452]
2025-06-25 10:28:50 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 10:28:50 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 10:28:50 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 10:28:50 [INFO] __main__ [main.py:188] - 启动CPU服务: 0.0.0.0:8009
2025-06-25 10:28:50 [INFO] __main__ [main.py:194] - 启动模式: development
2025-06-25 10:28:50 [INFO] __main__ [main.py:195] - 工作进程数: 1
2025-06-25 10:28:50 [INFO] __main__ [main.py:196] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-25 10:28:50 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 10:28:50 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 10:28:50 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 10:28:50 [INFO] uvicorn.error [server.py:83] - Started server process [19824]
2025-06-25 10:28:50 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-25 10:28:50 [INFO] main [main.py:86] - 🚀 初始化CPU服务...
2025-06-25 10:28:50 [INFO] main [main.py:87] - 环境: development
2025-06-25 10:28:50 [INFO] main [main.py:88] - 服务地址: http://0.0.0.0:8009
2025-06-25 10:28:50 [INFO] main [main.py:89] - GPU服务地址: None
2025-06-25 10:28:50 [INFO] main [main.py:92] - 🔥 开始预热服务组件...
2025-06-25 10:28:50 [INFO] cores.retrieval_service [retrieval_service.py:65] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-25 10:28:50 [INFO] cores.retrieval_service [retrieval_service.py:624] - 创建检索服务实例
2025-06-25 10:28:50 [INFO] main [main.py:103] - 正在预热Milvus客户端...
2025-06-25 10:28:50 [INFO] main [main.py:113] - ✅ 服务预热已启动（后台进行）
2025-06-25 10:28:50 [INFO] cores.retrieval_service [retrieval_service.py:72] - 正在初始化Milvus客户端...
2025-06-25 10:28:50 [INFO] main [main.py:118] - ✅ CPU服务初始化完成
2025-06-25 10:28:50 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:309] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-25 10:28:50 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-25 10:28:50 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:70] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-25 10:28:50 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:54] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-25 10:28:50 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-25 10:28:50 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:55] - 批处理配置: batch_size=32
2025-06-25 10:28:51 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:88] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '4%', 'memory_utilization': '31%', 'temperature': '42°C', 'power_usage': '6.2W'}}
2025-06-25 10:28:51 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:229] - GPU服务稀疏嵌入客户端初始化成功
2025-06-25 10:28:51 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:233] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-25 10:28:59 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:50939 - "GET /docs HTTP/1.1" 200
2025-06-25 10:29:00 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:50939 - "GET /openapi.json HTTP/1.1" 200
2025-06-25 10:29:01 [ERROR] shared.clients.milvus_client [milvus_client.py:221] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-25 10:29:01 [WARNING] shared.clients.milvus_client [milvus_client.py:240] - Milvus连接失败，将在离线模式下运行
2025-06-25 10:29:01 [INFO] shared.clients.milvus_client [milvus_client.py:189] - Milvus客户端初始化完成: http://localhost:19530
2025-06-25 10:29:01 [INFO] shared.clients.milvus_client [milvus_client.py:724] - 创建Milvus客户端实例
2025-06-25 10:29:01 [INFO] cores.retrieval_service [retrieval_service.py:74] - Milvus客户端初始化成功
2025-06-25 10:29:01 [INFO] main [main.py:105] - ✅ Milvus客户端预热完成
2025-06-25 10:54:31 [INFO] uvicorn.error [server.py:263] - Shutting down
2025-06-25 10:54:31 [INFO] uvicorn.error [on.py:67] - Waiting for application shutdown.
2025-06-25 10:54:31 [INFO] main [main.py:125] - CPU服务关闭
2025-06-25 10:54:31 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-25 10:54:31 [INFO] uvicorn.error [server.py:93] - Finished server process [19824]
2025-06-25 10:54:40 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 10:54:40 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 10:54:40 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 10:54:40 [INFO] __main__ [main.py:188] - 启动CPU服务: 0.0.0.0:8009
2025-06-25 10:54:40 [INFO] __main__ [main.py:194] - 启动模式: development
2025-06-25 10:54:40 [INFO] __main__ [main.py:195] - 工作进程数: 1
2025-06-25 10:54:40 [INFO] __main__ [main.py:196] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-25 10:54:40 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 10:54:40 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 10:54:40 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 10:54:40 [INFO] uvicorn.error [server.py:83] - Started server process [18360]
2025-06-25 10:54:40 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-25 10:54:40 [INFO] main [main.py:86] - 🚀 初始化CPU服务...
2025-06-25 10:54:40 [INFO] main [main.py:87] - 环境: development
2025-06-25 10:54:40 [INFO] main [main.py:88] - 服务地址: http://0.0.0.0:8009
2025-06-25 10:54:40 [INFO] main [main.py:89] - GPU服务地址: None
2025-06-25 10:54:40 [INFO] main [main.py:92] - 🔥 开始预热服务组件...
2025-06-25 10:54:40 [INFO] cores.retrieval_service [retrieval_service.py:65] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-25 10:54:40 [INFO] cores.retrieval_service [retrieval_service.py:624] - 创建检索服务实例
2025-06-25 10:54:40 [INFO] main [main.py:103] - 正在预热Milvus客户端...
2025-06-25 10:54:40 [INFO] main [main.py:113] - ✅ 服务预热已启动（后台进行）
2025-06-25 10:54:40 [INFO] cores.retrieval_service [retrieval_service.py:72] - 正在初始化Milvus客户端...
2025-06-25 10:54:40 [INFO] main [main.py:118] - ✅ CPU服务初始化完成
2025-06-25 10:54:40 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:309] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-25 10:54:40 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-25 10:54:40 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:70] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-25 10:54:40 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-25 10:54:40 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:54] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-25 10:54:40 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:55] - 批处理配置: batch_size=32
2025-06-25 10:54:40 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:88] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '5%', 'memory_utilization': '16%', 'temperature': '43°C', 'power_usage': '6.9W'}}
2025-06-25 10:54:40 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:229] - GPU服务稀疏嵌入客户端初始化成功
2025-06-25 10:54:40 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:233] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-25 10:54:50 [ERROR] shared.clients.milvus_client [milvus_client.py:221] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-25 10:54:50 [WARNING] shared.clients.milvus_client [milvus_client.py:240] - Milvus连接失败，将在离线模式下运行
2025-06-25 10:54:50 [INFO] shared.clients.milvus_client [milvus_client.py:189] - Milvus客户端初始化完成: http://localhost:19530
2025-06-25 10:54:50 [INFO] shared.clients.milvus_client [milvus_client.py:724] - 创建Milvus客户端实例
2025-06-25 10:54:50 [INFO] cores.retrieval_service [retrieval_service.py:74] - Milvus客户端初始化成功
2025-06-25 10:54:50 [INFO] main [main.py:105] - ✅ Milvus客户端预热完成
2025-06-25 11:00:41 [WARNING] cores.retrieval_service [retrieval_service.py:360] - Milvus不可用，使用GPU+API离线向量检索: department
2025-06-25 11:00:41 [INFO] cores.retrieval_service [retrieval_service.py:88] - 正在初始化GPU稀疏嵌入函数...
2025-06-25 11:00:41 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:309] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-25 11:00:41 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:70] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-25 11:00:41 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:54] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-25 11:00:41 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:55] - 批处理配置: batch_size=32
2025-06-25 11:00:41 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:88] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '7%', 'memory_utilization': '1%', 'temperature': '45°C', 'power_usage': '11.2W'}}
2025-06-25 11:00:41 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:229] - GPU服务稀疏嵌入客户端初始化成功
2025-06-25 11:00:41 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:233] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-25 11:00:41 [INFO] cores.retrieval_service [retrieval_service.py:90] - GPU稀疏嵌入函数初始化成功
2025-06-25 11:00:41 [INFO] cores.retrieval_service [retrieval_service.py:117] - 使用GPU服务生成查询稀疏嵌入: 你好...
2025-06-25 11:00:41 [INFO] cores.retrieval_service [retrieval_service.py:119] - GPU稀疏嵌入生成成功，维度: 2
2025-06-25 11:00:41 [INFO] cores.retrieval_service [retrieval_service.py:126] - 使用API服务生成查询密集嵌入: 你好...
2025-06-25 11:00:42 [INFO] shared.clients.embedding_client [embedding_client.py:179] - 成功获取 1 个密集嵌入（共 1 个文本）
2025-06-25 11:00:42 [INFO] cores.retrieval_service [retrieval_service.py:130] - API密集嵌入生成成功，维度: 1024
2025-06-25 11:00:42 [INFO] cores.retrieval_service [retrieval_service.py:178] - 离线向量检索完成: 0 个结果，使用GPU+API真实计算
2025-06-25 11:00:42 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: department, 查询: 你好..., 结果数: 0, 耗时: 0.467秒
2025-06-25 11:00:42 [WARNING] cores.retrieval_service [retrieval_service.py:360] - Milvus不可用，使用GPU+API离线向量检索: guideline
2025-06-25 11:00:42 [INFO] cores.retrieval_service [retrieval_service.py:117] - 使用GPU服务生成查询稀疏嵌入: 你好...
2025-06-25 11:00:42 [INFO] cores.retrieval_service [retrieval_service.py:119] - GPU稀疏嵌入生成成功，维度: 2
2025-06-25 11:00:42 [INFO] cores.retrieval_service [retrieval_service.py:126] - 使用API服务生成查询密集嵌入: 你好...
2025-06-25 11:00:42 [INFO] shared.clients.embedding_client [embedding_client.py:179] - 成功获取 1 个密集嵌入（共 1 个文本）
2025-06-25 11:00:42 [INFO] cores.retrieval_service [retrieval_service.py:130] - API密集嵌入生成成功，维度: 1024
2025-06-25 11:00:42 [INFO] cores.retrieval_service [retrieval_service.py:178] - 离线向量检索完成: 0 个结果，使用GPU+API真实计算
2025-06-25 11:00:42 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: guideline, 查询: 你好..., 结果数: 0, 耗时: 0.241秒
2025-06-25 11:00:42 [WARNING] cores.retrieval_service [retrieval_service.py:360] - Milvus不可用，使用GPU+API离线向量检索: historical
2025-06-25 11:00:42 [INFO] cores.retrieval_service [retrieval_service.py:117] - 使用GPU服务生成查询稀疏嵌入: 你好...
2025-06-25 11:00:42 [INFO] cores.retrieval_service [retrieval_service.py:119] - GPU稀疏嵌入生成成功，维度: 2
2025-06-25 11:00:42 [INFO] cores.retrieval_service [retrieval_service.py:126] - 使用API服务生成查询密集嵌入: 你好...
2025-06-25 11:00:42 [INFO] shared.clients.embedding_client [embedding_client.py:179] - 成功获取 1 个密集嵌入（共 1 个文本）
2025-06-25 11:00:42 [INFO] cores.retrieval_service [retrieval_service.py:130] - API密集嵌入生成成功，维度: 1024
2025-06-25 11:00:42 [INFO] cores.retrieval_service [retrieval_service.py:178] - 离线向量检索完成: 0 个结果，使用GPU+API真实计算
2025-06-25 11:00:42 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: historical, 查询: 你好..., 结果数: 0, 耗时: 0.355秒
2025-06-25 11:00:42 [WARNING] cores.retrieval_service [retrieval_service.py:360] - Milvus不可用，使用GPU+API离线向量检索: delegated
2025-06-25 11:00:42 [WARNING] cores.retrieval_service [retrieval_service.py:122] - GPU服务不可用，跳过稀疏嵌入
2025-06-25 11:00:42 [INFO] cores.retrieval_service [retrieval_service.py:126] - 使用API服务生成查询密集嵌入: 你好...
2025-06-25 11:00:42 [INFO] shared.clients.embedding_client [embedding_client.py:179] - 成功获取 1 个密集嵌入（共 1 个文本）
2025-06-25 11:00:42 [INFO] cores.retrieval_service [retrieval_service.py:130] - API密集嵌入生成成功，维度: 1024
2025-06-25 11:00:42 [INFO] cores.retrieval_service [retrieval_service.py:178] - 离线向量检索完成: 0 个结果，使用GPU+API真实计算
2025-06-25 11:00:42 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: delegated, 查询: 你好..., 结果数: 0, 耗时: 0.283秒
2025-06-25 11:00:42 [INFO] cores.retrieval_service [retrieval_service.py:519] - 并行检索完成: 查询: 你好..., 总耗时: 1.349秒
2025-06-25 11:00:42 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:52169 - "POST /v2/retrieval/search/parallel HTTP/1.1" 200
2025-06-25 11:10:06 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 11:10:06 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 11:10:06 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 11:10:06 [INFO] __main__ [main.py:188] - 启动CPU服务: 0.0.0.0:8009
2025-06-25 11:10:06 [INFO] __main__ [main.py:194] - 启动模式: development
2025-06-25 11:10:06 [INFO] __main__ [main.py:195] - 工作进程数: 1
2025-06-25 11:10:06 [INFO] __main__ [main.py:196] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-25 11:10:06 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 11:10:06 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 11:10:06 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 11:10:06 [INFO] uvicorn.error [server.py:83] - Started server process [25228]
2025-06-25 11:10:06 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-25 11:10:06 [INFO] main [main.py:86] - 初始化CPU服务...
2025-06-25 11:10:06 [INFO] main [main.py:87] - 环境: development
2025-06-25 11:10:06 [INFO] main [main.py:88] - 服务地址: http://0.0.0.0:8009
2025-06-25 11:10:06 [INFO] main [main.py:89] - GPU服务地址: None
2025-06-25 11:10:06 [INFO] main [main.py:92] - 开始预热服务组件...
2025-06-25 11:10:06 [INFO] cores.retrieval_service [retrieval_service.py:65] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-25 11:10:06 [INFO] cores.retrieval_service [retrieval_service.py:624] - 创建检索服务实例
2025-06-25 11:10:06 [INFO] main [main.py:103] - 正在预热Milvus客户端...
2025-06-25 11:10:06 [INFO] main [main.py:113] - 服务预热已启动（后台进行）
2025-06-25 11:10:06 [INFO] cores.retrieval_service [retrieval_service.py:72] - 正在初始化Milvus客户端...
2025-06-25 11:10:06 [INFO] main [main.py:118] - CPU服务初始化完成
2025-06-25 11:10:06 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:309] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-25 11:10:06 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-25 11:10:06 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:70] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-25 11:10:06 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-25 11:10:06 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:54] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-25 11:10:06 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:55] - 批处理配置: batch_size=32
2025-06-25 11:10:06 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:88] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '9%', 'memory_utilization': '25%', 'temperature': '43°C', 'power_usage': '6.4W'}}
2025-06-25 11:10:06 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:229] - GPU服务稀疏嵌入客户端初始化成功
2025-06-25 11:10:06 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:233] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-25 11:10:16 [ERROR] shared.clients.milvus_client [milvus_client.py:221] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-25 11:10:16 [WARNING] shared.clients.milvus_client [milvus_client.py:240] - Milvus连接失败，将在离线模式下运行
2025-06-25 11:10:16 [INFO] shared.clients.milvus_client [milvus_client.py:189] - Milvus客户端初始化完成: http://localhost:19530
2025-06-25 11:10:16 [INFO] shared.clients.milvus_client [milvus_client.py:724] - 创建Milvus客户端实例
2025-06-25 11:10:16 [INFO] cores.retrieval_service [retrieval_service.py:74] - Milvus客户端初始化成功
2025-06-25 11:10:16 [INFO] main [main.py:105] - Milvus客户端预热完成
2025-06-25 11:17:36 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 11:17:36 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 11:17:36 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 11:17:36 [INFO] __main__ [main.py:188] - 启动CPU服务: 0.0.0.0:8009
2025-06-25 11:17:36 [INFO] __main__ [main.py:194] - 启动模式: development
2025-06-25 11:17:36 [INFO] __main__ [main.py:195] - 工作进程数: 1
2025-06-25 11:17:36 [INFO] __main__ [main.py:196] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-25 11:17:36 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 11:17:36 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 11:17:36 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 11:17:36 [INFO] uvicorn.error [server.py:83] - Started server process [8816]
2025-06-25 11:17:36 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-25 11:17:36 [INFO] main [main.py:86] - 初始化CPU服务...
2025-06-25 11:17:36 [INFO] main [main.py:87] - 环境: development
2025-06-25 11:17:36 [INFO] main [main.py:88] - 服务地址: http://0.0.0.0:8009
2025-06-25 11:17:36 [INFO] main [main.py:89] - GPU服务地址: None
2025-06-25 11:17:36 [INFO] main [main.py:92] - 开始预热服务组件...
2025-06-25 11:17:36 [INFO] cores.retrieval_service [retrieval_service.py:65] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-25 11:17:36 [INFO] cores.retrieval_service [retrieval_service.py:624] - 创建检索服务实例
2025-06-25 11:17:36 [INFO] main [main.py:103] - 正在预热Milvus客户端...
2025-06-25 11:17:36 [INFO] main [main.py:113] - 服务预热已启动（后台进行）
2025-06-25 11:17:36 [INFO] cores.retrieval_service [retrieval_service.py:72] - 正在初始化Milvus客户端...
2025-06-25 11:17:36 [INFO] main [main.py:118] - CPU服务初始化完成
2025-06-25 11:17:36 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:309] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-25 11:17:36 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-25 11:17:36 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:70] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-25 11:17:36 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-25 11:17:36 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:54] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-25 11:17:36 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:55] - 批处理配置: batch_size=32
2025-06-25 11:17:37 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:88] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '7%', 'memory_utilization': '30%', 'temperature': '44°C', 'power_usage': '7.1W'}}
2025-06-25 11:17:37 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:229] - GPU服务稀疏嵌入客户端初始化成功
2025-06-25 11:17:37 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:233] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-25 11:17:37 [INFO] shared.clients.milvus_client [milvus_client.py:218] - 成功连接到Milvus: http://************:19530
2025-06-25 11:17:37 [INFO] shared.clients.milvus_client [milvus_client.py:189] - Milvus客户端初始化完成: http://************:19530
2025-06-25 11:17:37 [INFO] shared.clients.milvus_client [milvus_client.py:724] - 创建Milvus客户端实例
2025-06-25 11:17:37 [INFO] cores.retrieval_service [retrieval_service.py:74] - Milvus客户端初始化成功
2025-06-25 11:17:37 [INFO] main [main.py:105] - Milvus客户端预热完成
2025-06-25 11:18:11 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:53039 - "GET /docs HTTP/1.1" 200
2025-06-25 11:18:11 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:53039 - "GET /openapi.json HTTP/1.1" 200
2025-06-25 11:18:16 [INFO] cores.retrieval_service [retrieval_service.py:312] - 创建集合: department
2025-06-25 11:18:17 [INFO] shared.clients.milvus_client [milvus_client.py:342] - 密集向量索引创建成功
2025-06-25 11:18:17 [INFO] shared.clients.milvus_client [milvus_client.py:355] - 稀疏向量索引创建成功
2025-06-25 11:18:18 [INFO] shared.clients.milvus_client [milvus_client.py:321] - 集合 department 创建成功
2025-06-25 11:18:18 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-25 11:18:18 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: department, 查询: string..., 结果数: 0, 耗时: 0.310秒
2025-06-25 11:18:18 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: department, 查询: string..., 结果数: 0, 耗时: 2.066秒
2025-06-25 11:18:18 [INFO] cores.retrieval_service [retrieval_service.py:88] - 正在初始化GPU稀疏嵌入函数...
2025-06-25 11:18:18 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:309] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-25 11:18:18 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:70] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-25 11:18:18 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:54] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-25 11:18:18 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:55] - 批处理配置: batch_size=32
2025-06-25 11:18:18 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:88] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '89%', 'memory_utilization': '35%', 'temperature': '44°C', 'power_usage': '9.0W'}}
2025-06-25 11:18:18 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:229] - GPU服务稀疏嵌入客户端初始化成功
2025-06-25 11:18:18 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:233] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-25 11:18:18 [INFO] cores.retrieval_service [retrieval_service.py:90] - GPU稀疏嵌入函数初始化成功
2025-06-25 11:18:18 [INFO] cores.retrieval_service [retrieval_service.py:312] - 创建集合: guideline
2025-06-25 11:18:19 [INFO] shared.clients.milvus_client [milvus_client.py:342] - 密集向量索引创建成功
2025-06-25 11:18:19 [INFO] shared.clients.milvus_client [milvus_client.py:355] - 稀疏向量索引创建成功
2025-06-25 11:18:20 [INFO] shared.clients.milvus_client [milvus_client.py:321] - 集合 guideline 创建成功
2025-06-25 11:18:20 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-25 11:18:20 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: guideline, 查询: string..., 结果数: 0, 耗时: 0.289秒
2025-06-25 11:18:20 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: guideline, 查询: string..., 结果数: 0, 耗时: 1.809秒
2025-06-25 11:18:20 [INFO] cores.retrieval_service [retrieval_service.py:312] - 创建集合: historical
2025-06-25 11:18:20 [INFO] shared.clients.milvus_client [milvus_client.py:342] - 密集向量索引创建成功
2025-06-25 11:18:21 [INFO] shared.clients.milvus_client [milvus_client.py:355] - 稀疏向量索引创建成功
2025-06-25 11:18:22 [INFO] shared.clients.milvus_client [milvus_client.py:321] - 集合 historical 创建成功
2025-06-25 11:18:22 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-25 11:18:22 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: historical, 查询: string..., 结果数: 0, 耗时: 0.408秒
2025-06-25 11:18:22 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: historical, 查询: string..., 结果数: 0, 耗时: 2.165秒
2025-06-25 11:18:22 [INFO] cores.retrieval_service [retrieval_service.py:312] - 创建集合: delegated
2025-06-25 11:18:23 [INFO] shared.clients.milvus_client [milvus_client.py:342] - 密集向量索引创建成功
2025-06-25 11:18:23 [INFO] shared.clients.milvus_client [milvus_client.py:355] - 稀疏向量索引创建成功
2025-06-25 11:18:24 [INFO] shared.clients.milvus_client [milvus_client.py:321] - 集合 delegated 创建成功
2025-06-25 11:18:24 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-25 11:18:24 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: delegated, 查询: string..., 结果数: 0, 耗时: 0.284秒
2025-06-25 11:18:24 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: delegated, 查询: string..., 结果数: 0, 耗时: 1.820秒
2025-06-25 11:18:24 [INFO] cores.retrieval_service [retrieval_service.py:519] - 并行检索完成: 查询: string..., 总耗时: 7.876秒
2025-06-25 11:18:24 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:53039 - "POST /v2/retrieval/search/parallel HTTP/1.1" 200
2025-06-25 11:18:59 [INFO] cores.retrieval_service [retrieval_service.py:320] - 集合 department 已存在
2025-06-25 11:18:59 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-25 11:18:59 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: department, 查询: string..., 结果数: 0, 耗时: 0.082秒
2025-06-25 11:18:59 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: department, 查询: string..., 结果数: 0, 耗时: 0.097秒
2025-06-25 11:18:59 [INFO] cores.retrieval_service [retrieval_service.py:320] - 集合 guideline 已存在
2025-06-25 11:18:59 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-25 11:19:00 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: guideline, 查询: string..., 结果数: 0, 耗时: 0.080秒
2025-06-25 11:19:00 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: guideline, 查询: string..., 结果数: 0, 耗时: 0.092秒
2025-06-25 11:19:00 [INFO] cores.retrieval_service [retrieval_service.py:320] - 集合 historical 已存在
2025-06-25 11:19:00 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-25 11:19:00 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: historical, 查询: string..., 结果数: 0, 耗时: 0.160秒
2025-06-25 11:19:00 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: historical, 查询: string..., 结果数: 0, 耗时: 0.170秒
2025-06-25 11:19:00 [INFO] cores.retrieval_service [retrieval_service.py:320] - 集合 delegated 已存在
2025-06-25 11:19:00 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-25 11:19:00 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: delegated, 查询: string..., 结果数: 0, 耗时: 0.060秒
2025-06-25 11:19:00 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: delegated, 查询: string..., 结果数: 0, 耗时: 0.070秒
2025-06-25 11:19:00 [INFO] cores.retrieval_service [retrieval_service.py:519] - 并行检索完成: 查询: string..., 总耗时: 0.430秒
2025-06-25 11:19:00 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:53040 - "POST /v2/retrieval/search/parallel HTTP/1.1" 200
2025-06-25 11:19:20 [INFO] cores.retrieval_service [retrieval_service.py:320] - 集合 department 已存在
2025-06-25 11:19:21 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-25 11:19:21 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: department, 查询: 你好..., 结果数: 0, 耗时: 0.281秒
2025-06-25 11:19:21 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: department, 查询: 你好..., 结果数: 0, 耗时: 0.308秒
2025-06-25 11:19:21 [INFO] cores.retrieval_service [retrieval_service.py:320] - 集合 guideline 已存在
2025-06-25 11:19:21 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-25 11:19:21 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: guideline, 查询: 你好..., 结果数: 0, 耗时: 0.072秒
2025-06-25 11:19:21 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: guideline, 查询: 你好..., 结果数: 0, 耗时: 0.084秒
2025-06-25 11:19:21 [INFO] cores.retrieval_service [retrieval_service.py:320] - 集合 historical 已存在
2025-06-25 11:19:21 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-25 11:19:21 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: historical, 查询: 你好..., 结果数: 0, 耗时: 0.045秒
2025-06-25 11:19:21 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: historical, 查询: 你好..., 结果数: 0, 耗时: 0.058秒
2025-06-25 11:19:21 [INFO] cores.retrieval_service [retrieval_service.py:320] - 集合 delegated 已存在
2025-06-25 11:19:21 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-25 11:19:21 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: delegated, 查询: 你好..., 结果数: 0, 耗时: 0.036秒
2025-06-25 11:19:21 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: delegated, 查询: 你好..., 结果数: 0, 耗时: 0.042秒
2025-06-25 11:19:21 [INFO] cores.retrieval_service [retrieval_service.py:519] - 并行检索完成: 查询: 你好..., 总耗时: 0.492秒
2025-06-25 11:19:21 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:53141 - "POST /v2/retrieval/search/parallel HTTP/1.1" 200
2025-06-25 11:29:21 [INFO] uvicorn.error [server.py:263] - Shutting down
2025-06-25 11:29:21 [INFO] uvicorn.error [on.py:67] - Waiting for application shutdown.
2025-06-25 11:29:21 [INFO] main [main.py:125] - CPU服务关闭
2025-06-25 11:29:21 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-25 11:29:21 [INFO] uvicorn.error [server.py:93] - Finished server process [8816]
2025-06-25 11:29:31 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 11:29:31 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 11:29:31 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 11:29:31 [INFO] __main__ [main.py:188] - 启动CPU服务: 0.0.0.0:8009
2025-06-25 11:29:31 [INFO] __main__ [main.py:194] - 启动模式: development
2025-06-25 11:29:31 [INFO] __main__ [main.py:195] - 工作进程数: 1
2025-06-25 11:29:31 [INFO] __main__ [main.py:196] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-25 11:29:31 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-25 11:29:31 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-25.log
2025-06-25 11:29:31 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-25.log
2025-06-25 11:29:31 [INFO] uvicorn.error [server.py:83] - Started server process [10668]
2025-06-25 11:29:31 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-25 11:29:31 [INFO] main [main.py:86] - 初始化CPU服务...
2025-06-25 11:29:31 [INFO] main [main.py:87] - 环境: development
2025-06-25 11:29:31 [INFO] main [main.py:88] - 服务地址: http://0.0.0.0:8009
2025-06-25 11:29:31 [INFO] main [main.py:89] - GPU服务地址: None
2025-06-25 11:29:31 [INFO] main [main.py:92] - 开始预热服务组件...
2025-06-25 11:29:31 [INFO] cores.retrieval_service [retrieval_service.py:65] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-25 11:29:31 [INFO] cores.retrieval_service [retrieval_service.py:624] - 创建检索服务实例
2025-06-25 11:29:31 [INFO] main [main.py:103] - 正在预热Milvus客户端...
2025-06-25 11:29:31 [INFO] main [main.py:113] - 服务预热已启动（后台进行）
2025-06-25 11:29:31 [INFO] cores.retrieval_service [retrieval_service.py:72] - 正在初始化Milvus客户端...
2025-06-25 11:29:31 [INFO] main [main.py:118] - CPU服务初始化完成
2025-06-25 11:29:31 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:309] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-25 11:29:31 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-25 11:29:31 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:70] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-25 11:29:31 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-25 11:29:31 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:54] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-25 11:29:31 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:55] - 批处理配置: batch_size=32
2025-06-25 11:29:31 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:88] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '0%', 'memory_utilization': '22%', 'temperature': '42°C', 'power_usage': '6.0W'}}
2025-06-25 11:29:31 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:229] - GPU服务稀疏嵌入客户端初始化成功
2025-06-25 11:29:31 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:233] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-25 11:29:31 [INFO] shared.clients.milvus_client [milvus_client.py:218] - 成功连接到Milvus: http://************:19530
2025-06-25 11:29:31 [INFO] shared.clients.milvus_client [milvus_client.py:189] - Milvus客户端初始化完成: http://************:19530
2025-06-25 11:29:31 [INFO] shared.clients.milvus_client [milvus_client.py:724] - 创建Milvus客户端实例
2025-06-25 11:29:31 [INFO] cores.retrieval_service [retrieval_service.py:74] - Milvus客户端初始化成功
2025-06-25 11:29:31 [INFO] main [main.py:105] - Milvus客户端预热完成
