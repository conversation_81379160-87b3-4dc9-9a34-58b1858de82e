2025-06-03 07:46:06 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-03 07:46:06 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-03.log
2025-06-03 07:46:06 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-03.log
2025-06-03 07:46:06 [INFO] __main__ [main.py:140] - 启动CPU服务: localhost:8009
2025-06-03 07:46:09 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-03 07:46:09 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-03.log
2025-06-03 07:46:09 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-03.log
2025-06-03 07:46:09 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-03 07:46:09 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-03.log
2025-06-03 07:46:09 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-03.log
2025-06-03 07:46:09 [INFO] uvicorn.error [server.py:76] - Started server process [4072]
2025-06-03 07:46:09 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-03 07:46:09 [INFO] main [main.py:80] - 初始化CPU服务...
2025-06-03 07:46:09 [INFO] main [main.py:85] - CPU服务初始化完成
2025-06-03 07:46:09 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-03 07:46:11 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:61505 - "GET /health HTTP/1.1" 200
2025-06-03 07:46:13 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:61507 - "GET / HTTP/1.1" 200
2025-06-03 07:46:15 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:61512 - "GET /health HTTP/1.1" 200
2025-06-03 07:46:17 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:61514 - "GET /v2/ HTTP/1.1" 200
2025-06-03 07:46:19 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:65] - 使用默认GPU服务URL: http://gpu-service:8001
2025-06-03 07:46:19 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-03 07:46:22 [WARNING] shared.clients.gpu_service_client [gpu_service_client.py:84] - GPU服务健康检查异常: HTTPConnectionPool(host='gpu-service', port=8001): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002636C3574D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-06-03 07:46:22 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:61516 - "GET /v2/status HTTP/1.1" 200
2025-06-03 07:46:24 [INFO] cores.indexing_service [indexing_service.py:55] - 优化版索引服务初始化完成（GPU+CPU分离架构）
2025-06-03 07:46:24 [INFO] cores.indexing_service [indexing_service.py:407] - 创建索引服务实例
2025-06-03 07:46:24 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:61519 - "GET /v2/indexing/config HTTP/1.1" 200
2025-06-03 07:46:26 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:61521 - "GET /v2/indexing/status HTTP/1.1" 200
2025-06-03 07:46:28 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-03 07:46:28 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:65] - 使用默认GPU服务URL: http://gpu-service:8001
2025-06-03 07:46:28 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-03 07:46:31 [WARNING] shared.clients.gpu_service_client [gpu_service_client.py:84] - GPU服务健康检查异常: HTTPConnectionPool(host='gpu-service', port=8001): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002636CB103D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-06-03 07:46:31 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:217] - GPU服务稀疏嵌入客户端初始化成功
2025-06-03 07:46:31 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:221] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-03 07:46:31 [INFO] cores.indexing_service [indexing_service.py:72] - 为 test_collection 启用生产环境稀疏嵌入函数（GPU服务优先）
2025-06-03 07:46:41 [ERROR] cores.indexing_service [indexing_service.py:114] - 混合索引 test_collection 创建失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-03 07:46:41 [ERROR] cores.indexing_service [indexing_service.py:167] - 索引构建失败: test_collection, 错误: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>, 耗时: 12.863秒
2025-06-03 07:46:41 [ERROR] cores.indexing_service [indexing_service.py:388] - 索引功能测试失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-03 07:46:41 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:61524 - "POST /v2/indexing/test HTTP/1.1" 200
2025-06-03 07:46:43 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-03 07:46:43 [INFO] cores.retrieval_service [retrieval_service.py:383] - 创建检索服务实例
2025-06-03 07:46:43 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-03 07:47:38 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-03 07:47:38 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:65] - 使用默认GPU服务URL: http://gpu-service:8001
2025-06-03 07:47:38 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-03 09:47:48 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-03 09:47:48 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-03.log
2025-06-03 09:47:48 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-03.log
2025-06-03 09:47:48 [INFO] __main__ [main.py:167] - 启动CPU服务: 0.0.0.0:8009
2025-06-03 09:47:50 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-03 09:47:50 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-03.log
2025-06-03 09:47:50 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-03.log
2025-06-03 09:47:50 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-03 09:47:50 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-03.log
2025-06-03 09:47:50 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-03.log
2025-06-03 09:47:50 [INFO] uvicorn.error [server.py:83] - Started server process [45368]
2025-06-03 09:47:50 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-03 09:47:50 [INFO] main [main.py:80] - 🚀 初始化CPU服务...
2025-06-03 09:47:50 [INFO] main [main.py:81] - 环境: development
2025-06-03 09:47:50 [INFO] main [main.py:82] - 服务地址: http://0.0.0.0:8009
2025-06-03 09:47:50 [INFO] main [main.py:83] - GPU服务地址: None
2025-06-03 09:47:50 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:47:51 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:47:51 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:47:51 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:47:52 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:47:52 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:47:52 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:47:53 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:47:53 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:47:53 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:47:54 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:47:54 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:47:55 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:47:55 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:47:55 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:47:56 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:47:56 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:47:56 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:47:57 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:47:57 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:47:58 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:48:12 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:48:13 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:48:27 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:48:28 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:48:35 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:48:36 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:48:38 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:48:40 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:48:53 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:48:54 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:48:55 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:48:57 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 09:49:05 [INFO] main [main.py:86] - 🔥 开始预热服务组件...
2025-06-03 09:49:05 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-03 09:49:05 [INFO] cores.retrieval_service [retrieval_service.py:383] - 创建检索服务实例
2025-06-03 09:49:05 [INFO] main [main.py:97] - 正在预热Milvus客户端...
2025-06-03 09:49:05 [INFO] main [main.py:107] - ✅ 服务预热已启动（后台进行）
2025-06-03 09:49:05 [INFO] main [main.py:112] - ✅ CPU服务初始化完成
2025-06-03 09:49:05 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-03 09:49:05 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-03 09:49:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-03 09:49:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:65] - 使用默认GPU服务URL: http://gpu-service:8001
2025-06-03 09:49:05 [INFO] main [main.py:119] - CPU服务关闭
2025-06-03 09:49:05 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-03 09:49:05 [ERROR] uvicorn.error [on.py:134] - Traceback (most recent call last):
  File "D:\ProgramFiles\python311\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ProgramFiles\python311\Lib\asyncio\base_events.py", line 654, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\ProgramFiles\python311\Lib\asyncio\runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "D:\ProgramFiles\python311\Lib\asyncio\runners.py", line 123, in run
    raise KeyboardInterrupt()
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Project\Backend\ai\ai-jiesujiban-v3\.venv\Lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "D:\Project\Backend\ai\ai-jiesujiban-v3\.venv\Lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ProgramFiles\python311\Lib\asyncio\queues.py", line 158, in get
    await getter
asyncio.exceptions.CancelledError

2025-06-03 14:09:33 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-03 14:09:33 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-03.log
2025-06-03 14:09:33 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-03.log
2025-06-03 14:09:33 [INFO] __main__ [main.py:181] - 启动CPU服务: 0.0.0.0:8009
2025-06-03 14:09:35 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-03 14:09:35 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-03.log
2025-06-03 14:09:35 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-03.log
2025-06-03 14:09:35 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-03 14:09:35 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-03.log
2025-06-03 14:09:35 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-03.log
2025-06-03 14:09:35 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:35 [INFO] uvicorn.error [server.py:83] - Started server process [46532]
2025-06-03 14:09:35 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-03 14:09:35 [INFO] main [main.py:80] - 🚀 初始化CPU服务...
2025-06-03 14:09:35 [INFO] main [main.py:81] - 环境: development
2025-06-03 14:09:35 [INFO] main [main.py:82] - 服务地址: http://0.0.0.0:8009
2025-06-03 14:09:35 [INFO] main [main.py:83] - GPU服务地址: None
2025-06-03 14:09:36 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:36 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:36 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:37 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:37 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:37 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:38 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:38 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:38 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:39 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:39 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:40 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:40 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:40 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:41 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:41 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:41 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:42 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:42 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:42 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:43 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:43 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:43 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:44 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:44 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:45 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:45 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:45 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:46 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:46 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:46 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:47 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:47 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:47 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:48 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:48 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:49 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:49 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:49 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:50 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:50 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:50 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:51 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:51 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:52 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:55 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:57 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:09:59 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:10:01 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:10:09 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:10:10 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:10:10 [INFO] main [main.py:86] - 🔥 开始预热服务组件...
2025-06-03 14:10:10 [INFO] cores.retrieval_service [retrieval_service.py:66] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-03 14:10:10 [INFO] cores.retrieval_service [retrieval_service.py:586] - 创建检索服务实例
2025-06-03 14:10:10 [INFO] main [main.py:97] - 正在预热Milvus客户端...
2025-06-03 14:10:11 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:10:11 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:10:12 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:10:12 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:10:12 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:10:13 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:10:13 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:10:14 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:10:40 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:10:41 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 14:10:49 [INFO] watchfiles.main [main.py:308] - 1 change detected
2025-06-03 15:00:32 [INFO] main [main.py:107] - ✅ 服务预热已启动（后台进行）
2025-06-03 15:00:32 [INFO] cores.retrieval_service [retrieval_service.py:73] - 正在初始化Milvus客户端...
2025-06-03 15:00:32 [INFO] main [main.py:112] - ✅ CPU服务初始化完成
2025-06-03 15:00:32 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:297] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-03 15:00:32 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-03 15:00:32 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:65] - 使用默认GPU服务URL: http://gpu-service:8001
2025-06-03 15:00:32 [INFO] main [main.py:119] - CPU服务关闭
2025-06-03 15:00:32 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:53] - 初始化GPU服务稀疏嵌入客户端: http://gpu-service:8001
2025-06-03 15:00:32 [ERROR] uvicorn.error [on.py:134] - Traceback (most recent call last):
  File "D:\ProgramFiles\python311\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ProgramFiles\python311\Lib\asyncio\base_events.py", line 654, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\ProgramFiles\python311\Lib\asyncio\runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "D:\ProgramFiles\python311\Lib\asyncio\runners.py", line 123, in run
    raise KeyboardInterrupt()
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Project\Backend\ai\ai-jiesujiban-v3\.venv\Lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "D:\Project\Backend\ai\ai-jiesujiban-v3\.venv\Lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ProgramFiles\python311\Lib\asyncio\queues.py", line 158, in get
    await getter
asyncio.exceptions.CancelledError

