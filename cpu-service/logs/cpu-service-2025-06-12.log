2025-06-12 15:03:35 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 15:03:35 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 15:03:35 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 15:03:35 [INFO] __main__ [main.py:184] - 启动CPU服务: 0.0.0.0:8009
2025-06-12 15:03:35 [INFO] __main__ [main.py:190] - 启动模式: development
2025-06-12 15:03:35 [INFO] __main__ [main.py:191] - 工作进程数: 1
2025-06-12 15:03:35 [INFO] __main__ [main.py:192] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-12 15:03:35 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 15:03:35 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 15:03:35 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 15:03:35 [INFO] uvicorn.error [server.py:76] - Started server process [15408]
2025-06-12 15:03:35 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-12 15:03:35 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-12 15:03:35 [INFO] main [main.py:83] - 环境: development
2025-06-12 15:03:35 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8009
2025-06-12 15:03:35 [INFO] main [main.py:85] - GPU服务地址: None
2025-06-12 15:03:35 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-12 15:03:35 [INFO] cores.retrieval_service [retrieval_service.py:69] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-12 15:03:35 [INFO] cores.retrieval_service [retrieval_service.py:628] - 创建检索服务实例
2025-06-12 15:03:35 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-12 15:03:35 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-12 15:03:35 [INFO] cores.retrieval_service [retrieval_service.py:76] - 正在初始化Milvus客户端...
2025-06-12 15:03:35 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-12 15:03:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 15:03:35 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-12 15:03:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 15:03:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 15:03:35 [INFO] uvicorn.error [server.py:218] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-12 15:03:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '30%', 'memory_utilization': '31%', 'temperature': '45°C', 'power_usage': '7.1W'}}
2025-06-12 15:03:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 15:03:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 15:03:45 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-12 15:03:45 [WARNING] shared.clients.milvus_client [milvus_client.py:229] - 尝试连接本地Milvus作为回退...
2025-06-12 15:03:45 [INFO] shared.clients.milvus_client [milvus_client.py:237] - 成功连接到本地Milvus
2025-06-12 15:03:45 [INFO] shared.clients.milvus_client [milvus_client.py:193] - Milvus客户端初始化完成: http://localhost:19530
2025-06-12 15:03:45 [INFO] shared.clients.milvus_client [milvus_client.py:728] - 创建Milvus客户端实例
2025-06-12 15:03:45 [INFO] cores.retrieval_service [retrieval_service.py:78] - Milvus客户端初始化成功
2025-06-12 15:03:45 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-12 15:04:07 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-12 15:04:07 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-12 15:04:07 [INFO] main [main.py:121] - CPU服务关闭
2025-06-12 15:04:07 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-12 15:04:07 [INFO] uvicorn.error [server.py:86] - Finished server process [15408]
2025-06-12 15:14:41 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 15:14:41 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 15:14:41 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 15:14:41 [INFO] __main__ [main.py:184] - 启动CPU服务: 0.0.0.0:8009
2025-06-12 15:14:41 [INFO] __main__ [main.py:190] - 启动模式: development
2025-06-12 15:14:41 [INFO] __main__ [main.py:191] - 工作进程数: 1
2025-06-12 15:14:41 [INFO] __main__ [main.py:192] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-12 15:14:41 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 15:14:41 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 15:14:41 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 15:14:41 [INFO] uvicorn.error [server.py:76] - Started server process [32592]
2025-06-12 15:14:41 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-12 15:14:41 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-12 15:14:41 [INFO] main [main.py:83] - 环境: development
2025-06-12 15:14:41 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8009
2025-06-12 15:14:41 [INFO] main [main.py:85] - GPU服务地址: None
2025-06-12 15:14:41 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-12 15:14:41 [INFO] cores.retrieval_service [retrieval_service.py:69] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-12 15:14:41 [INFO] cores.retrieval_service [retrieval_service.py:628] - 创建检索服务实例
2025-06-12 15:14:41 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-12 15:14:41 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-12 15:14:41 [INFO] cores.retrieval_service [retrieval_service.py:76] - 正在初始化Milvus客户端...
2025-06-12 15:14:41 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-12 15:14:41 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 15:14:41 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-12 15:14:41 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 15:14:41 [INFO] uvicorn.error [server.py:218] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-12 15:14:41 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 15:14:41 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '37%', 'memory_utilization': '30%', 'temperature': '47°C', 'power_usage': '6.9W'}}
2025-06-12 15:14:41 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 15:14:41 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 15:14:51 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-12 15:14:51 [WARNING] shared.clients.milvus_client [milvus_client.py:229] - 尝试连接本地Milvus作为回退...
2025-06-12 15:14:51 [INFO] shared.clients.milvus_client [milvus_client.py:237] - 成功连接到本地Milvus
2025-06-12 15:14:51 [INFO] shared.clients.milvus_client [milvus_client.py:193] - Milvus客户端初始化完成: http://localhost:19530
2025-06-12 15:14:51 [INFO] shared.clients.milvus_client [milvus_client.py:728] - 创建Milvus客户端实例
2025-06-12 15:14:51 [INFO] cores.retrieval_service [retrieval_service.py:78] - Milvus客户端初始化成功
2025-06-12 15:14:51 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-12 15:15:07 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57033 - "GET /health HTTP/1.1" 200
2025-06-12 15:19:15 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57663 - "GET /health HTTP/1.1" 200
2025-06-12 15:19:45 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57684 - "GET /docs HTTP/1.1" 200
2025-06-12 15:19:57 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:57699 - "GET /v2/collections HTTP/1.1" 404
2025-06-12 16:33:52 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-12 16:33:52 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-12 16:33:52 [INFO] main [main.py:121] - CPU服务关闭
2025-06-12 16:33:52 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-12 16:33:52 [INFO] uvicorn.error [server.py:86] - Finished server process [32592]
2025-06-12 16:35:07 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 16:35:07 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 16:35:07 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 16:35:07 [INFO] __main__ [main.py:184] - 启动CPU服务: 0.0.0.0:8009
2025-06-12 16:35:07 [INFO] __main__ [main.py:190] - 启动模式: development
2025-06-12 16:35:07 [INFO] __main__ [main.py:191] - 工作进程数: 1
2025-06-12 16:35:07 [INFO] __main__ [main.py:192] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-12 16:35:07 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 16:35:07 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 16:35:07 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 16:35:07 [INFO] uvicorn.error [server.py:76] - Started server process [32552]
2025-06-12 16:35:07 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-12 16:35:07 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-12 16:35:07 [INFO] main [main.py:83] - 环境: development
2025-06-12 16:35:07 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8009
2025-06-12 16:35:07 [INFO] main [main.py:85] - GPU服务地址: None
2025-06-12 16:35:07 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-12 16:35:07 [INFO] cores.retrieval_service [retrieval_service.py:69] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-12 16:35:07 [INFO] cores.retrieval_service [retrieval_service.py:628] - 创建检索服务实例
2025-06-12 16:35:07 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-12 16:35:07 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-12 16:35:07 [INFO] cores.retrieval_service [retrieval_service.py:76] - 正在初始化Milvus客户端...
2025-06-12 16:35:07 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-12 16:35:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 16:35:07 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-12 16:35:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 16:35:07 [INFO] uvicorn.error [server.py:218] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-12 16:35:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 16:35:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '3%', 'memory_utilization': '30%', 'temperature': '40°C', 'power_usage': '5.8W'}}
2025-06-12 16:35:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 16:35:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 16:35:17 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-12 16:35:17 [WARNING] shared.clients.milvus_client [milvus_client.py:229] - 尝试连接本地Milvus作为回退...
2025-06-12 16:35:17 [INFO] shared.clients.milvus_client [milvus_client.py:237] - 成功连接到本地Milvus
2025-06-12 16:35:17 [INFO] shared.clients.milvus_client [milvus_client.py:193] - Milvus客户端初始化完成: http://localhost:19530
2025-06-12 16:35:17 [INFO] shared.clients.milvus_client [milvus_client.py:728] - 创建Milvus客户端实例
2025-06-12 16:35:17 [INFO] cores.retrieval_service [retrieval_service.py:78] - Milvus客户端初始化成功
2025-06-12 16:35:17 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-12 16:35:20 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59005 - "GET /docs HTTP/1.1" 200
2025-06-12 16:35:21 [INFO] uvicorn.access [httptools_impl.py:496] - 127.0.0.1:59005 - "GET /openapi.json HTTP/1.1" 200
2025-06-12 17:34:03 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-12 17:34:03 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-12 17:34:03 [INFO] main [main.py:121] - CPU服务关闭
2025-06-12 17:34:03 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-12 17:34:03 [INFO] uvicorn.error [server.py:86] - Finished server process [32552]
2025-06-12 17:34:18 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 17:34:18 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 17:34:18 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 17:34:18 [INFO] __main__ [main.py:184] - 启动CPU服务: 0.0.0.0:8009
2025-06-12 17:34:18 [INFO] __main__ [main.py:190] - 启动模式: development
2025-06-12 17:34:18 [INFO] __main__ [main.py:191] - 工作进程数: 1
2025-06-12 17:34:18 [INFO] __main__ [main.py:192] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-12 17:34:18 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 17:34:18 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 17:34:18 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 17:34:18 [INFO] uvicorn.error [server.py:83] - Started server process [30180]
2025-06-12 17:34:18 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-12 17:34:18 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-12 17:34:18 [INFO] main [main.py:83] - 环境: development
2025-06-12 17:34:18 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8009
2025-06-12 17:34:18 [INFO] main [main.py:85] - GPU服务地址: None
2025-06-12 17:34:18 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-12 17:34:18 [INFO] cores.retrieval_service [retrieval_service.py:69] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-12 17:34:18 [INFO] cores.retrieval_service [retrieval_service.py:628] - 创建检索服务实例
2025-06-12 17:34:18 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-12 17:34:18 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-12 17:34:18 [INFO] cores.retrieval_service [retrieval_service.py:76] - 正在初始化Milvus客户端...
2025-06-12 17:34:18 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-12 17:34:18 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 17:34:18 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-12 17:34:18 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 17:34:18 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 17:34:18 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-12 17:34:18 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '18%', 'memory_utilization': '36%', 'temperature': '50°C', 'power_usage': '7.4W'}}
2025-06-12 17:34:18 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 17:34:18 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 17:34:28 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-12 17:34:28 [WARNING] shared.clients.milvus_client [milvus_client.py:229] - 尝试连接本地Milvus作为回退...
2025-06-12 17:34:29 [INFO] shared.clients.milvus_client [milvus_client.py:237] - 成功连接到本地Milvus
2025-06-12 17:34:29 [INFO] shared.clients.milvus_client [milvus_client.py:193] - Milvus客户端初始化完成: http://localhost:19530
2025-06-12 17:34:29 [INFO] shared.clients.milvus_client [milvus_client.py:728] - 创建Milvus客户端实例
2025-06-12 17:34:29 [INFO] cores.retrieval_service [retrieval_service.py:78] - Milvus客户端初始化成功
2025-06-12 17:34:29 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-12 18:45:35 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 18:45:35 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 18:45:35 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 18:45:35 [INFO] __main__ [main.py:184] - 启动CPU服务: 0.0.0.0:8009
2025-06-12 18:45:35 [INFO] __main__ [main.py:190] - 启动模式: development
2025-06-12 18:45:35 [INFO] __main__ [main.py:191] - 工作进程数: 1
2025-06-12 18:45:35 [INFO] __main__ [main.py:192] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-12 18:45:35 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 18:45:35 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 18:45:35 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 18:45:35 [INFO] uvicorn.error [server.py:83] - Started server process [10740]
2025-06-12 18:45:35 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-12 18:45:35 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-12 18:45:35 [INFO] main [main.py:83] - 环境: development
2025-06-12 18:45:35 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8009
2025-06-12 18:45:35 [INFO] main [main.py:85] - GPU服务地址: None
2025-06-12 18:45:35 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-12 18:45:35 [INFO] cores.retrieval_service [retrieval_service.py:69] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-12 18:45:35 [INFO] cores.retrieval_service [retrieval_service.py:628] - 创建检索服务实例
2025-06-12 18:45:35 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-12 18:45:35 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-12 18:45:35 [INFO] cores.retrieval_service [retrieval_service.py:76] - 正在初始化Milvus客户端...
2025-06-12 18:45:35 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-12 18:45:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 18:45:35 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-12 18:45:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 18:45:35 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-12 18:45:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 18:45:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '1%', 'memory_utilization': '31%', 'temperature': '46°C', 'power_usage': '6.5W'}}
2025-06-12 18:45:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 18:45:35 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 18:45:45 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-12 18:45:45 [WARNING] shared.clients.milvus_client [milvus_client.py:229] - 尝试连接本地Milvus作为回退...
2025-06-12 18:45:55 [WARNING] shared.clients.milvus_client [milvus_client.py:241] - 本地Milvus连接也失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-12 18:45:55 [WARNING] shared.clients.milvus_client [milvus_client.py:244] - Milvus连接失败，将在离线模式下运行
2025-06-12 18:45:55 [INFO] shared.clients.milvus_client [milvus_client.py:193] - Milvus客户端初始化完成: http://************:19530
2025-06-12 18:45:55 [INFO] shared.clients.milvus_client [milvus_client.py:728] - 创建Milvus客户端实例
2025-06-12 18:45:55 [INFO] cores.retrieval_service [retrieval_service.py:78] - Milvus客户端初始化成功
2025-06-12 18:45:55 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-12 18:46:56 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:63142 - "GET /docs HTTP/1.1" 200
2025-06-12 18:46:58 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:63142 - "GET /openapi.json HTTP/1.1" 200
2025-06-12 18:49:09 [INFO] cores.indexing_service [indexing_service.py:58] - 优化版索引服务初始化完成（GPU+CPU分离架构）
2025-06-12 18:49:09 [INFO] cores.indexing_service [indexing_service.py:573] - 创建索引服务实例
2025-06-12 18:49:09 [INFO] cores.indexing_service [indexing_service.py:275] - 异步索引追加构建任务启动: department, 任务ID: affd1ff0-af62-4db2-bb6b-53cd411d52a3
2025-06-12 18:49:09 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:63354 - "POST /v2/indexing/build HTTP/1.1" 200
2025-06-12 18:49:10 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 18:49:10 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 18:49:10 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 18:49:10 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '9%', 'memory_utilization': '2%', 'temperature': '47°C', 'power_usage': '17.8W'}}
2025-06-12 18:49:10 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 18:49:10 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 18:49:10 [INFO] cores.indexing_service [indexing_service.py:76] - 为 department 启用生产环境稀疏嵌入函数（GPU服务优先）
2025-06-12 18:49:10 [INFO] cores.indexing_service [indexing_service.py:80] - 创建MilvusVectorStore: department, 操作: 连接到现有
2025-06-12 18:49:10 [INFO] shared.clients.embedding_client [embedding_client.py:64] - 初始化自定义API嵌入模型: BAAI/bge-m3
2025-06-12 18:49:10 [INFO] cores.indexing_service [indexing_service.py:173] - 混合索引 department 连接成功（稀疏嵌入: True）
2025-06-12 18:49:10 [ERROR] shared.clients.embedding_client [embedding_client.py:168] - 密集嵌入获取异常: name 'settings' is not defined
2025-06-12 18:49:10 [ERROR] shared.clients.embedding_client [embedding_client.py:109] - 批量嵌入获取失败: name 'settings' is not defined
2025-06-12 18:50:30 [ERROR] shared.clients.embedding_client [embedding_client.py:168] - 密集嵌入获取异常: name 'settings' is not defined
2025-06-12 18:50:30 [ERROR] shared.clients.embedding_client [embedding_client.py:109] - 批量嵌入获取失败: name 'settings' is not defined
2025-06-12 18:50:51 [INFO] cores.indexing_service [indexing_service.py:218] - 索引追加构建完成: department, 文档数: 2575, 节点数: 2579, 耗时: 102.034秒
2025-06-12 18:50:51 [INFO] cores.indexing_service [indexing_service.py:308] - 异步索引追加构建任务完成: affd1ff0-af62-4db2-bb6b-53cd411d52a3
2025-06-12 18:52:46 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 18:52:46 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 18:52:46 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 18:52:46 [INFO] __main__ [main.py:184] - 启动CPU服务: 0.0.0.0:8009
2025-06-12 18:52:46 [INFO] __main__ [main.py:190] - 启动模式: development
2025-06-12 18:52:46 [INFO] __main__ [main.py:191] - 工作进程数: 1
2025-06-12 18:52:46 [INFO] __main__ [main.py:192] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-12 18:52:46 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 18:52:46 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 18:52:46 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 18:52:46 [INFO] uvicorn.error [server.py:76] - Started server process [29372]
2025-06-12 18:52:46 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-12 18:52:46 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-12 18:52:46 [INFO] main [main.py:83] - 环境: development
2025-06-12 18:52:46 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8009
2025-06-12 18:52:46 [INFO] main [main.py:85] - GPU服务地址: None
2025-06-12 18:52:46 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-12 18:52:46 [INFO] cores.retrieval_service [retrieval_service.py:69] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-12 18:52:46 [INFO] cores.retrieval_service [retrieval_service.py:628] - 创建检索服务实例
2025-06-12 18:52:46 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-12 18:52:46 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-12 18:52:46 [INFO] cores.retrieval_service [retrieval_service.py:76] - 正在初始化Milvus客户端...
2025-06-12 18:52:46 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-12 18:52:46 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 18:52:46 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-12 18:52:46 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 18:52:46 [ERROR] uvicorn.error [server.py:168] - [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8009): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-12 18:52:46 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 18:52:46 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-12 18:52:46 [INFO] main [main.py:121] - CPU服务关闭
2025-06-12 18:52:46 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-12 18:53:30 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 18:53:30 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 18:53:30 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 18:53:30 [INFO] __main__ [main.py:184] - 启动CPU服务: 0.0.0.0:8009
2025-06-12 18:53:30 [INFO] __main__ [main.py:190] - 启动模式: development
2025-06-12 18:53:30 [INFO] __main__ [main.py:191] - 工作进程数: 1
2025-06-12 18:53:30 [INFO] __main__ [main.py:192] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-12 18:53:30 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 18:53:30 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 18:53:30 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 18:53:30 [INFO] uvicorn.error [server.py:76] - Started server process [26100]
2025-06-12 18:53:30 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-12 18:53:30 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-12 18:53:30 [INFO] main [main.py:83] - 环境: development
2025-06-12 18:53:30 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8009
2025-06-12 18:53:30 [INFO] main [main.py:85] - GPU服务地址: None
2025-06-12 18:53:30 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-12 18:53:30 [INFO] cores.retrieval_service [retrieval_service.py:69] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-12 18:53:30 [INFO] cores.retrieval_service [retrieval_service.py:628] - 创建检索服务实例
2025-06-12 18:53:30 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-12 18:53:30 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-12 18:53:30 [INFO] cores.retrieval_service [retrieval_service.py:76] - 正在初始化Milvus客户端...
2025-06-12 18:53:30 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-12 18:53:30 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 18:53:30 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-12 18:53:30 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 18:53:30 [INFO] uvicorn.error [server.py:218] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-12 18:53:30 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 18:53:30 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '35%', 'memory_utilization': '30%', 'temperature': '50°C', 'power_usage': '7.5W'}}
2025-06-12 18:53:30 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 18:53:30 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 18:53:30 [INFO] shared.clients.milvus_client [milvus_client.py:222] - 成功连接到Milvus: http://************:19530
2025-06-12 18:53:30 [INFO] shared.clients.milvus_client [milvus_client.py:193] - Milvus客户端初始化完成: http://************:19530
2025-06-12 18:53:30 [INFO] shared.clients.milvus_client [milvus_client.py:728] - 创建Milvus客户端实例
2025-06-12 18:53:30 [INFO] cores.retrieval_service [retrieval_service.py:78] - Milvus客户端初始化成功
2025-06-12 18:53:30 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-12 18:53:54 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:49861 - "GET /health HTTP/1.1" 200
2025-06-12 18:54:52 [INFO] cores.indexing_service [indexing_service.py:58] - 优化版索引服务初始化完成（GPU+CPU分离架构）
2025-06-12 18:54:52 [INFO] cores.indexing_service [indexing_service.py:573] - 创建索引服务实例
2025-06-12 18:54:52 [INFO] cores.indexing_service [indexing_service.py:275] - 异步索引追加构建任务启动: department, 任务ID: 386cf355-5d09-4c91-9d1d-a2dca2d1eb59
2025-06-12 18:54:52 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:49911 - "POST /v2/indexing/build HTTP/1.1" 200
2025-06-12 18:54:54 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 18:54:54 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 18:54:54 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 18:54:54 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '31%', 'memory_utilization': '16%', 'temperature': '48°C', 'power_usage': '9.5W'}}
2025-06-12 18:54:54 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 18:54:54 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 18:54:54 [INFO] cores.indexing_service [indexing_service.py:76] - 为 department 启用生产环境稀疏嵌入函数（GPU服务优先）
2025-06-12 18:54:54 [INFO] cores.indexing_service [indexing_service.py:80] - 创建MilvusVectorStore: department, 操作: 连接到现有
2025-06-12 18:54:54 [INFO] shared.clients.embedding_client [embedding_client.py:64] - 初始化自定义API嵌入模型: BAAI/bge-m3
2025-06-12 18:54:55 [INFO] cores.indexing_service [indexing_service.py:173] - 混合索引 department 连接成功（稀疏嵌入: True）
2025-06-12 18:54:56 [ERROR] shared.clients.embedding_client [embedding_client.py:167] - 密集嵌入API请求失败: 413, {"code":20042,"message":"input batch size 2048 \u003e maximum allowed batch size 64","data":null}
2025-06-12 18:56:18 [ERROR] shared.clients.embedding_client [embedding_client.py:167] - 密集嵌入API请求失败: 413, {"code":20042,"message":"input batch size 531 \u003e maximum allowed batch size 64","data":null}
2025-06-12 18:56:40 [INFO] cores.indexing_service [indexing_service.py:218] - 索引追加构建完成: department, 文档数: 2575, 节点数: 2579, 耗时: 107.524秒
2025-06-12 18:56:40 [INFO] cores.indexing_service [indexing_service.py:308] - 异步索引追加构建任务完成: 386cf355-5d09-4c91-9d1d-a2dca2d1eb59
2025-06-12 18:57:44 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-12 18:57:44 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-12 18:57:44 [INFO] main [main.py:121] - CPU服务关闭
2025-06-12 18:57:44 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-12 18:57:44 [INFO] uvicorn.error [server.py:86] - Finished server process [26100]
2025-06-12 18:57:59 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 18:57:59 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 18:57:59 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 18:57:59 [INFO] __main__ [main.py:184] - 启动CPU服务: 0.0.0.0:8009
2025-06-12 18:57:59 [INFO] __main__ [main.py:190] - 启动模式: development
2025-06-12 18:57:59 [INFO] __main__ [main.py:191] - 工作进程数: 1
2025-06-12 18:57:59 [INFO] __main__ [main.py:192] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-12 18:57:59 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 18:57:59 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 18:57:59 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 18:57:59 [INFO] uvicorn.error [server.py:76] - Started server process [16760]
2025-06-12 18:57:59 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-12 18:57:59 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-12 18:57:59 [INFO] main [main.py:83] - 环境: development
2025-06-12 18:57:59 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8009
2025-06-12 18:57:59 [INFO] main [main.py:85] - GPU服务地址: None
2025-06-12 18:57:59 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-12 18:57:59 [INFO] cores.retrieval_service [retrieval_service.py:69] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-12 18:57:59 [INFO] cores.retrieval_service [retrieval_service.py:628] - 创建检索服务实例
2025-06-12 18:57:59 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-12 18:57:59 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-12 18:57:59 [INFO] cores.retrieval_service [retrieval_service.py:76] - 正在初始化Milvus客户端...
2025-06-12 18:57:59 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-12 18:57:59 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 18:57:59 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-12 18:57:59 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 18:57:59 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 18:57:59 [INFO] uvicorn.error [server.py:218] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-12 18:57:59 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '33%', 'memory_utilization': '23%', 'temperature': '51°C', 'power_usage': '7.0W'}}
2025-06-12 18:57:59 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 18:57:59 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 18:57:59 [INFO] shared.clients.milvus_client [milvus_client.py:222] - 成功连接到Milvus: http://************:19530
2025-06-12 18:57:59 [INFO] shared.clients.milvus_client [milvus_client.py:193] - Milvus客户端初始化完成: http://************:19530
2025-06-12 18:57:59 [INFO] shared.clients.milvus_client [milvus_client.py:728] - 创建Milvus客户端实例
2025-06-12 18:57:59 [INFO] cores.retrieval_service [retrieval_service.py:78] - Milvus客户端初始化成功
2025-06-12 18:57:59 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-12 18:58:20 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:52645 - "GET /health HTTP/1.1" 200
2025-06-12 18:59:30 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:52710 - "POST /v2/indexing/build HTTP/1.1" 422
2025-06-12 18:59:56 [INFO] cores.indexing_service [indexing_service.py:58] - 优化版索引服务初始化完成（GPU+CPU分离架构）
2025-06-12 18:59:56 [INFO] cores.indexing_service [indexing_service.py:573] - 创建索引服务实例
2025-06-12 18:59:56 [INFO] cores.indexing_service [indexing_service.py:275] - 异步索引追加构建任务启动: department, 任务ID: 52d5c241-3822-43ee-bc11-873a677acd44
2025-06-12 18:59:56 [INFO] cores.indexing_service [indexing_service.py:275] - 异步索引追加构建任务启动: guideline, 任务ID: d054ff88-ad7f-4252-923b-e9d1932529d4
2025-06-12 18:59:56 [INFO] cores.indexing_service [indexing_service.py:275] - 异步索引追加构建任务启动: historical, 任务ID: 312159e8-8dff-4e4d-bfbf-75494379f735
2025-06-12 18:59:56 [INFO] cores.indexing_service [indexing_service.py:275] - 异步索引追加构建任务启动: delegated, 任务ID: e8b72aa0-66c6-4bcd-9928-72aa11d2e14e
2025-06-12 18:59:56 [INFO] cores.indexing_service [indexing_service.py:374] - 所有索引异步构建任务启动: ['52d5c241-3822-43ee-bc11-873a677acd44', 'd054ff88-ad7f-4252-923b-e9d1932529d4', '312159e8-8dff-4e4d-bfbf-75494379f735', 'e8b72aa0-66c6-4bcd-9928-72aa11d2e14e']
2025-06-12 18:59:56 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:52741 - "POST /v2/indexing/build HTTP/1.1" 200
2025-06-12 18:59:56 [INFO] cores.indexing_service [indexing_service.py:331] - 从数据源加载文档: department, 数量: 1
2025-06-12 18:59:56 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 18:59:56 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 18:59:56 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 18:59:56 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '52%', 'memory_utilization': '28%', 'temperature': '50°C', 'power_usage': '8.8W'}}
2025-06-12 18:59:56 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 18:59:56 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 18:59:56 [INFO] cores.indexing_service [indexing_service.py:76] - 为 department 启用生产环境稀疏嵌入函数（GPU服务优先）
2025-06-12 18:59:56 [INFO] cores.indexing_service [indexing_service.py:80] - 创建MilvusVectorStore: department, 操作: 连接到现有
2025-06-12 18:59:56 [INFO] shared.clients.embedding_client [embedding_client.py:64] - 初始化自定义API嵌入模型: BAAI/bge-m3
2025-06-12 18:59:57 [INFO] cores.indexing_service [indexing_service.py:173] - 混合索引 department 连接成功（稀疏嵌入: True）
2025-06-12 18:59:58 [INFO] shared.clients.embedding_client [embedding_client.py:183] - 成功获取 1 个密集嵌入（共 1 个文本）
2025-06-12 18:59:58 [INFO] cores.indexing_service [indexing_service.py:218] - 索引追加构建完成: department, 文档数: 1, 节点数: 1, 耗时: 1.998秒
2025-06-12 18:59:58 [INFO] cores.indexing_service [indexing_service.py:308] - 异步索引追加构建任务完成: 52d5c241-3822-43ee-bc11-873a677acd44
2025-06-12 18:59:58 [INFO] cores.indexing_service [indexing_service.py:331] - 从数据源加载文档: guideline, 数量: 1
2025-06-12 18:59:58 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 18:59:58 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 18:59:58 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 18:59:58 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '90%', 'memory_utilization': '44%', 'temperature': '50°C', 'power_usage': '10.0W'}}
2025-06-12 18:59:58 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 18:59:58 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 18:59:58 [INFO] cores.indexing_service [indexing_service.py:76] - 为 guideline 启用生产环境稀疏嵌入函数（GPU服务优先）
2025-06-12 18:59:58 [INFO] cores.indexing_service [indexing_service.py:80] - 创建MilvusVectorStore: guideline, 操作: 连接到现有
2025-06-12 18:59:58 [INFO] shared.clients.embedding_client [embedding_client.py:64] - 初始化自定义API嵌入模型: BAAI/bge-m3
2025-06-12 18:59:58 [INFO] cores.indexing_service [indexing_service.py:173] - 混合索引 guideline 连接成功（稀疏嵌入: True）
2025-06-12 18:59:58 [INFO] shared.clients.embedding_client [embedding_client.py:183] - 成功获取 1 个密集嵌入（共 1 个文本）
2025-06-12 18:59:58 [ERROR] cores.indexing_service [indexing_service.py:236] - 索引追加构建失败: guideline, 错误: <DataNotMatchException: (code=1, message=Attempt to insert an unexpected field `source` to collection without enabling dynamic field)>, 耗时: 0.308秒
2025-06-12 18:59:58 [ERROR] cores.indexing_service [indexing_service.py:317] - 异步索引追加构建任务失败: d054ff88-ad7f-4252-923b-e9d1932529d4, 错误: <DataNotMatchException: (code=1, message=Attempt to insert an unexpected field `source` to collection without enabling dynamic field)>
2025-06-12 18:59:58 [INFO] cores.indexing_service [indexing_service.py:331] - 从数据源加载文档: historical, 数量: 1
2025-06-12 18:59:58 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 18:59:58 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 18:59:58 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 18:59:58 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '11%', 'memory_utilization': '6%', 'temperature': '50°C', 'power_usage': '11.1W'}}
2025-06-12 18:59:58 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 18:59:58 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 18:59:58 [INFO] cores.indexing_service [indexing_service.py:76] - 为 historical 启用生产环境稀疏嵌入函数（GPU服务优先）
2025-06-12 18:59:58 [INFO] cores.indexing_service [indexing_service.py:80] - 创建MilvusVectorStore: historical, 操作: 连接到现有
2025-06-12 18:59:58 [INFO] shared.clients.embedding_client [embedding_client.py:64] - 初始化自定义API嵌入模型: BAAI/bge-m3
2025-06-12 18:59:59 [INFO] cores.indexing_service [indexing_service.py:173] - 混合索引 historical 连接成功（稀疏嵌入: True）
2025-06-12 18:59:59 [INFO] shared.clients.embedding_client [embedding_client.py:183] - 成功获取 1 个密集嵌入（共 1 个文本）
2025-06-12 18:59:59 [ERROR] cores.indexing_service [indexing_service.py:236] - 索引追加构建失败: historical, 错误: <DataNotMatchException: (code=1, message=Attempt to insert an unexpected field `source` to collection without enabling dynamic field)>, 耗时: 0.424秒
2025-06-12 18:59:59 [ERROR] cores.indexing_service [indexing_service.py:317] - 异步索引追加构建任务失败: 312159e8-8dff-4e4d-bfbf-75494379f735, 错误: <DataNotMatchException: (code=1, message=Attempt to insert an unexpected field `source` to collection without enabling dynamic field)>
2025-06-12 18:59:59 [INFO] cores.indexing_service [indexing_service.py:331] - 从数据源加载文档: delegated, 数量: 1
2025-06-12 18:59:59 [INFO] cores.indexing_service [indexing_service.py:80] - 创建MilvusVectorStore: delegated, 操作: 连接到现有
2025-06-12 18:59:59 [INFO] shared.clients.embedding_client [embedding_client.py:64] - 初始化自定义API嵌入模型: BAAI/bge-m3
2025-06-12 18:59:59 [INFO] cores.indexing_service [indexing_service.py:173] - 混合索引 delegated 连接成功（稀疏嵌入: False）
2025-06-12 18:59:59 [INFO] shared.clients.embedding_client [embedding_client.py:183] - 成功获取 1 个密集嵌入（共 1 个文本）
2025-06-12 18:59:59 [ERROR] cores.indexing_service [indexing_service.py:236] - 索引追加构建失败: delegated, 错误: <DataNotMatchException: (code=1, message=Attempt to insert an unexpected field `source` to collection without enabling dynamic field)>, 耗时: 0.261秒
2025-06-12 18:59:59 [ERROR] cores.indexing_service [indexing_service.py:317] - 异步索引追加构建任务失败: e8b72aa0-66c6-4bcd-9928-72aa11d2e14e, 错误: <DataNotMatchException: (code=1, message=Attempt to insert an unexpected field `source` to collection without enabling dynamic field)>
2025-06-12 19:00:54 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-12 19:00:54 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-12 19:00:54 [INFO] main [main.py:121] - CPU服务关闭
2025-06-12 19:00:54 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-12 19:00:54 [INFO] uvicorn.error [server.py:86] - Finished server process [16760]
2025-06-12 19:01:01 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 19:01:01 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 19:01:01 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 19:01:01 [INFO] __main__ [main.py:184] - 启动CPU服务: 0.0.0.0:8009
2025-06-12 19:01:01 [INFO] __main__ [main.py:190] - 启动模式: development
2025-06-12 19:01:01 [INFO] __main__ [main.py:191] - 工作进程数: 1
2025-06-12 19:01:01 [INFO] __main__ [main.py:192] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-12 19:01:01 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 19:01:01 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 19:01:01 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 19:01:01 [INFO] uvicorn.error [server.py:76] - Started server process [18656]
2025-06-12 19:01:01 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-12 19:01:01 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-12 19:01:01 [INFO] main [main.py:83] - 环境: development
2025-06-12 19:01:01 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8009
2025-06-12 19:01:01 [INFO] main [main.py:85] - GPU服务地址: None
2025-06-12 19:01:01 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-12 19:01:01 [INFO] cores.retrieval_service [retrieval_service.py:69] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-12 19:01:01 [INFO] cores.retrieval_service [retrieval_service.py:628] - 创建检索服务实例
2025-06-12 19:01:01 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-12 19:01:01 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-12 19:01:01 [INFO] cores.retrieval_service [retrieval_service.py:76] - 正在初始化Milvus客户端...
2025-06-12 19:01:01 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-12 19:01:01 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 19:01:01 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-12 19:01:01 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 19:01:01 [INFO] uvicorn.error [server.py:218] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-12 19:01:01 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 19:01:01 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '31%', 'memory_utilization': '28%', 'temperature': '51°C', 'power_usage': '7.3W'}}
2025-06-12 19:01:01 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 19:01:01 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 19:01:01 [INFO] shared.clients.milvus_client [milvus_client.py:222] - 成功连接到Milvus: http://************:19530
2025-06-12 19:01:01 [INFO] shared.clients.milvus_client [milvus_client.py:193] - Milvus客户端初始化完成: http://************:19530
2025-06-12 19:01:01 [INFO] shared.clients.milvus_client [milvus_client.py:728] - 创建Milvus客户端实例
2025-06-12 19:01:01 [INFO] cores.retrieval_service [retrieval_service.py:78] - Milvus客户端初始化成功
2025-06-12 19:01:01 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-12 19:03:02 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-12 19:03:02 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-12 19:03:02 [INFO] main [main.py:121] - CPU服务关闭
2025-06-12 19:03:02 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-12 19:03:02 [INFO] uvicorn.error [server.py:86] - Finished server process [18656]
2025-06-12 19:03:07 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 19:03:07 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 19:03:07 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 19:03:07 [INFO] __main__ [main.py:184] - 启动CPU服务: 0.0.0.0:8009
2025-06-12 19:03:07 [INFO] __main__ [main.py:190] - 启动模式: development
2025-06-12 19:03:07 [INFO] __main__ [main.py:191] - 工作进程数: 1
2025-06-12 19:03:07 [INFO] __main__ [main.py:192] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-12 19:03:07 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 19:03:07 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 19:03:07 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 19:03:07 [INFO] uvicorn.error [server.py:76] - Started server process [28140]
2025-06-12 19:03:07 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-12 19:03:07 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-12 19:03:07 [INFO] main [main.py:83] - 环境: development
2025-06-12 19:03:07 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8009
2025-06-12 19:03:07 [INFO] main [main.py:85] - GPU服务地址: None
2025-06-12 19:03:07 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-12 19:03:07 [INFO] cores.retrieval_service [retrieval_service.py:69] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-12 19:03:07 [INFO] cores.retrieval_service [retrieval_service.py:628] - 创建检索服务实例
2025-06-12 19:03:07 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-12 19:03:07 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-12 19:03:07 [INFO] cores.retrieval_service [retrieval_service.py:76] - 正在初始化Milvus客户端...
2025-06-12 19:03:07 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-12 19:03:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 19:03:07 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-12 19:03:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 19:03:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 19:03:07 [INFO] uvicorn.error [server.py:218] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-12 19:03:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '2%', 'memory_utilization': '40%', 'temperature': '47°C', 'power_usage': '7.2W'}}
2025-06-12 19:03:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 19:03:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 19:03:07 [INFO] shared.clients.milvus_client [milvus_client.py:222] - 成功连接到Milvus: http://************:19530
2025-06-12 19:03:07 [INFO] shared.clients.milvus_client [milvus_client.py:193] - Milvus客户端初始化完成: http://************:19530
2025-06-12 19:03:07 [INFO] shared.clients.milvus_client [milvus_client.py:728] - 创建Milvus客户端实例
2025-06-12 19:03:07 [INFO] cores.retrieval_service [retrieval_service.py:78] - Milvus客户端初始化成功
2025-06-12 19:03:07 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-12 19:03:13 [INFO] cores.indexing_service [indexing_service.py:58] - 优化版索引服务初始化完成（GPU+CPU分离架构）
2025-06-12 19:03:13 [INFO] cores.indexing_service [indexing_service.py:573] - 创建索引服务实例
2025-06-12 19:03:13 [INFO] cores.indexing_service [indexing_service.py:275] - 异步索引追加构建任务启动: department, 任务ID: 48b8325a-a60d-4a8f-936e-52eaecc1ab72
2025-06-12 19:03:13 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:52863 - "POST /v2/indexing/build HTTP/1.1" 200
2025-06-12 19:03:14 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 19:03:14 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 19:03:14 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 19:03:14 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '16%', 'memory_utilization': '6%', 'temperature': '48°C', 'power_usage': '15.5W'}}
2025-06-12 19:03:14 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 19:03:14 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 19:03:14 [INFO] cores.indexing_service [indexing_service.py:76] - 为 department 启用生产环境稀疏嵌入函数（GPU服务优先）
2025-06-12 19:03:14 [INFO] cores.indexing_service [indexing_service.py:80] - 创建MilvusVectorStore: department, 操作: 连接到现有
2025-06-12 19:03:15 [INFO] shared.clients.embedding_client [embedding_client.py:64] - 初始化自定义API嵌入模型: BAAI/bge-m3
2025-06-12 19:03:16 [INFO] cores.indexing_service [indexing_service.py:173] - 混合索引 department 连接成功（稀疏嵌入: True）
2025-06-12 19:03:44 [INFO] shared.clients.embedding_client [embedding_client.py:183] - 成功获取 2048 个密集嵌入（共 2048 个文本）
2025-06-12 19:04:50 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 19:04:50 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 19:04:50 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 19:04:50 [INFO] __main__ [main.py:184] - 启动CPU服务: 0.0.0.0:8009
2025-06-12 19:04:50 [INFO] __main__ [main.py:190] - 启动模式: development
2025-06-12 19:04:50 [INFO] __main__ [main.py:191] - 工作进程数: 1
2025-06-12 19:04:50 [INFO] __main__ [main.py:192] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-12 19:04:50 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 19:04:50 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 19:04:50 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 19:04:50 [INFO] uvicorn.error [server.py:76] - Started server process [4344]
2025-06-12 19:04:50 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-12 19:04:50 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-12 19:04:50 [INFO] main [main.py:83] - 环境: development
2025-06-12 19:04:50 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8009
2025-06-12 19:04:50 [INFO] main [main.py:85] - GPU服务地址: None
2025-06-12 19:04:50 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-12 19:04:50 [INFO] cores.retrieval_service [retrieval_service.py:69] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-12 19:04:50 [INFO] cores.retrieval_service [retrieval_service.py:628] - 创建检索服务实例
2025-06-12 19:04:50 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-12 19:04:50 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-12 19:04:50 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-12 19:04:50 [INFO] cores.retrieval_service [retrieval_service.py:76] - 正在初始化Milvus客户端...
2025-06-12 19:04:50 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-12 19:04:50 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 19:04:50 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 19:04:50 [INFO] uvicorn.error [server.py:218] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-12 19:04:50 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 19:04:50 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '1%', 'memory_utilization': '12%', 'temperature': '53°C', 'power_usage': '8.2W'}}
2025-06-12 19:04:50 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 19:04:50 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 19:04:50 [INFO] shared.clients.milvus_client [milvus_client.py:222] - 成功连接到Milvus: http://************:19530
2025-06-12 19:04:50 [INFO] shared.clients.milvus_client [milvus_client.py:193] - Milvus客户端初始化完成: http://************:19530
2025-06-12 19:04:50 [INFO] shared.clients.milvus_client [milvus_client.py:728] - 创建Milvus客户端实例
2025-06-12 19:04:50 [INFO] cores.retrieval_service [retrieval_service.py:78] - Milvus客户端初始化成功
2025-06-12 19:04:50 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-12 19:05:09 [INFO] cores.indexing_service [indexing_service.py:58] - 优化版索引服务初始化完成（GPU+CPU分离架构）
2025-06-12 19:05:09 [INFO] cores.indexing_service [indexing_service.py:573] - 创建索引服务实例
2025-06-12 19:05:10 [INFO] cores.indexing_service [indexing_service.py:275] - 异步索引追加构建任务启动: department, 任务ID: e600ea15-e1f8-4616-a194-e65e8e7054aa
2025-06-12 19:05:10 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:55092 - "POST /v2/indexing/build HTTP/1.1" 200
2025-06-12 19:05:11 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 19:05:11 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 19:05:11 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 19:05:11 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '33%', 'memory_utilization': '17%', 'temperature': '52°C', 'power_usage': '9.3W'}}
2025-06-12 19:05:11 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 19:05:11 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 19:05:11 [INFO] cores.indexing_service [indexing_service.py:76] - 为 department 启用生产环境稀疏嵌入函数（GPU服务优先）
2025-06-12 19:05:11 [INFO] cores.indexing_service [indexing_service.py:80] - 创建MilvusVectorStore: department, 操作: 连接到现有
2025-06-12 19:05:11 [INFO] shared.clients.embedding_client [embedding_client.py:64] - 初始化自定义API嵌入模型: BAAI/bge-m3
2025-06-12 19:05:12 [INFO] cores.indexing_service [indexing_service.py:173] - 混合索引 department 连接成功（稀疏嵌入: True）
2025-06-12 19:05:31 [INFO] shared.clients.embedding_client [embedding_client.py:183] - 成功获取 2048 个密集嵌入（共 2048 个文本）
2025-06-12 19:07:01 [INFO] shared.clients.embedding_client [embedding_client.py:183] - 成功获取 531 个密集嵌入（共 531 个文本）
2025-06-12 19:07:22 [INFO] cores.indexing_service [indexing_service.py:218] - 索引追加构建完成: department, 文档数: 2575, 节点数: 2579, 耗时: 132.549秒
2025-06-12 19:07:22 [INFO] cores.indexing_service [indexing_service.py:308] - 异步索引追加构建任务完成: e600ea15-e1f8-4616-a194-e65e8e7054aa
2025-06-12 19:08:37 [INFO] uvicorn.error [server.py:264] - Shutting down
2025-06-12 19:08:37 [INFO] uvicorn.error [on.py:65] - Waiting for application shutdown.
2025-06-12 19:08:37 [INFO] main [main.py:121] - CPU服务关闭
2025-06-12 19:08:37 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-12 19:08:37 [INFO] uvicorn.error [server.py:86] - Finished server process [4344]
2025-06-12 19:08:45 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 19:08:45 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 19:08:45 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 19:08:45 [INFO] __main__ [main.py:184] - 启动CPU服务: 0.0.0.0:8009
2025-06-12 19:08:45 [INFO] __main__ [main.py:190] - 启动模式: development
2025-06-12 19:08:45 [INFO] __main__ [main.py:191] - 工作进程数: 1
2025-06-12 19:08:45 [INFO] __main__ [main.py:192] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-12 19:08:45 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 19:08:45 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 19:08:45 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 19:08:45 [INFO] uvicorn.error [server.py:76] - Started server process [36996]
2025-06-12 19:08:45 [INFO] uvicorn.error [on.py:46] - Waiting for application startup.
2025-06-12 19:08:45 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-12 19:08:45 [INFO] main [main.py:83] - 环境: development
2025-06-12 19:08:45 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8009
2025-06-12 19:08:45 [INFO] main [main.py:85] - GPU服务地址: None
2025-06-12 19:08:45 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-12 19:08:45 [INFO] cores.retrieval_service [retrieval_service.py:69] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-12 19:08:45 [INFO] cores.retrieval_service [retrieval_service.py:628] - 创建检索服务实例
2025-06-12 19:08:45 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-12 19:08:45 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-12 19:08:45 [INFO] cores.retrieval_service [retrieval_service.py:76] - 正在初始化Milvus客户端...
2025-06-12 19:08:45 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-12 19:08:45 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 19:08:45 [INFO] uvicorn.error [on.py:60] - Application startup complete.
2025-06-12 19:08:45 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 19:08:45 [INFO] uvicorn.error [server.py:218] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-12 19:08:45 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 19:08:45 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '0%', 'memory_utilization': '25%', 'temperature': '47°C', 'power_usage': '6.4W'}}
2025-06-12 19:08:45 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 19:08:45 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 19:08:45 [INFO] shared.clients.milvus_client [milvus_client.py:222] - 成功连接到Milvus: http://************:19530
2025-06-12 19:08:45 [INFO] shared.clients.milvus_client [milvus_client.py:193] - Milvus客户端初始化完成: http://************:19530
2025-06-12 19:08:45 [INFO] shared.clients.milvus_client [milvus_client.py:728] - 创建Milvus客户端实例
2025-06-12 19:08:45 [INFO] cores.retrieval_service [retrieval_service.py:78] - Milvus客户端初始化成功
2025-06-12 19:08:45 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-12 19:09:06 [INFO] cores.indexing_service [indexing_service.py:58] - 优化版索引服务初始化完成（GPU+CPU分离架构）
2025-06-12 19:09:06 [INFO] cores.indexing_service [indexing_service.py:573] - 创建索引服务实例
2025-06-12 19:09:06 [INFO] cores.indexing_service [indexing_service.py:275] - 异步索引追加构建任务启动: department, 任务ID: a141a5ff-7fc7-423b-b5da-b7223a58134f
2025-06-12 19:09:06 [INFO] uvicorn.access [h11_impl.py:478] - 127.0.0.1:57933 - "POST /v2/indexing/build HTTP/1.1" 200
2025-06-12 19:09:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 19:09:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 19:09:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 19:09:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '33%', 'memory_utilization': '17%', 'temperature': '45°C', 'power_usage': '9.4W'}}
2025-06-12 19:09:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 19:09:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 19:09:07 [INFO] cores.indexing_service [indexing_service.py:76] - 为 department 启用生产环境稀疏嵌入函数（GPU服务优先）
2025-06-12 19:09:07 [INFO] cores.indexing_service [indexing_service.py:80] - 创建MilvusVectorStore: department, 操作: 连接到现有
2025-06-12 19:09:08 [INFO] shared.clients.embedding_client [embedding_client.py:64] - 初始化自定义API嵌入模型: BAAI/bge-m3
2025-06-12 19:09:10 [INFO] cores.indexing_service [indexing_service.py:173] - 混合索引 department 连接成功（稀疏嵌入: True）
2025-06-12 19:09:29 [INFO] shared.clients.embedding_client [embedding_client.py:183] - 成功获取 2048 个密集嵌入（共 2048 个文本）
2025-06-12 23:16:12 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 23:16:12 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 23:16:12 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 23:16:12 [INFO] __main__ [main.py:184] - 启动CPU服务: 0.0.0.0:8009
2025-06-12 23:16:12 [INFO] __main__ [main.py:190] - 启动模式: development
2025-06-12 23:16:12 [INFO] __main__ [main.py:191] - 工作进程数: 1
2025-06-12 23:16:12 [INFO] __main__ [main.py:192] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-12 23:16:12 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-12 23:16:12 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-12.log
2025-06-12 23:16:12 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-12.log
2025-06-12 23:16:12 [INFO] uvicorn.error [server.py:83] - Started server process [36372]
2025-06-12 23:16:12 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-12 23:16:12 [INFO] main [main.py:82] - 🚀 初始化CPU服务...
2025-06-12 23:16:12 [INFO] main [main.py:83] - 环境: development
2025-06-12 23:16:12 [INFO] main [main.py:84] - 服务地址: http://0.0.0.0:8009
2025-06-12 23:16:12 [INFO] main [main.py:85] - GPU服务地址: None
2025-06-12 23:16:12 [INFO] main [main.py:88] - 🔥 开始预热服务组件...
2025-06-12 23:16:12 [INFO] cores.retrieval_service [retrieval_service.py:69] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-12 23:16:12 [INFO] cores.retrieval_service [retrieval_service.py:628] - 创建检索服务实例
2025-06-12 23:16:12 [INFO] main [main.py:99] - 正在预热Milvus客户端...
2025-06-12 23:16:12 [INFO] main [main.py:109] - ✅ 服务预热已启动（后台进行）
2025-06-12 23:16:12 [INFO] main [main.py:114] - ✅ CPU服务初始化完成
2025-06-12 23:16:12 [INFO] cores.retrieval_service [retrieval_service.py:76] - 正在初始化Milvus客户端...
2025-06-12 23:16:12 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-12 23:16:12 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 23:16:12 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-12 23:16:12 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 23:16:12 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 23:16:12 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '0%', 'memory_utilization': '5%', 'temperature': '46°C', 'power_usage': '6.0W'}}
2025-06-12 23:16:12 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 23:16:12 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 23:16:13 [INFO] shared.clients.milvus_client [milvus_client.py:222] - 成功连接到Milvus: http://************:19530
2025-06-12 23:16:13 [INFO] shared.clients.milvus_client [milvus_client.py:193] - Milvus客户端初始化完成: http://************:19530
2025-06-12 23:16:13 [INFO] shared.clients.milvus_client [milvus_client.py:728] - 创建Milvus客户端实例
2025-06-12 23:16:13 [INFO] cores.retrieval_service [retrieval_service.py:78] - Milvus客户端初始化成功
2025-06-12 23:16:13 [INFO] main [main.py:101] - ✅ Milvus客户端预热完成
2025-06-12 23:16:15 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:65426 - "GET /docs HTTP/1.1" 200
2025-06-12 23:16:15 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:65426 - "GET /openapi.json HTTP/1.1" 200
2025-06-12 23:16:44 [INFO] cores.indexing_service [indexing_service.py:58] - 优化版索引服务初始化完成（GPU+CPU分离架构）
2025-06-12 23:16:44 [INFO] cores.indexing_service [indexing_service.py:573] - 创建索引服务实例
2025-06-12 23:16:45 [INFO] cores.indexing_service [indexing_service.py:275] - 异步索引追加构建任务启动: department, 任务ID: 065cb5a3-de25-41af-8b4b-e5456c4d0042
2025-06-12 23:16:45 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:65427 - "POST /v2/indexing/build HTTP/1.1" 200
2025-06-12 23:16:47 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:307] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-12 23:16:47 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:71] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-12 23:16:47 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:56] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-12 23:16:47 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:89] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '40%', 'memory_utilization': '18%', 'temperature': '46°C', 'power_usage': '6.5W'}}
2025-06-12 23:16:47 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:227] - GPU服务稀疏嵌入客户端初始化成功
2025-06-12 23:16:47 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:231] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-12 23:16:47 [INFO] cores.indexing_service [indexing_service.py:76] - 为 department 启用生产环境稀疏嵌入函数（GPU服务优先）
2025-06-12 23:16:47 [INFO] cores.indexing_service [indexing_service.py:80] - 创建MilvusVectorStore: department, 操作: 连接到现有
2025-06-12 23:16:48 [INFO] shared.clients.embedding_client [embedding_client.py:64] - 初始化自定义API嵌入模型: BAAI/bge-m3
2025-06-12 23:16:48 [INFO] cores.indexing_service [indexing_service.py:173] - 混合索引 department 连接成功（稀疏嵌入: True）
2025-06-12 23:17:14 [INFO] shared.clients.embedding_client [embedding_client.py:183] - 成功获取 2048 个密集嵌入（共 2048 个文本）
2025-06-12 23:19:13 [INFO] shared.clients.embedding_client [embedding_client.py:183] - 成功获取 531 个密集嵌入（共 531 个文本）
2025-06-12 23:19:39 [INFO] cores.indexing_service [indexing_service.py:218] - 索引追加构建完成: department, 文档数: 2575, 节点数: 2579, 耗时: 174.400秒
2025-06-12 23:19:39 [INFO] cores.indexing_service [indexing_service.py:308] - 异步索引追加构建任务完成: 065cb5a3-de25-41af-8b4b-e5456c4d0042
2025-06-13 00:14:42 [INFO] uvicorn.error [server.py:263] - Shutting down
2025-06-13 00:14:42 [INFO] uvicorn.error [on.py:67] - Waiting for application shutdown.
2025-06-13 00:14:42 [INFO] main [main.py:121] - CPU服务关闭
2025-06-13 00:14:42 [INFO] uvicorn.error [on.py:76] - Application shutdown complete.
2025-06-13 00:14:42 [INFO] uvicorn.error [server.py:93] - Finished server process [36372]
