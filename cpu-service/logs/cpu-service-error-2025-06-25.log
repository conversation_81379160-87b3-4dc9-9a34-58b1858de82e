2025-06-25 09:15:46 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-25 09:22:03 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-25 09:31:20 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-25 09:40:27 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-25 09:59:33 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-25 10:00:57 [ERROR] shared.clients.milvus_client [milvus_client.py:225] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-25 10:07:26 [ERROR] uvicorn.error [on.py:134] - Traceback (most recent call last):
  File "D:\ProgramFiles\python311\Lib\asyncio\runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "D:\ProgramFiles\python311\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ProgramFiles\python311\Lib\asyncio\base_events.py", line 641, in run_until_complete
    self.run_forever()
  File "D:\ProgramFiles\python311\Lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "D:\ProgramFiles\python311\Lib\asyncio\base_events.py", line 608, in run_forever
    self._run_once()
  File "D:\ProgramFiles\python311\Lib\asyncio\base_events.py", line 1936, in _run_once
    handle._run()
  File "D:\ProgramFiles\python311\Lib\asyncio\events.py", line 84, in _run
    self._context.run(self._callback, *self._args)
  File "D:\Project\Backend\ai\ai-jiesujiban-v3\.venv\Lib\site-packages\uvicorn\server.py", line 69, in serve
    with self.capture_signals():
  File "D:\ProgramFiles\python311\Lib\contextlib.py", line 144, in __exit__
    next(self.gen)
  File "D:\Project\Backend\ai\ai-jiesujiban-v3\.venv\Lib\site-packages\uvicorn\server.py", line 330, in capture_signals
    signal.raise_signal(captured_signal)
  File "D:\ProgramFiles\python311\Lib\asyncio\runners.py", line 157, in _on_sigint
    raise KeyboardInterrupt()
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Project\Backend\ai\ai-jiesujiban-v3\.venv\Lib\site-packages\starlette\routing.py", line 699, in lifespan
    await receive()
  File "D:\Project\Backend\ai\ai-jiesujiban-v3\.venv\Lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ProgramFiles\python311\Lib\asyncio\queues.py", line 158, in get
    await getter
asyncio.exceptions.CancelledError

2025-06-25 10:07:49 [ERROR] shared.clients.milvus_client [milvus_client.py:221] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-25 10:09:03 [ERROR] shared.clients.milvus_client [milvus_client.py:221] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-25 10:22:55 [ERROR] shared.clients.milvus_client [milvus_client.py:221] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-06-25 10:29:01 [ERROR] shared.clients.milvus_client [milvus_client.py:221] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-25 10:54:50 [ERROR] shared.clients.milvus_client [milvus_client.py:221] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
2025-06-25 11:10:16 [ERROR] shared.clients.milvus_client [milvus_client.py:221] - 连接Milvus失败: <MilvusException: (code=2, message=Fail connecting to server on localhost:19530, illegal connection params or server unavailable)>
