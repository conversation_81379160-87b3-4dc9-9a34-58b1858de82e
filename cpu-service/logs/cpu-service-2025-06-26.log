2025-06-26 16:17:52 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-26 16:17:52 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-26.log
2025-06-26 16:17:52 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-26.log
2025-06-26 16:17:52 [INFO] __main__ [main.py:188] - 启动CPU服务: 0.0.0.0:8009
2025-06-26 16:17:52 [INFO] __main__ [main.py:194] - 启动模式: development
2025-06-26 16:17:52 [INFO] __main__ [main.py:195] - 工作进程数: 1
2025-06-26 16:17:52 [INFO] __main__ [main.py:196] - 热重载: 已禁用 (为确保服务稳定性)
2025-06-26 16:17:52 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: cpu-service, 级别: INFO
2025-06-26 16:17:52 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\cpu-service-2025-06-26.log
2025-06-26 16:17:52 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\cpu-service-error-2025-06-26.log
2025-06-26 16:17:52 [INFO] uvicorn.error [server.py:83] - Started server process [26420]
2025-06-26 16:17:52 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-26 16:17:52 [INFO] main [main.py:86] - 初始化CPU服务...
2025-06-26 16:17:52 [INFO] main [main.py:87] - 环境: development
2025-06-26 16:17:52 [INFO] main [main.py:88] - 服务地址: http://0.0.0.0:8009
2025-06-26 16:17:52 [INFO] main [main.py:89] - GPU服务地址: None
2025-06-26 16:17:52 [INFO] main [main.py:92] - 开始预热服务组件...
2025-06-26 16:17:52 [INFO] cores.retrieval_service [retrieval_service.py:65] - 优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）
2025-06-26 16:17:52 [INFO] cores.retrieval_service [retrieval_service.py:624] - 创建检索服务实例
2025-06-26 16:17:52 [INFO] main [main.py:103] - 正在预热Milvus客户端...
2025-06-26 16:17:52 [INFO] main [main.py:113] - 服务预热已启动（后台进行）
2025-06-26 16:17:52 [INFO] cores.retrieval_service [retrieval_service.py:72] - 正在初始化Milvus客户端...
2025-06-26 16:17:52 [INFO] main [main.py:118] - CPU服务初始化完成
2025-06-26 16:17:52 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:309] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-26 16:17:52 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-26 16:17:52 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:70] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-26 16:17:52 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-06-26 16:17:52 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:54] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-26 16:17:52 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:55] - 批处理配置: batch_size=32
2025-06-26 16:17:52 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:88] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '31%', 'memory_utilization': '22%', 'temperature': '43°C', 'power_usage': '6.4W'}}
2025-06-26 16:17:52 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:229] - GPU服务稀疏嵌入客户端初始化成功
2025-06-26 16:17:52 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:233] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-26 16:17:52 [INFO] shared.clients.milvus_client [milvus_client.py:218] - 成功连接到Milvus: http://172.24.255.5:19530
2025-06-26 16:17:52 [INFO] shared.clients.milvus_client [milvus_client.py:189] - Milvus客户端初始化完成: http://172.24.255.5:19530
2025-06-26 16:17:52 [INFO] shared.clients.milvus_client [milvus_client.py:724] - 创建Milvus客户端实例
2025-06-26 16:17:52 [INFO] cores.retrieval_service [retrieval_service.py:74] - Milvus客户端初始化成功
2025-06-26 16:17:52 [INFO] main [main.py:105] - Milvus客户端预热完成
2025-06-26 16:18:07 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:56196 - "GET /docs HTTP/1.1" 200
2025-06-26 16:18:07 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:56196 - "GET /openapi.json HTTP/1.1" 200
2025-06-26 16:20:06 [INFO] cores.retrieval_service [retrieval_service.py:320] - 集合 department 已存在
2025-06-26 16:20:06 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-26 16:20:06 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: department, 查询: 你好..., 结果数: 0, 耗时: 0.441秒
2025-06-26 16:20:06 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: department, 查询: 你好..., 结果数: 0, 耗时: 0.447秒
2025-06-26 16:20:06 [INFO] cores.retrieval_service [retrieval_service.py:88] - 正在初始化GPU稀疏嵌入函数...
2025-06-26 16:20:06 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:309] - 使用生产环境稀疏嵌入策略：GPU服务优先，本地回退
2025-06-26 16:20:06 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:70] - 使用本地开发GPU服务URL: http://localhost:8001
2025-06-26 16:20:06 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:54] - 初始化GPU服务稀疏嵌入客户端: http://localhost:8001
2025-06-26 16:20:06 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:55] - 批处理配置: batch_size=32
2025-06-26 16:20:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:88] - GPU服务健康检查通过: {'status': 'healthy', 'service': 'bge-m3-sparse-embedding', 'device': 'cuda', 'model_path': '/app/models/hub/bge-m3', 'gpu_info': {'gpu_name': 'NVIDIA GeForce RTX 4060 Laptop GPU', 'gpu_memory_total': '8.0GB', 'gpu_memory_allocated': '1.1GB', 'gpu_memory_cached': '1.1GB', 'gpu_utilization': '90%', 'memory_utilization': '42%', 'temperature': '44°C', 'power_usage': '7.6W'}}
2025-06-26 16:20:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:229] - GPU服务稀疏嵌入客户端初始化成功
2025-06-26 16:20:07 [INFO] shared.clients.gpu_service_client [gpu_service_client.py:233] - 初始化混合稀疏嵌入函数（GPU服务优先，本地回退）
2025-06-26 16:20:07 [INFO] cores.retrieval_service [retrieval_service.py:90] - GPU稀疏嵌入函数初始化成功
2025-06-26 16:20:07 [INFO] cores.retrieval_service [retrieval_service.py:320] - 集合 guideline 已存在
2025-06-26 16:20:07 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-26 16:20:07 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: guideline, 查询: 你好..., 结果数: 0, 耗时: 0.125秒
2025-06-26 16:20:07 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: guideline, 查询: 你好..., 结果数: 0, 耗时: 0.144秒
2025-06-26 16:20:07 [INFO] cores.retrieval_service [retrieval_service.py:320] - 集合 historical 已存在
2025-06-26 16:20:07 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-26 16:20:07 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: historical, 查询: 你好..., 结果数: 0, 耗时: 0.118秒
2025-06-26 16:20:07 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: historical, 查询: 你好..., 结果数: 0, 耗时: 0.124秒
2025-06-26 16:20:07 [INFO] cores.retrieval_service [retrieval_service.py:320] - 集合 delegated 已存在
2025-06-26 16:20:07 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-26 16:20:07 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: delegated, 查询: 你好..., 结果数: 0, 耗时: 0.166秒
2025-06-26 16:20:07 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: delegated, 查询: 你好..., 结果数: 0, 耗时: 0.171秒
2025-06-26 16:20:07 [INFO] cores.retrieval_service [retrieval_service.py:519] - 并行检索完成: 查询: 你好..., 总耗时: 0.922秒
2025-06-26 16:20:07 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:56360 - "POST /v2/retrieval/search/parallel HTTP/1.1" 200
2025-06-26 16:20:32 [INFO] cores.retrieval_service [retrieval_service.py:320] - 集合 department 已存在
2025-06-26 16:20:32 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-26 16:20:32 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: department, 查询: 你好..., 结果数: 0, 耗时: 0.100秒
2025-06-26 16:20:32 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: department, 查询: 你好..., 结果数: 0, 耗时: 0.107秒
2025-06-26 16:20:32 [INFO] cores.retrieval_service [retrieval_service.py:320] - 集合 guideline 已存在
2025-06-26 16:20:32 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-26 16:20:32 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: guideline, 查询: 你好..., 结果数: 0, 耗时: 0.069秒
2025-06-26 16:20:32 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: guideline, 查询: 你好..., 结果数: 0, 耗时: 0.081秒
2025-06-26 16:20:32 [INFO] cores.retrieval_service [retrieval_service.py:320] - 集合 historical 已存在
2025-06-26 16:20:32 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-26 16:20:32 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: historical, 查询: 你好..., 结果数: 0, 耗时: 0.098秒
2025-06-26 16:20:32 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: historical, 查询: 你好..., 结果数: 0, 耗时: 0.110秒
2025-06-26 16:20:32 [INFO] cores.retrieval_service [retrieval_service.py:320] - 集合 delegated 已存在
2025-06-26 16:20:32 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-26 16:20:32 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: delegated, 查询: 你好..., 结果数: 0, 耗时: 0.079秒
2025-06-26 16:20:32 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: delegated, 查询: 你好..., 结果数: 0, 耗时: 0.086秒
2025-06-26 16:20:32 [INFO] cores.retrieval_service [retrieval_service.py:519] - 并行检索完成: 查询: 你好..., 总耗时: 0.384秒
2025-06-26 16:20:32 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:56363 - "POST /v2/retrieval/search/parallel HTTP/1.1" 200
2025-06-26 16:20:35 [INFO] cores.retrieval_service [retrieval_service.py:320] - 集合 department 已存在
2025-06-26 16:20:35 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-26 16:20:35 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: department, 查询: 你好..., 结果数: 0, 耗时: 0.352秒
2025-06-26 16:20:35 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: department, 查询: 你好..., 结果数: 0, 耗时: 0.400秒
2025-06-26 16:20:35 [INFO] cores.retrieval_service [retrieval_service.py:320] - 集合 guideline 已存在
2025-06-26 16:20:35 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-26 16:20:36 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: guideline, 查询: 你好..., 结果数: 0, 耗时: 0.220秒
2025-06-26 16:20:36 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: guideline, 查询: 你好..., 结果数: 0, 耗时: 0.245秒
2025-06-26 16:20:36 [INFO] cores.retrieval_service [retrieval_service.py:320] - 集合 historical 已存在
2025-06-26 16:20:36 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-26 16:20:36 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: historical, 查询: 你好..., 结果数: 0, 耗时: 0.141秒
2025-06-26 16:20:36 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: historical, 查询: 你好..., 结果数: 0, 耗时: 0.235秒
2025-06-26 16:20:36 [INFO] cores.retrieval_service [retrieval_service.py:320] - 集合 delegated 已存在
2025-06-26 16:20:36 [INFO] shared.clients.milvus_client [milvus_client.py:140] - 生成密集嵌入: 1个向量，维度: 1024
2025-06-26 16:20:36 [INFO] shared.clients.milvus_client [milvus_client.py:583] - 混合检索完成: delegated, 查询: 你好..., 结果数: 0, 耗时: 0.058秒
2025-06-26 16:20:36 [INFO] cores.retrieval_service [retrieval_service.py:428] - 混合检索完成: delegated, 查询: 你好..., 结果数: 0, 耗时: 0.114秒
2025-06-26 16:20:36 [INFO] cores.retrieval_service [retrieval_service.py:519] - 并行检索完成: 查询: 你好..., 总耗时: 0.995秒
2025-06-26 16:20:36 [INFO] uvicorn.access [httptools_impl.py:476] - 127.0.0.1:56363 - "POST /v2/retrieval/search/parallel HTTP/1.1" 200
