import os
import sqlite3
import json
import time
import logging
import asyncio
from typing import Dict, List, Any, Optional, Tuple, Union
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

class DatabaseThread:
    """数据库线程类，用于在单独的线程中执行数据库操作"""

    def __init__(self, db_file: str):
        self.db_file = db_file
        self.executor = ThreadPoolExecutor(max_workers=1)
        self.conn = None
        self.lock = asyncio.Lock()

    async def start(self):
        """启动数据库线程"""
        # 确保数据库目录存在
        os.makedirs(os.path.dirname(self.db_file), exist_ok=True)

        # 在线程中创建连接
        def _create_connection():
            conn = sqlite3.connect(self.db_file)
            conn.row_factory = sqlite3.Row
            return conn

        self.conn = await asyncio.get_event_loop().run_in_executor(
            self.executor, _create_connection
        )

    async def execute(self, func):
        """在数据库线程中执行函数"""
        async with self.lock:
            return await asyncio.get_event_loop().run_in_executor(
                self.executor, func
            )

    async def close(self):
        """关闭数据库连接"""
        if self.conn:
            await self.execute(lambda: self.conn.close())
            self.executor.shutdown()

class FileDatabase:
    """文件数据库类，用于管理文件元数据和API密钥"""

    _instance = None

    @classmethod
    async def get_instance(cls):
        """获取数据库实例（单例模式）"""
        if cls._instance is None:
            cls._instance = FileDatabase()
            await cls._instance.init_db()
        return cls._instance

    async def init_db(self):
        """初始化数据库"""
        # 获取数据库文件路径
        self.db_file = os.environ.get("DB_FILE", os.path.join(os.path.dirname(__file__), "..", "data", "db", "main.db"))
        logger.info(f"使用数据库文件: {self.db_file}")

        # 确保数据库目录存在
        os.makedirs(os.path.dirname(self.db_file), exist_ok=True)

        # 初始化数据库连接
        self._db_thread = DatabaseThread(self.db_file)
        await self._db_thread.start()

        # 创建表（如果不存在）
        await self._db_thread.execute(lambda: self._db_thread.conn.execute('''
        CREATE TABLE IF NOT EXISTS file_metadata (
            id TEXT PRIMARY KEY,
            filename TEXT NOT NULL,
            collection_type TEXT NOT NULL,
            created_at REAL NOT NULL,
            file_size INTEGER NOT NULL,
            file_type TEXT NOT NULL,
            vectorized BOOLEAN NOT NULL DEFAULT 0,
            metadata TEXT
        )
        '''))

        await self._db_thread.execute(lambda: self._db_thread.conn.execute('''
        CREATE TABLE IF NOT EXISTS api_keys (
            api_key TEXT PRIMARY KEY,
            client_name TEXT NOT NULL,
            created_at REAL NOT NULL,
            permissions TEXT NOT NULL,
            rate_limit INTEGER NOT NULL DEFAULT 100,
            is_active BOOLEAN NOT NULL DEFAULT 1,
            metadata TEXT
        )
        '''))

        await self._db_thread.execute(lambda: self._db_thread.conn.commit())
        logger.info("数据库初始化完成")

    async def close(self):
        """关闭数据库连接"""
        if hasattr(self, '_db_thread'):
            await self._db_thread.close()

    async def execute_query(self, query: str, params: tuple = (), fetch_one: bool = False) -> Union[List[sqlite3.Row], sqlite3.Row, None]:
        """执行SQL查询"""
        try:
            def _execute_query():
                cursor = self._db_thread.conn.cursor()
                cursor.execute(query, params)
                if fetch_one:
                    return cursor.fetchone()
                return cursor.fetchall()

            return await self._db_thread.execute(_execute_query)
        except Exception as e:
            logger.error(f"执行查询失败: {str(e)}, 查询: {query}, 参数: {params}")
            return None

    async def execute_update(self, query: str, params: tuple = ()) -> bool:
        """执行SQL更新"""
        try:
            def _execute_update():
                cursor = self._db_thread.conn.cursor()
                cursor.execute(query, params)
                self._db_thread.conn.commit()
                return cursor.rowcount

            rowcount = await self._db_thread.execute(_execute_update)
            return rowcount > 0
        except Exception as e:
            logger.error(f"执行更新失败: {str(e)}, 查询: {query}, 参数: {params}")
            return False

    # 文件元数据相关方法

    async def add_file_metadata(self, file_id: str, filename: str, collection_type: str,
                               file_size: int, file_type: str, metadata: Dict = None) -> bool:
        """添加文件元数据"""
        try:
            created_at = time.time()
            metadata_json = json.dumps(metadata) if metadata else "{}"

            query = '''
            INSERT INTO file_metadata
            (id, filename, collection_type, created_at, file_size, file_type, vectorized, metadata)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            '''

            params = (file_id, filename, collection_type, created_at, file_size, file_type, False, metadata_json)

            return await self.execute_update(query, params)
        except Exception as e:
            logger.error(f"添加文件元数据失败: {str(e)}")
            return False

    async def get_file_metadata(self, file_id: str) -> Optional[Dict]:
        """获取文件元数据"""
        try:
            query = "SELECT * FROM file_metadata WHERE id = ?"
            params = (file_id,)

            row = await self.execute_query(query, params, fetch_one=True)
            if not row:
                return None

            metadata = json.loads(row['metadata']) if row['metadata'] else {}

            return {
                'id': row['id'],
                'filename': row['filename'],
                'collection_type': row['collection_type'],
                'created_at': row['created_at'],
                'file_size': row['file_size'],
                'file_type': row['file_type'],
                'vectorized': bool(row['vectorized']),
                'metadata': metadata
            }
        except Exception as e:
            logger.error(f"获取文件元数据失败: {str(e)}")
            return None

    async def update_file_vectorized(self, file_id: str, vectorized: bool) -> bool:
        """更新文件向量化状态"""
        try:
            query = "UPDATE file_metadata SET vectorized = ? WHERE id = ?"
            params = (vectorized, file_id)

            return await self.execute_update(query, params)
        except Exception as e:
            logger.error(f"更新文件向量化状态失败: {str(e)}")
            return False

    async def delete_file_metadata(self, file_id: str) -> bool:
        """删除文件元数据"""
        try:
            query = "DELETE FROM file_metadata WHERE id = ?"
            params = (file_id,)

            return await self.execute_update(query, params)
        except Exception as e:
            logger.error(f"删除文件元数据失败: {str(e)}")
            return False

    async def get_files_by_collection(self, collection_type: str, vectorized: Optional[bool] = None) -> List[Dict]:
        """获取指定集合类型的文件列表"""
        try:
            if vectorized is None:
                query = "SELECT * FROM file_metadata WHERE collection_type = ? ORDER BY created_at DESC"
                params = (collection_type,)
            else:
                query = "SELECT * FROM file_metadata WHERE collection_type = ? AND vectorized = ? ORDER BY created_at DESC"
                params = (collection_type, vectorized)

            rows = await self.execute_query(query, params)
            if not rows:
                return []

            result = []
            for row in rows:
                metadata = json.loads(row['metadata']) if row['metadata'] else {}
                result.append({
                    'id': row['id'],
                    'filename': row['filename'],
                    'collection_type': row['collection_type'],
                    'created_at': row['created_at'],
                    'file_size': row['file_size'],
                    'file_type': row['file_type'],
                    'vectorized': bool(row['vectorized']),
                    'metadata': metadata
                })

            return result
        except Exception as e:
            logger.error(f"获取文件列表失败: {str(e)}")
            return []

    # 兼容旧版本的API
    async def list_files(self, collection_type: str, vectorized: Optional[bool] = None) -> List[Dict]:
        """获取指定集合类型的文件列表（兼容旧版本）"""
        return await self.get_files_by_collection(collection_type, vectorized)

    async def add_file(self, filename: str, collection_type: str, file_path: str, size: int, mime_type: str, metadata: Dict = None) -> str:
        """添加文件记录"""
        try:
            # 生成唯一文件ID
            import uuid
            file_id = str(uuid.uuid4())

            # 准备元数据
            file_metadata = {"file_path": file_path}

            # 如果提供了元数据，合并到file_metadata
            if metadata:
                file_metadata.update(metadata)

            # 添加文件元数据
            await self.add_file_metadata(
                file_id=file_id,
                filename=filename,
                collection_type=collection_type,
                file_size=size,
                file_type=mime_type,
                metadata=file_metadata
            )

            return file_id
        except Exception as e:
            logger.error(f"添加文件记录失败: {str(e)}")
            raise

    async def get_file_by_id(self, file_id: str) -> Optional[Dict]:
        """获取指定ID的文件信息"""
        try:
            # 获取文件元数据
            file_metadata = await self.get_file_metadata(file_id)
            if not file_metadata:
                return None

            # 构建响应格式
            return {
                "id": file_metadata["id"],
                "filename": file_metadata["filename"],
                "collection_type": file_metadata["collection_type"],
                "size": file_metadata["file_size"],
                "mime_type": file_metadata["file_type"],
                "upload_timestamp": file_metadata["created_at"],
                "vectorized": file_metadata["vectorized"],
                "file_path": file_metadata["metadata"].get("file_path", "")
            }
        except Exception as e:
            logger.error(f"获取文件信息失败: {str(e)}")
            return None

    async def delete_file(self, file_id: str) -> bool:
        """删除文件记录"""
        try:
            return await self.delete_file_metadata(file_id)
        except Exception as e:
            logger.error(f"删除文件记录失败: {str(e)}")
            return False

    # API密钥相关方法

    async def add_api_key(self, api_key: str, client_name: str, permissions: List[str],
                         rate_limit: int = 100, metadata: Dict = None) -> bool:
        """添加API密钥"""
        try:
            created_at = time.time()
            permissions_json = json.dumps(permissions)
            metadata_json = json.dumps(metadata) if metadata else "{}"

            query = '''
            INSERT INTO api_keys
            (api_key, client_name, created_at, permissions, rate_limit, is_active, metadata)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            '''

            params = (api_key, client_name, created_at, permissions_json, rate_limit, True, metadata_json)

            return await self.execute_update(query, params)
        except Exception as e:
            logger.error(f"添加API密钥失败: {str(e)}")
            return False

    async def get_api_key(self, api_key: str) -> Optional[Dict]:
        """获取API密钥信息"""
        try:
            query = "SELECT * FROM api_keys WHERE api_key = ?"
            params = (api_key,)

            row = await self.execute_query(query, params, fetch_one=True)
            if not row:
                return None

            permissions = json.loads(row['permissions'])
            metadata = json.loads(row['metadata']) if row['metadata'] else {}

            return {
                'api_key': row['api_key'],
                'client_name': row['client_name'],
                'created_at': row['created_at'],
                'permissions': permissions,
                'rate_limit': row['rate_limit'],
                'is_active': bool(row['is_active']),
                'metadata': metadata
            }
        except Exception as e:
            logger.error(f"获取API密钥信息失败: {str(e)}")
            return None

    async def update_api_key(self, api_key: str, client_name: Optional[str] = None,
                           permissions: Optional[List[str]] = None, rate_limit: Optional[int] = None,
                           is_active: Optional[bool] = None, metadata: Optional[Dict] = None) -> bool:
        """更新API密钥信息"""
        try:
            # 获取当前API密钥信息
            current_key = await self.get_api_key(api_key)
            if not current_key:
                return False

            # 准备更新字段
            updates = []
            params = []

            if client_name is not None:
                updates.append("client_name = ?")
                params.append(client_name)

            if permissions is not None:
                updates.append("permissions = ?")
                params.append(json.dumps(permissions))

            if rate_limit is not None:
                updates.append("rate_limit = ?")
                params.append(rate_limit)

            if is_active is not None:
                updates.append("is_active = ?")
                params.append(is_active)

            if metadata is not None:
                updates.append("metadata = ?")
                params.append(json.dumps(metadata))

            if not updates:
                return True  # 没有需要更新的字段

            # 构建更新查询
            query = f"UPDATE api_keys SET {', '.join(updates)} WHERE api_key = ?"
            params.append(api_key)

            return await self.execute_update(query, tuple(params))
        except Exception as e:
            logger.error(f"更新API密钥信息失败: {str(e)}")
            return False

    async def delete_api_key(self, api_key: str) -> bool:
        """删除API密钥"""
        try:
            query = "DELETE FROM api_keys WHERE api_key = ?"
            params = (api_key,)

            return await self.execute_update(query, params)
        except Exception as e:
            logger.error(f"删除API密钥失败: {str(e)}")
            return False

    async def get_all_api_keys(self) -> List[Dict]:
        """获取所有API密钥"""
        try:
            query = "SELECT * FROM api_keys ORDER BY created_at DESC"

            rows = await self.execute_query(query)
            if not rows:
                return []

            result = []
            for row in rows:
                permissions = json.loads(row['permissions'])
                metadata = json.loads(row['metadata']) if row['metadata'] else {}

                # 隐藏完整API密钥
                api_key = row['api_key']
                masked_key = f"{api_key[:5]}*****{api_key[-5:]}" if len(api_key) > 10 else "**********"

                result.append({
                    'api_key': masked_key,
                    'client_name': row['client_name'],
                    'created_at': row['created_at'],
                    'permissions': permissions,
                    'rate_limit': row['rate_limit'],
                    'is_active': bool(row['is_active']),
                    'metadata': metadata
                })

            return result
        except Exception as e:
            logger.error(f"获取所有API密钥失败: {str(e)}")
            return []