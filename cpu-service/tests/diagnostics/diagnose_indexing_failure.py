#!/usr/bin/env python3
"""
索引构建失败诊断脚本

深入分析索引构建失败的根本原因
"""

import logging
import os
import sys
import traceback
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_milvus_vector_store_creation():
    """测试MilvusVectorStore创建"""
    logger.info("🔍 测试MilvusVectorStore创建...")
    
    try:
        from llama_index.vector_stores.milvus import MilvusVectorStore
        from shared.clients.gpu_service_client import get_production_sparse_embedding_function
        from shared.config.settings import get_settings
        
        settings = get_settings()
        
        # 测试基本参数
        logger.info(f"Milvus URI: {settings.MILVUS_URI}")
        logger.info(f"Milvus维度: {settings.MILVUS_DIMENSION}")
        
        # 获取稀疏嵌入函数
        sparse_function = get_production_sparse_embedding_function()
        logger.info("✅ GPU稀疏嵌入函数获取成功")
        
        # 尝试创建MilvusVectorStore
        test_collection = "test_indexing_collection"
        
        vector_store_kwargs = {
            "uri": settings.MILVUS_URI,
            "collection_name": test_collection,
            "dim": settings.MILVUS_DIMENSION,
            "enable_sparse": True,
            "sparse_embedding_function": sparse_function,
            "hybrid_ranker": "RRFRanker",
            "hybrid_ranker_params": {"k": 60}
        }
        
        logger.info(f"创建MilvusVectorStore参数: {vector_store_kwargs}")
        
        vector_store = MilvusVectorStore(**vector_store_kwargs)
        logger.info("✅ MilvusVectorStore创建成功")
        
        return True, vector_store
        
    except Exception as e:
        logger.error(f"❌ MilvusVectorStore创建失败: {str(e)}")
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False, None


def test_vector_store_index_creation():
    """测试VectorStoreIndex创建"""
    logger.info("🔍 测试VectorStoreIndex创建...")
    
    try:
        from llama_index.core import VectorStoreIndex
        from shared.clients.embedding_client import CustomAPIEmbedding
        from shared.config.settings import get_settings
        
        settings = get_settings()
        
        # 创建MilvusVectorStore
        success, vector_store = test_milvus_vector_store_creation()
        if not success:
            return False, "MilvusVectorStore创建失败"
        
        # 创建嵌入模型
        embed_model = CustomAPIEmbedding(
            model=settings.API_EMBED_MODEL,
            api_key=settings.API_EMBED_KEY,
            api_base=settings.API_EMBED_BASE,
            dimensions=settings.MILVUS_DIMENSION
        )
        logger.info("✅ 嵌入模型创建成功")
        
        # 创建VectorStoreIndex
        index = VectorStoreIndex.from_vector_store(
            vector_store=vector_store,
            embed_model=embed_model
        )
        logger.info("✅ VectorStoreIndex创建成功")
        
        return True, index
        
    except Exception as e:
        logger.error(f"❌ VectorStoreIndex创建失败: {str(e)}")
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False, str(e)


def test_document_insertion():
    """测试文档插入"""
    logger.info("🔍 测试文档插入...")
    
    try:
        from llama_index.core import Document
        from llama_index.core.node_parser import SentenceSplitter
        
        # 创建索引
        success, index = test_vector_store_index_creation()
        if not success:
            return False, f"索引创建失败: {index}"
        
        # 创建测试文档
        test_documents = [
            Document(
                text="这是一个测试文档，用于验证索引功能。包含食品安全、城市管理等关键词。",
                metadata={"source": "test", "type": "test_document", "category": "功能测试"}
            ),
            Document(
                text="第二个测试文档，用于验证批量索引构建功能。",
                metadata={"source": "test", "type": "test_document", "category": "批量测试"}
            )
        ]
        logger.info(f"✅ 测试文档创建成功: {len(test_documents)} 个文档")
        
        # 解析文档为节点
        node_parser = SentenceSplitter(chunk_size=512, chunk_overlap=50)
        nodes = node_parser.get_nodes_from_documents(test_documents)
        logger.info(f"✅ 文档解析成功: {len(nodes)} 个节点")
        
        # 插入节点
        logger.info("开始插入节点...")
        index.insert_nodes(nodes)
        logger.info("✅ 节点插入成功")
        
        return True, "文档插入成功"
        
    except Exception as e:
        logger.error(f"❌ 文档插入失败: {str(e)}")
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False, str(e)


def test_milvus_connection():
    """测试Milvus连接"""
    logger.info("🔍 测试Milvus连接...")
    
    try:
        from shared.clients.milvus_client import get_milvus_client
        
        milvus_client = get_milvus_client()
        logger.info("✅ Milvus客户端获取成功")
        
        # 检查连接状态
        if hasattr(milvus_client, '_offline_mode') and milvus_client._offline_mode:
            logger.warning("⚠️ Milvus客户端处于离线模式")
            return False, "Milvus处于离线模式"
        
        # 尝试列出集合
        collections = milvus_client.list_collections()
        logger.info(f"✅ Milvus连接正常，现有集合: {collections}")
        
        return True, collections
        
    except Exception as e:
        logger.error(f"❌ Milvus连接测试失败: {str(e)}")
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False, str(e)


def test_collection_operations():
    """测试集合操作"""
    logger.info("🔍 测试集合操作...")

    try:
        from shared.clients.milvus_client import get_milvus_client
        from shared.config.settings import get_settings

        settings = get_settings()
        milvus_client = get_milvus_client()

        test_collection = "test_indexing_collection"

        # 检查集合是否存在并删除（为LlamaIndex让路）
        collections = milvus_client.list_collections()
        if test_collection in collections:
            logger.info(f"集合 {test_collection} 已存在，删除以让LlamaIndex重新创建...")
            try:
                milvus_client.drop_collection(test_collection)
                logger.info(f"集合 {test_collection} 删除成功")
            except Exception as delete_e:
                logger.warning(f"集合删除失败: {str(delete_e)}")

        # 不再手动创建集合，让LlamaIndex自己创建
        logger.info("✅ 集合操作准备完成，将由LlamaIndex创建集合")
        return True, "集合操作准备成功"

    except Exception as e:
        logger.error(f"❌ 集合操作失败: {str(e)}")
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False, str(e)


def run_comprehensive_diagnosis():
    """运行综合诊断"""
    logger.info("🎯 开始索引构建失败综合诊断...")
    
    # 诊断结果
    diagnosis_results = {}
    
    # 1. 测试Milvus连接
    success, result = test_milvus_connection()
    diagnosis_results["milvus_connection"] = {
        "success": success,
        "result": result,
        "description": "Milvus数据库连接测试"
    }
    
    # 2. 测试集合操作
    success, result = test_collection_operations()
    diagnosis_results["collection_operations"] = {
        "success": success,
        "result": result,
        "description": "Milvus集合创建和删除操作测试"
    }
    
    # 3. 测试MilvusVectorStore创建
    success, result = test_milvus_vector_store_creation()
    diagnosis_results["vector_store_creation"] = {
        "success": success,
        "result": "成功" if success else str(result),
        "description": "LlamaIndex MilvusVectorStore创建测试"
    }
    
    # 4. 测试VectorStoreIndex创建
    success, result = test_vector_store_index_creation()
    diagnosis_results["index_creation"] = {
        "success": success,
        "result": "成功" if success else str(result),
        "description": "LlamaIndex VectorStoreIndex创建测试"
    }
    
    # 5. 测试文档插入
    success, result = test_document_insertion()
    diagnosis_results["document_insertion"] = {
        "success": success,
        "result": result,
        "description": "文档节点插入测试"
    }
    
    # 汇总诊断结果
    logger.info("\n" + "="*60)
    logger.info("📊 索引构建失败诊断结果:")
    logger.info("="*60)
    
    total_tests = len(diagnosis_results)
    passed_tests = 0
    
    for test_name, test_info in diagnosis_results.items():
        success = test_info["success"]
        result = test_info["result"]
        description = test_info["description"]
        
        status = "✅ 通过" if success else "❌ 失败"
        logger.info(f"{test_name:25s}: {status}")
        logger.info(f"  描述: {description}")
        logger.info(f"  结果: {result}")
        logger.info("")
        
        if success:
            passed_tests += 1
    
    logger.info(f"诊断通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.0f}%)")
    
    # 分析失败原因
    if passed_tests < total_tests:
        logger.info("\n🔍 失败原因分析:")
        
        if not diagnosis_results["milvus_connection"]["success"]:
            logger.error("❌ 根本原因：Milvus数据库连接失败")
            logger.error("   建议：检查Milvus服务是否运行，网络连接是否正常")
        
        elif not diagnosis_results["collection_operations"]["success"]:
            logger.error("❌ 根本原因：Milvus集合操作失败")
            logger.error("   建议：检查Milvus权限设置，集合创建参数是否正确")
        
        elif not diagnosis_results["vector_store_creation"]["success"]:
            logger.error("❌ 根本原因：MilvusVectorStore创建失败")
            logger.error("   建议：检查LlamaIndex版本兼容性，参数配置是否正确")
        
        elif not diagnosis_results["index_creation"]["success"]:
            logger.error("❌ 根本原因：VectorStoreIndex创建失败")
            logger.error("   建议：检查嵌入模型配置，向量存储初始化是否正确")
        
        elif not diagnosis_results["document_insertion"]["success"]:
            logger.error("❌ 根本原因：文档插入失败")
            logger.error("   建议：检查文档格式，节点解析是否正确")
    
    else:
        logger.info("🎉 所有诊断测试通过！索引构建应该能够正常工作")
    
    return diagnosis_results


if __name__ == "__main__":
    try:
        results = run_comprehensive_diagnosis()
        
        # 根据诊断结果决定退出码
        total_tests = len(results)
        passed_tests = sum(1 for r in results.values() if r["success"])
        
        if passed_tests == total_tests:
            logger.info("✅ 诊断完成：所有测试通过")
            sys.exit(0)
        else:
            logger.error("❌ 诊断完成：存在失败的测试")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("用户中断诊断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"诊断过程中发生错误: {str(e)}")
        logger.error(f"详细错误: {traceback.format_exc()}")
        sys.exit(1)
