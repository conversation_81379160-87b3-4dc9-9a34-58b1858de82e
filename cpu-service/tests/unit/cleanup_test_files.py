#!/usr/bin/env python3
"""
测试文件清理脚本

识别并清理不应该出现在生产环境中的测试文件
"""

import os
import logging
import shutil
from pathlib import Path
from typing import List, Dict, Any

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def identify_test_files(project_root: str) -> Dict[str, List[str]]:
    """识别测试文件"""
    test_files = {
        "临时测试文件": [],
        "诊断脚本": [],
        "分析脚本": [],
        "验证脚本": [],
        "调试文件": [],
        "其他测试文件": []
    }
    
    # 定义测试文件模式
    test_patterns = {
        "临时测试文件": [
            "test_*.py",
            "*_test.py", 
            "temp_*.py",
            "*_temp.py",
            "debug_*.py",
            "*_debug.py"
        ],
        "诊断脚本": [
            "diagnose_*.py",
            "*_diagnosis.py",
            "check_*.py",
            "*_check.py"
        ],
        "分析脚本": [
            "analyze_*.py",
            "*_analysis.py",
            "report_*.py",
            "*_report.py"
        ],
        "验证脚本": [
            "verify_*.py",
            "*_verification.py",
            "validate_*.py",
            "*_validation.py"
        ]
    }
    
    # 遍历项目目录
    for root, dirs, files in os.walk(project_root):
        # 跳过隐藏目录和常见的非源码目录
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules', 'venv', 'env']]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path, project_root)
                
                # 检查是否匹配测试文件模式
                categorized = False
                for category, patterns in test_patterns.items():
                    for pattern in patterns:
                        if pattern.replace('*', '') in file.lower():
                            test_files[category].append(relative_path)
                            categorized = True
                            break
                    if categorized:
                        break
                
                # 检查文件内容是否包含测试相关关键词
                if not categorized:
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                        # 检查是否为测试文件
                        test_keywords = [
                            '# 测试', '# Test', '测试脚本', 'test script',
                            '诊断', 'diagnosis', '分析', 'analysis',
                            '验证', 'verification', '调试', 'debug'
                        ]
                        
                        if any(keyword in content for keyword in test_keywords):
                            # 进一步分类
                            if '诊断' in content or 'diagnosis' in content:
                                test_files["诊断脚本"].append(relative_path)
                            elif '分析' in content or 'analysis' in content:
                                test_files["分析脚本"].append(relative_path)
                            elif '验证' in content or 'verification' in content:
                                test_files["验证脚本"].append(relative_path)
                            elif '调试' in content or 'debug' in content:
                                test_files["调试文件"].append(relative_path)
                            else:
                                test_files["其他测试文件"].append(relative_path)
                                
                    except Exception as e:
                        logger.warning(f"无法读取文件 {relative_path}: {str(e)}")
    
    return test_files


def categorize_files_by_importance(test_files: Dict[str, List[str]]) -> Dict[str, List[str]]:
    """按重要性分类文件"""
    categories = {
        "可以删除": [],
        "移动到tests目录": [],
        "保留但重命名": [],
        "需要手动审查": []
    }
    
    # 定义删除规则
    delete_patterns = [
        "temp_", "_temp", "debug_", "_debug",
        "test_temp", "quick_test", "scratch"
    ]
    
    # 定义保留规则
    keep_patterns = [
        "final_", "acceptance_", "integration_",
        "performance_", "load_", "stress_"
    ]
    
    for category, files in test_files.items():
        for file_path in files:
            file_name = os.path.basename(file_path).lower()
            
            # 检查是否应该删除
            should_delete = any(pattern in file_name for pattern in delete_patterns)
            
            # 检查是否应该保留
            should_keep = any(pattern in file_name for pattern in keep_patterns)
            
            if should_delete:
                categories["可以删除"].append(file_path)
            elif should_keep:
                categories["移动到tests目录"].append(file_path)
            elif category in ["诊断脚本", "分析脚本"]:
                categories["移动到tests目录"].append(file_path)
            elif category in ["验证脚本"]:
                categories["保留但重命名"].append(file_path)
            else:
                categories["需要手动审查"].append(file_path)
    
    return categories


def create_tests_directory(project_root: str) -> str:
    """创建tests目录"""
    tests_dir = os.path.join(project_root, "tests")
    
    if not os.path.exists(tests_dir):
        os.makedirs(tests_dir)
        logger.info(f"创建tests目录: {tests_dir}")
    
    # 创建子目录
    subdirs = ["unit", "integration", "performance", "diagnostics", "analysis"]
    for subdir in subdirs:
        subdir_path = os.path.join(tests_dir, subdir)
        if not os.path.exists(subdir_path):
            os.makedirs(subdir_path)
            logger.info(f"创建子目录: {subdir_path}")
    
    return tests_dir


def move_files_to_tests(files: List[str], project_root: str, tests_dir: str):
    """移动文件到tests目录"""
    for file_path in files:
        source = os.path.join(project_root, file_path)
        
        # 确定目标子目录
        file_name = os.path.basename(file_path).lower()
        if 'diagnose' in file_name or '诊断' in file_name:
            target_subdir = "diagnostics"
        elif 'analyze' in file_name or '分析' in file_name:
            target_subdir = "analysis"
        elif 'performance' in file_name or '性能' in file_name:
            target_subdir = "performance"
        elif 'integration' in file_name or '集成' in file_name:
            target_subdir = "integration"
        else:
            target_subdir = "unit"
        
        target_dir = os.path.join(tests_dir, target_subdir)
        target = os.path.join(target_dir, os.path.basename(file_path))
        
        try:
            shutil.move(source, target)
            logger.info(f"移动文件: {file_path} -> tests/{target_subdir}/{os.path.basename(file_path)}")
        except Exception as e:
            logger.error(f"移动文件失败 {file_path}: {str(e)}")


def delete_files(files: List[str], project_root: str):
    """删除文件"""
    for file_path in files:
        full_path = os.path.join(project_root, file_path)
        try:
            os.remove(full_path)
            logger.info(f"删除文件: {file_path}")
        except Exception as e:
            logger.error(f"删除文件失败 {file_path}: {str(e)}")


def generate_cleanup_report(before_files: Dict[str, List[str]], after_actions: Dict[str, List[str]]) -> str:
    """生成清理报告"""
    report = []
    report.append("="*80)
    report.append("测试文件清理报告")
    report.append("="*80)
    
    # 清理前统计
    report.append("\n📊 清理前文件统计:")
    total_before = 0
    for category, files in before_files.items():
        if files:
            report.append(f"  {category}: {len(files)} 个文件")
            total_before += len(files)
    report.append(f"  总计: {total_before} 个测试文件")
    
    # 清理操作
    report.append("\n🔧 执行的清理操作:")
    for action, files in after_actions.items():
        if files:
            report.append(f"  {action}: {len(files)} 个文件")
            for file_path in files[:5]:  # 只显示前5个
                report.append(f"    - {file_path}")
            if len(files) > 5:
                report.append(f"    ... 还有 {len(files) - 5} 个文件")
    
    # 建议
    report.append("\n💡 生产环境建议:")
    report.append("  • 定期运行此清理脚本")
    report.append("  • 将重要的测试文件移动到tests目录")
    report.append("  • 使用.gitignore忽略临时测试文件")
    report.append("  • 建立代码审查流程，防止测试文件进入生产")
    
    return "\n".join(report)


def main():
    """主函数"""
    logger.info("🎯 开始测试文件清理...")
    
    # 获取项目根目录
    project_root = os.path.dirname(os.path.abspath(__file__))
    logger.info(f"项目根目录: {project_root}")
    
    # 1. 识别测试文件
    logger.info("🔍 识别测试文件...")
    test_files = identify_test_files(project_root)
    
    # 2. 按重要性分类
    logger.info("📂 按重要性分类文件...")
    categorized_files = categorize_files_by_importance(test_files)
    
    # 3. 显示分析结果
    logger.info("\n" + "="*60)
    logger.info("📋 测试文件分析结果:")
    logger.info("="*60)
    
    for category, files in test_files.items():
        if files:
            logger.info(f"\n{category}: {len(files)} 个文件")
            for file_path in files:
                logger.info(f"  - {file_path}")
    
    logger.info("\n" + "="*60)
    logger.info("📊 清理建议:")
    logger.info("="*60)
    
    for action, files in categorized_files.items():
        if files:
            logger.info(f"\n{action}: {len(files)} 个文件")
            for file_path in files[:3]:  # 只显示前3个
                logger.info(f"  - {file_path}")
            if len(files) > 3:
                logger.info(f"  ... 还有 {len(files) - 3} 个文件")
    
    # 4. 询问是否执行清理
    logger.info("\n🤔 是否执行清理操作？")
    logger.info("注意：这将实际删除和移动文件！")
    
    # 在脚本中自动执行（生产环境中应该要求确认）
    execute_cleanup = True  # 在实际使用中，这里应该是用户输入
    
    if execute_cleanup:
        logger.info("✅ 开始执行清理操作...")
        
        # 创建tests目录
        tests_dir = create_tests_directory(project_root)
        
        # 执行清理操作
        if categorized_files["可以删除"]:
            logger.info("🗑️ 删除临时文件...")
            delete_files(categorized_files["可以删除"], project_root)
        
        if categorized_files["移动到tests目录"]:
            logger.info("📁 移动文件到tests目录...")
            move_files_to_tests(categorized_files["移动到tests目录"], project_root, tests_dir)
        
        # 生成报告
        report = generate_cleanup_report(test_files, categorized_files)
        logger.info("\n" + report)
        
        # 保存报告
        report_file = os.path.join(project_root, "cleanup_report.txt")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        logger.info(f"📄 清理报告已保存: {report_file}")
        
        logger.info("✅ 清理操作完成！")
    else:
        logger.info("❌ 用户取消清理操作")
    
    return categorized_files


if __name__ == "__main__":
    try:
        result = main()
        logger.info("📋 测试文件清理完成")
    except KeyboardInterrupt:
        logger.info("用户中断清理")
    except Exception as e:
        logger.error(f"清理过程中发生错误: {str(e)}")
        raise
