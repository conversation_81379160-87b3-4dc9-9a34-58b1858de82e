#!/usr/bin/env python3
"""
最终验收测试

验证所有三个优先级问题的解决情况
"""

import asyncio
import logging
import requests
import json
import time
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# API配置
API_BASE_URL = "http://localhost:8009"
API_V2_BASE = f"{API_BASE_URL}/v2"


def test_priority_1_gpu_integration():
    """验收测试：优先级1 - 检索服务GPU集成"""
    logger.info("🎯 验收测试：优先级1 - 检索服务GPU集成")
    
    try:
        # 测试真实向量检索
        search_request = {
            "query": "食品安全监管问题",
            "collection": "department",
            "top_k": 3
        }
        
        response = requests.post(
            f"{API_V2_BASE}/retrieval/search",
            json=search_request,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            results = data.get('results', [])
            
            # 验证结果真实性
            if results and len(results) > 0:
                first_result = results[0]
                result_text = first_result.get('text', '')
                result_id = first_result.get('id', '')
                result_score = first_result.get('score', 0)
                
                # 检查是否为真实数据（非模拟）
                is_real = "模拟" not in result_text and "offline" not in result_id.lower()
                has_score = result_score > 0
                
                if is_real and has_score:
                    logger.info("✅ 优先级1验收通过：检索结果来自真实向量计算")
                    logger.info(f"   结果数量: {len(results)}")
                    logger.info(f"   最高分数: {result_score:.4f}")
                    return True
                else:
                    logger.error("❌ 优先级1验收失败：检索结果仍为模拟数据")
                    return False
            else:
                logger.error("❌ 优先级1验收失败：无检索结果")
                return False
        else:
            logger.error(f"❌ 优先级1验收失败：HTTP {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 优先级1验收异常: {str(e)}")
        return False


def test_priority_2_indexing_functionality():
    """验收测试：优先级2 - 索引测试功能"""
    logger.info("🎯 验收测试：优先级2 - 索引测试功能")
    
    try:
        response = requests.post(f"{API_V2_BASE}/indexing/test", timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            status = data.get('status', 'unknown')
            
            # 验证状态一致性
            if status in ['success', 'partial_success']:
                # 检查嵌套的test_details
                test_result = data.get('test_result', {})
                test_details = test_result.get('test_details', data.get('test_details', {}))

                # 检查关键组件测试
                embedding_test = test_details.get('embedding_model', '').startswith('✅')
                gpu_test = test_details.get('gpu_sparse_embedding', '').startswith('✅')
                dense_test = test_details.get('dense_embedding', '').startswith('✅')
                
                if embedding_test and gpu_test and dense_test:
                    logger.info("✅ 优先级2验收通过：索引功能正常工作")
                    logger.info(f"   测试状态: {status}")
                    logger.info(f"   嵌入模型: {'通过' if embedding_test else '失败'}")
                    logger.info(f"   GPU稀疏嵌入: {'通过' if gpu_test else '失败'}")
                    logger.info(f"   密集嵌入: {'通过' if dense_test else '失败'}")
                    return True
                else:
                    logger.error("❌ 优先级2验收失败：关键组件测试未通过")
                    return False
            else:
                logger.error(f"❌ 优先级2验收失败：索引测试状态异常 - {status}")
                return False
        else:
            logger.error(f"❌ 优先级2验收失败：HTTP {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 优先级2验收异常: {str(e)}")
        return False


def test_priority_3_api_optimization():
    """验收测试：优先级3 - API接口重构优化"""
    logger.info("🎯 验收测试：优先级3 - API接口重构优化")
    
    try:
        # 测试关键端点的标准化字段名
        endpoints_to_test = [
            (API_BASE_URL, ["欢迎", "状态", "架构"]),  # 根路径保持中文字段
            (f"{API_V2_BASE}/system/health", ["status", "service_name", "component_status"]),  # 统一健康检查端点
            (f"{API_V2_BASE}/", ["状态", "API版本", "核心功能"]),  # V2根路径保持中文字段
            (f"{API_V2_BASE}/system/status", ["service_name", "component_status", "performance_metrics"])  # 状态端点使用英文字段名
        ]
        
        chinese_score = 0
        total_endpoints = len(endpoints_to_test)
        
        for url, required_keys in endpoints_to_test:
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    
                    # 检查中文字段
                    found_keys = [key for key in required_keys if key in data]
                    if len(found_keys) == len(required_keys):
                        chinese_score += 1
                    
            except Exception:
                pass
        
        # 测试API文档可访问性
        docs_accessible = True
        try:
            docs_response = requests.get(f"{API_BASE_URL}/docs", timeout=10)
            redoc_response = requests.get(f"{API_BASE_URL}/redoc", timeout=10)
            docs_accessible = docs_response.status_code == 200 and redoc_response.status_code == 200
        except Exception:
            docs_accessible = False
        
        # 验收标准：80%端点中文化 + 文档可访问
        chinese_rate = chinese_score / total_endpoints
        
        if chinese_rate >= 0.8 and docs_accessible:
            logger.info("✅ 优先级3验收通过：API接口重构优化完成")
            logger.info(f"   中文化率: {chinese_rate*100:.0f}%")
            logger.info(f"   API文档: {'可访问' if docs_accessible else '不可访问'}")
            return True
        else:
            logger.error("❌ 优先级3验收失败：API优化未达标")
            logger.error(f"   中文化率: {chinese_rate*100:.0f}% (需要≥80%)")
            logger.error(f"   API文档: {'可访问' if docs_accessible else '不可访问'}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 优先级3验收异常: {str(e)}")
        return False


def test_overall_performance():
    """验收测试：整体性能"""
    logger.info("🎯 验收测试：整体性能")
    
    try:
        # 测试响应时间
        start_time = time.time()
        
        # 并行检索测试
        parallel_request = {"query": "食品安全监管问题"}
        response = requests.post(
            f"{API_V2_BASE}/retrieval/search/parallel",
            json=parallel_request,
            timeout=30
        )
        
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            processing_time = data.get('total_processing_time', 0)
            
            # 验收标准：响应时间<3秒
            if response_time < 3.0:
                logger.info("✅ 整体性能验收通过")
                logger.info(f"   响应时间: {response_time:.3f}秒")
                logger.info(f"   处理时间: {processing_time:.3f}秒")
                return True
            else:
                logger.error(f"❌ 整体性能验收失败：响应时间过长 ({response_time:.3f}秒)")
                return False
        else:
            logger.error(f"❌ 整体性能验收失败：HTTP {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 整体性能验收异常: {str(e)}")
        return False


def test_system_stability():
    """验收测试：系统稳定性"""
    logger.info("🎯 验收测试：系统稳定性")
    
    try:
        # 连续测试多个端点
        endpoints = [
            f"{API_BASE_URL}/health",
            f"{API_V2_BASE}/status",
            f"{API_V2_BASE}/retrieval/config",
            f"{API_V2_BASE}/indexing/config"
        ]
        
        stable_count = 0
        total_tests = len(endpoints)
        
        for endpoint in endpoints:
            try:
                response = requests.get(endpoint, timeout=10)
                if response.status_code == 200:
                    stable_count += 1
            except Exception:
                pass
        
        stability_rate = stable_count / total_tests
        
        if stability_rate >= 0.9:  # 90%稳定性
            logger.info("✅ 系统稳定性验收通过")
            logger.info(f"   稳定性: {stability_rate*100:.0f}%")
            return True
        else:
            logger.error(f"❌ 系统稳定性验收失败：稳定性不足 ({stability_rate*100:.0f}%)")
            return False
            
    except Exception as e:
        logger.error(f"❌ 系统稳定性验收异常: {str(e)}")
        return False


def main():
    """最终验收测试主函数"""
    logger.info("🏆 开始最终验收测试...")
    logger.info("="*60)
    
    # 验收测试结果
    acceptance_results = {}
    
    # 优先级1验收
    acceptance_results["priority_1_gpu_integration"] = test_priority_1_gpu_integration()
    
    # 优先级2验收
    acceptance_results["priority_2_indexing_functionality"] = test_priority_2_indexing_functionality()
    
    # 优先级3验收
    acceptance_results["priority_3_api_optimization"] = test_priority_3_api_optimization()
    
    # 整体性能验收
    acceptance_results["overall_performance"] = test_overall_performance()
    
    # 系统稳定性验收
    acceptance_results["system_stability"] = test_system_stability()
    
    # 汇总验收结果
    logger.info("\n" + "="*60)
    logger.info("🏆 最终验收测试结果:")
    logger.info("="*60)
    
    for test_name, passed in acceptance_results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        logger.info(f"{test_name:35s}: {status}")
    
    total_tests = len(acceptance_results)
    passed_tests = sum(acceptance_results.values())
    
    logger.info(f"\n验收通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.0f}%)")
    
    # 最终判定
    if passed_tests == total_tests:
        logger.info("\n🎉🎉🎉 恭喜！所有验收测试通过！ 🎉🎉🎉")
        logger.info("✅ 优先级1：检索服务GPU集成问题已完全解决")
        logger.info("✅ 优先级2：索引测试功能修复已完全解决")
        logger.info("✅ 优先级3：API接口重构优化已完全解决")
        logger.info("✅ 系统性能和稳定性达到验收标准")
        logger.info("\n🚀 CPU服务向量化和检索API优化完成！")
        return True
    else:
        logger.error("\n❌ 部分验收测试未通过，需要进一步优化")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("用户中断测试")
        sys.exit(1)
    except Exception as e:
        logger.error(f"验收测试过程中发生错误: {str(e)}")
        sys.exit(1)
