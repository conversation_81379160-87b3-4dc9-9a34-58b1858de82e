#!/usr/bin/env python3
"""
API接口结构分析

分析现有API接口，识别重复功能并提出重构建议
"""

import logging
import requests
import json
from typing import Dict, List, Any
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# API配置
API_BASE_URL = "http://localhost:8009"


def get_openapi_spec():
    """获取OpenAPI规范"""
    try:
        response = requests.get(f"{API_BASE_URL}/openapi.json", timeout=10)
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"获取OpenAPI规范失败: HTTP {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"获取OpenAPI规范异常: {str(e)}")
        return None


def analyze_api_endpoints(openapi_spec):
    """分析API端点"""
    if not openapi_spec or 'paths' not in openapi_spec:
        return {}
    
    endpoints = {}
    paths = openapi_spec['paths']
    
    for path, methods in paths.items():
        for method, details in methods.items():
            if method.upper() in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']:
                endpoint_key = f"{method.upper()} {path}"
                endpoints[endpoint_key] = {
                    "path": path,
                    "method": method.upper(),
                    "summary": details.get('summary', ''),
                    "description": details.get('description', ''),
                    "tags": details.get('tags', []),
                    "parameters": details.get('parameters', []),
                    "requestBody": details.get('requestBody', {}),
                    "responses": details.get('responses', {})
                }
    
    return endpoints


def categorize_endpoints(endpoints):
    """对端点进行分类"""
    categories = {
        "系统信息": [],
        "健康检查": [],
        "检索服务": [],
        "索引服务": [],
        "文件服务": [],
        "其他": []
    }
    
    for endpoint_key, details in endpoints.items():
        path = details['path']
        method = details['method']
        summary = details['summary']
        
        # 根据路径和功能分类
        if path in ['/', '/v2/', '/v2']:
            categories["系统信息"].append((endpoint_key, details))
        elif 'health' in path:
            categories["健康检查"].append((endpoint_key, details))
        elif 'retrieval' in path:
            categories["检索服务"].append((endpoint_key, details))
        elif 'indexing' in path:
            categories["索引服务"].append((endpoint_key, details))
        elif 'files' in path:
            categories["文件服务"].append((endpoint_key, details))
        else:
            categories["其他"].append((endpoint_key, details))
    
    return categories


def identify_duplicate_functionality(categories):
    """识别重复功能"""
    duplicates = []
    
    # 检查系统信息类重复
    system_info_endpoints = categories["系统信息"]
    if len(system_info_endpoints) > 1:
        duplicates.append({
            "type": "功能重复",
            "category": "系统信息",
            "endpoints": [ep[0] for ep in system_info_endpoints],
            "description": "多个端点提供系统信息，功能重叠",
            "recommendation": "合并为单一的系统信息端点"
        })
    
    # 检查健康检查类重复
    health_endpoints = categories["健康检查"]
    if len(health_endpoints) > 1:
        duplicates.append({
            "type": "功能重复",
            "category": "健康检查",
            "endpoints": [ep[0] for ep in health_endpoints],
            "description": "多个健康检查端点，功能重叠",
            "recommendation": "保留一个主要的健康检查端点"
        })
    
    # 检查检索服务内部重复
    retrieval_endpoints = categories["检索服务"]
    retrieval_paths = [ep[1]['path'] for ep in retrieval_endpoints]
    
    # 检查是否有相似的检索端点
    similar_retrieval = []
    for i, (key1, details1) in enumerate(retrieval_endpoints):
        for j, (key2, details2) in enumerate(retrieval_endpoints[i+1:], i+1):
            if ('search' in details1['path'] and 'search' in details2['path']) or \
               ('config' in details1['path'] and 'config' in details2['path']):
                similar_retrieval.append((key1, key2))
    
    if similar_retrieval:
        duplicates.append({
            "type": "功能相似",
            "category": "检索服务",
            "endpoints": [pair for pair in similar_retrieval],
            "description": "检索服务中存在功能相似的端点",
            "recommendation": "审查并合并相似功能"
        })
    
    return duplicates


def analyze_api_design_issues(endpoints):
    """分析API设计问题"""
    issues = []
    
    # 检查RESTful设计
    non_restful = []
    for endpoint_key, details in endpoints.items():
        path = details['path']
        method = details['method']
        
        # 检查是否有非RESTful的设计
        if method == 'GET' and ('test' in path or 'check' in path):
            non_restful.append(endpoint_key)
        elif method == 'POST' and 'get' in path.lower():
            non_restful.append(endpoint_key)
    
    if non_restful:
        issues.append({
            "type": "RESTful设计问题",
            "endpoints": non_restful,
            "description": "部分端点不符合RESTful设计原则",
            "recommendation": "调整HTTP方法和路径设计"
        })
    
    # 检查路径一致性
    inconsistent_paths = []
    v2_paths = [ep[1]['path'] for ep in endpoints.items() if ep[1]['path'].startswith('/v2/')]
    non_v2_paths = [ep[1]['path'] for ep in endpoints.items() if not ep[1]['path'].startswith('/v2/') and ep[1]['path'] not in ['/', '/docs', '/redoc', '/openapi.json']]
    
    if non_v2_paths:
        issues.append({
            "type": "版本一致性问题",
            "endpoints": non_v2_paths,
            "description": "存在非v2版本的API端点",
            "recommendation": "统一使用v2版本前缀"
        })
    
    return issues


def generate_restructure_plan(categories, duplicates, issues):
    """生成重构计划"""
    plan = {
        "current_structure": {},
        "proposed_structure": {},
        "migration_steps": [],
        "backward_compatibility": []
    }
    
    # 当前结构
    for category, endpoints in categories.items():
        plan["current_structure"][category] = len(endpoints)
    
    # 建议的新结构
    plan["proposed_structure"] = {
        "系统管理": {
            "GET /v2/system/info": "系统信息（合并根路径和v2信息）",
            "GET /v2/system/health": "健康检查（统一健康检查端点）",
            "GET /v2/system/status": "服务状态（详细状态信息）"
        },
        "检索服务": {
            "POST /v2/retrieval/search": "单集合检索",
            "POST /v2/retrieval/search/parallel": "并行检索",
            "GET /v2/retrieval/config": "检索配置",
            "GET /v2/retrieval/collections": "集合信息"
        },
        "索引服务": {
            "POST /v2/indexing/build": "构建索引",
            "GET /v2/indexing/status": "索引状态",
            "GET /v2/indexing/config": "索引配置",
            "POST /v2/indexing/test": "索引测试"
        },
        "文件服务": {
            "POST /v2/files/upload": "文件上传",
            "GET /v2/files/{file_id}": "文件下载",
            "DELETE /v2/files/{file_id}": "文件删除"
        }
    }
    
    # 迁移步骤
    plan["migration_steps"] = [
        "1. 创建新的系统管理端点，合并现有功能",
        "2. 重构检索服务端点，消除重复功能",
        "3. 标准化索引服务端点命名",
        "4. 完善文件服务RESTful设计",
        "5. 添加API版本兼容性处理",
        "6. 更新API文档和客户端代码"
    ]
    
    # 向后兼容性
    plan["backward_compatibility"] = [
        "保留现有端点6个月，添加弃用警告",
        "在响应头中添加新端点的重定向信息",
        "提供迁移指南和示例代码",
        "在文档中明确标注弃用端点"
    ]
    
    return plan


def main():
    """主分析函数"""
    logger.info("🎯 开始API接口结构分析...")
    
    # 1. 获取OpenAPI规范
    logger.info("📋 获取API规范...")
    openapi_spec = get_openapi_spec()
    
    if not openapi_spec:
        logger.error("❌ 无法获取API规范，分析终止")
        return
    
    # 2. 分析端点
    logger.info("🔍 分析API端点...")
    endpoints = analyze_api_endpoints(openapi_spec)
    logger.info(f"发现 {len(endpoints)} 个API端点")
    
    # 3. 分类端点
    logger.info("📂 分类API端点...")
    categories = categorize_endpoints(endpoints)
    
    # 4. 识别重复功能
    logger.info("🔍 识别重复功能...")
    duplicates = identify_duplicate_functionality(categories)
    
    # 5. 分析设计问题
    logger.info("🔍 分析设计问题...")
    issues = analyze_api_design_issues(endpoints)
    
    # 6. 生成重构计划
    logger.info("📋 生成重构计划...")
    restructure_plan = generate_restructure_plan(categories, duplicates, issues)
    
    # 生成报告
    logger.info("\n" + "="*80)
    logger.info("📊 API接口结构分析报告")
    logger.info("="*80)
    
    # 当前结构
    logger.info("\n📂 当前API结构:")
    for category, endpoints in categories.items():
        logger.info(f"  {category}: {len(endpoints)} 个端点")
        for endpoint_key, details in endpoints:
            logger.info(f"    {endpoint_key}: {details['summary']}")
    
    # 重复功能
    logger.info("\n🔍 发现的重复功能:")
    if duplicates:
        for duplicate in duplicates:
            logger.info(f"  类型: {duplicate['type']}")
            logger.info(f"  分类: {duplicate['category']}")
            logger.info(f"  端点: {duplicate['endpoints']}")
            logger.info(f"  描述: {duplicate['description']}")
            logger.info(f"  建议: {duplicate['recommendation']}")
            logger.info("")
    else:
        logger.info("  ✅ 未发现明显的重复功能")
    
    # 设计问题
    logger.info("\n⚠️ 设计问题:")
    if issues:
        for issue in issues:
            logger.info(f"  类型: {issue['type']}")
            logger.info(f"  端点: {issue['endpoints']}")
            logger.info(f"  描述: {issue['description']}")
            logger.info(f"  建议: {issue['recommendation']}")
            logger.info("")
    else:
        logger.info("  ✅ 未发现明显的设计问题")
    
    # 重构建议
    logger.info("\n💡 建议的新API结构:")
    for category, endpoints in restructure_plan["proposed_structure"].items():
        logger.info(f"  {category}:")
        for endpoint, description in endpoints.items():
            logger.info(f"    {endpoint}: {description}")
    
    # 迁移计划
    logger.info("\n🚀 迁移步骤:")
    for step in restructure_plan["migration_steps"]:
        logger.info(f"  {step}")
    
    # 向后兼容性
    logger.info("\n🔄 向后兼容性保证:")
    for item in restructure_plan["backward_compatibility"]:
        logger.info(f"  • {item}")
    
    # 总结
    logger.info("\n🎯 分析总结:")
    logger.info(f"✅ 当前API端点总数: {len(endpoints)}")
    logger.info(f"⚠️ 发现重复功能: {len(duplicates)} 个")
    logger.info(f"⚠️ 发现设计问题: {len(issues)} 个")
    logger.info("📋 建议进行适度重构以提升API质量")
    
    return {
        "endpoints_count": len(endpoints),
        "duplicates_count": len(duplicates),
        "issues_count": len(issues),
        "restructure_needed": len(duplicates) > 0 or len(issues) > 0
    }


if __name__ == "__main__":
    try:
        result = main()
        logger.info(f"\n📋 分析完成，建议重构: {result['restructure_needed']}")
        sys.exit(0)
    except KeyboardInterrupt:
        logger.info("用户中断分析")
        sys.exit(1)
    except Exception as e:
        logger.error(f"分析过程中发生错误: {str(e)}")
        sys.exit(1)
