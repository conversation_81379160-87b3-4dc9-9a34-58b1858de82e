# 核心业务逻辑模块初始化文件
# 模型注册和配置模块
from typing import Dict


from cores.online_reranker import OnlineReranker
from cores.config import my_settings

# 环境变量应该在main.py中已经加载完成
if not my_settings.ENV_LOADED:
    print("警告: 环境变量似乎未在main.py中正确加载，可能会导致配置问题")

from llama_index.core.postprocessor import SentenceTransformerRerank
from llama_index.llms.openai.utils import ALL_AVAILABLE_MODELS, CHAT_MODELS




DOUBAO_MODELS: Dict[str, int] = {
    "doubao-1-5-pro-32k-250115": 128000,
}

DEEPSEEK_MODELS: Dict[str, int] = {
    "deepseek-v3-250324": 128000,
}

# 添加千问模型支持
QWEN_MODELS: Dict[str, int] = {
    "qwen2.5-32b-instruct": 128000,
}

QWEN_MODELS2_5: Dict[str, int] = {
    "Qwen/Qwen2.5-32B-Instruct": 128000,
}

ALL_AVAILABLE_MODELS.update(DEEPSEEK_MODELS)
ALL_AVAILABLE_MODELS.update(QWEN_MODELS)
ALL_AVAILABLE_MODELS.update(DOUBAO_MODELS)
ALL_AVAILABLE_MODELS.update(QWEN_MODELS2_5)
CHAT_MODELS.update(DEEPSEEK_MODELS)
CHAT_MODELS.update(QWEN_MODELS)
CHAT_MODELS.update(DOUBAO_MODELS)
CHAT_MODELS.update(QWEN_MODELS2_5)


# 所有配置现在从 my_settings 对象中获取

# 导入embedding_core中的函数
from cores.embedding_core import (
    get_local_embed_model,
    get_api_embed_model,
    get_llm_model
)


def get_local_reranker():
    """获取本地Reranker实例，从 my_settings 获取配置"""
    reranker = SentenceTransformerRerank(
        model=my_settings.RERANK_MODEL,
        top_n=my_settings.RERANK_TOP_N,
        keep_retrieval_score=True
    )
    return reranker


def get_online_reranker():
    """获取在线Reranker实例，从 my_settings 获取配置"""
    reranker = OnlineReranker(
        api_url=my_settings.API_RERANK_URL,  # 使用环境变量中的URL
        top_n=my_settings.RERANK_TOP_N,
        api_key=my_settings.API_RERANK_KEY,  # 使用环境变量中的API密钥
        model=my_settings.API_RERANK_MODEL,  # 使用环境变量中的模型名称
    )
    return reranker

