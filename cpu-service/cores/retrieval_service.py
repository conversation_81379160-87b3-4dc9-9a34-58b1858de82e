"""
检索服务

基于GPU+CPU分离架构的优化版检索服务
使用优化的Milvus客户端和GPU稀疏嵌入
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional
from functools import lru_cache

from shared.config.settings import get_settings
from shared.clients.milvus_client import get_milvus_client
from shared.clients.gpu_service_client import get_production_sparse_embedding_function

logger = logging.getLogger(__name__)


class OptimizedRetrievalService:
    """
    优化版检索服务

    基于GPU+CPU分离架构，使用优化的Milvus客户端和GPU稀疏嵌入
    """

    def __init__(self):
        """初始化检索服务"""
        self.settings = get_settings()

        # 检索配置
        self.retrieval_config = {
            "department": {
                "top_k": self.settings.RETRIEVAL_TOP_K_DEPARTMENT,
                "dense_weight": 0.8,
                "sparse_weight": 0.2,
                "use_rerank": True
            },
            "guideline": {
                "top_k": self.settings.RETRIEVAL_TOP_K_GUIDELINE,
                "dense_weight": 0.6,
                "sparse_weight": 0.4,
                "use_rerank": False
            },
            "historical": {
                "top_k": self.settings.RETRIEVAL_TOP_K_HISTORICAL,
                "dense_weight": 0.7,
                "sparse_weight": 0.3,
                "use_rerank": True
            },
            "delegated": {
                "top_k": self.settings.RETRIEVAL_TOP_K_DELEGATED,
                "dense_weight": 1.0,
                "sparse_weight": 0.0,
                "use_rerank": False
            }
        }

        # 延迟初始化的客户端
        self._milvus_client = None
        self._sparse_embedding_function = None
        self._initialization_attempted = False

        logger.info("优化版检索服务初始化完成（GPU+CPU分离架构 + 优化Milvus）")

    @property
    def milvus_client(self):
        """延迟初始化Milvus客户端"""
        if self._milvus_client is None and not self._initialization_attempted:
            try:
                logger.info("正在初始化Milvus客户端...")
                self._milvus_client = get_milvus_client()
                logger.info("Milvus客户端初始化成功")
            except Exception as e:
                logger.warning(f"Milvus客户端初始化失败: {str(e)}")
                # 创建一个模拟客户端以避免重复尝试
                self._milvus_client = None
            finally:
                self._initialization_attempted = True
        return self._milvus_client

    @property
    def sparse_embedding_function(self):
        """延迟初始化稀疏嵌入函数"""
        if self._sparse_embedding_function is None:
            try:
                logger.info("正在初始化GPU稀疏嵌入函数...")
                self._sparse_embedding_function = get_production_sparse_embedding_function()
                logger.info("GPU稀疏嵌入函数初始化成功")
            except Exception as e:
                logger.warning(f"GPU稀疏嵌入函数初始化失败: {str(e)}")
                self._sparse_embedding_function = None
        return self._sparse_embedding_function

    async def _offline_vector_search(
        self,
        collection_name: str,
        query: str,
        top_k: int = 5,
        dense_weight: float = 0.7,
        sparse_weight: float = 0.3,
        filters: Optional[Dict] = None
    ) -> List:
        """
        离线向量检索 - 使用GPU服务进行真实的向量计算

        当Milvus不可用时，仍然可以使用GPU服务进行稀疏嵌入计算
        并结合API密集嵌入进行相似度计算
        """
        try:
            # 模拟文档数据库（实际应用中可以从其他数据源获取）
            mock_documents = self._get_mock_documents(collection_name)

            # 1. 生成查询的稀疏嵌入（使用GPU服务）
            if self.sparse_embedding_function and sparse_weight > 0:
                logger.info(f"使用GPU服务生成查询稀疏嵌入: {query[:50]}...")
                sparse_query = self.sparse_embedding_function.encode_queries([query])[0]
                logger.info(f"GPU稀疏嵌入生成成功，维度: {len(sparse_query)}")
            else:
                sparse_query = {}
                logger.warning("GPU服务不可用，跳过稀疏嵌入")

            # 2. 生成查询的密集嵌入（使用API服务）
            if dense_weight > 0:
                logger.info(f"使用API服务生成查询密集嵌入: {query[:50]}...")
                try:
                    from shared.clients.embedding_client import get_dense_embeddings
                    dense_query = get_dense_embeddings([query])[0]
                    logger.info(f"API密集嵌入生成成功，维度: {len(dense_query)}")
                except Exception as e:
                    logger.warning(f"API密集嵌入生成失败: {str(e)}")
                    dense_query = []
            else:
                dense_query = []

            # 3. 计算相似度并排序
            results = []
            for doc in mock_documents:
                similarity_score = 0.0

                # 稀疏向量相似度（余弦相似度）
                if sparse_query and doc.get('sparse_vector'):
                    sparse_sim = self._calculate_sparse_similarity(sparse_query, doc['sparse_vector'])
                    similarity_score += sparse_sim * sparse_weight

                # 密集向量相似度（余弦相似度）
                if dense_query and doc.get('dense_vector'):
                    dense_sim = self._calculate_dense_similarity(dense_query, doc['dense_vector'])
                    similarity_score += dense_sim * dense_weight

                # 应用过滤条件
                if filters and not self._apply_filters(doc.get('metadata', {}), filters):
                    continue

                results.append({
                    'id': doc['id'],
                    'text': doc['text'],
                    'score': similarity_score,
                    'metadata': doc.get('metadata', {}),
                    'source': 'offline_gpu_calculation'
                })

            # 按相似度排序并返回top_k结果
            results.sort(key=lambda x: x['score'], reverse=True)
            top_results = results[:top_k]

            # 转换为兼容格式
            search_results = []
            for result in top_results:
                search_results.append(type('OfflineResult', (), {
                    'id': result['id'],
                    'text': result['text'],
                    'score': result['score'],
                    'metadata': result['metadata']
                })())

            logger.info(f"离线向量检索完成: {len(search_results)} 个结果，使用GPU+API真实计算")
            return search_results

        except Exception as e:
            logger.error(f"离线向量检索失败: {str(e)}")
            # 返回基础模拟结果作为回退
            return [
                type('FallbackResult', (), {
                    'id': f'fallback_{collection_name}_1',
                    'text': f'回退模式结果：{query} (来源: {collection_name})',
                    'score': 0.5,
                    'metadata': {'source': 'fallback_mode', 'collection': collection_name}
                })()
            ]

    def _get_mock_documents(self, collection_name: str) -> List[Dict]:
        """获取模拟文档数据"""
        # 这里可以从其他数据源获取真实数据，比如数据库、文件等
        # 为了演示，我们创建一些有意义的模拟数据

        base_documents = {
            "department": [
                {
                    "id": "dept_001",
                    "text": "市场监督管理局负责食品安全监管，包括食品生产、流通、餐饮服务等环节的监督检查。",
                    "metadata": {"department": "市场监督管理局", "category": "食品安全", "type": "监管职责"},
                    "sparse_vector": {"食品": 0.8, "安全": 0.9, "监管": 0.7, "市场": 0.6},
                    "dense_vector": [0.1] * 1024  # 模拟1024维密集向量
                },
                {
                    "id": "dept_002",
                    "text": "城市管理委员会负责城市环境卫生、市容市貌、违法建设治理等城市管理工作。",
                    "metadata": {"department": "城市管理委员会", "category": "城市管理", "type": "职责范围"},
                    "sparse_vector": {"城市": 0.9, "管理": 0.8, "环境": 0.7, "卫生": 0.6},
                    "dense_vector": [0.2] * 1024
                }
            ],
            "guideline": [
                {
                    "id": "guide_001",
                    "text": "食品安全投诉处理流程：接收投诉→核实情况→现场检查→处理措施→反馈结果。",
                    "metadata": {"type": "处理流程", "category": "食品安全", "version": "v2.0"},
                    "sparse_vector": {"投诉": 0.9, "处理": 0.8, "食品": 0.7, "流程": 0.6},
                    "dense_vector": [0.3] * 1024
                }
            ],
            "historical": [
                {
                    "id": "hist_001",
                    "text": "2024年3月，某餐厅因食品安全问题被投诉，经调查发现存在食材过期问题，已责令整改。",
                    "metadata": {"date": "2024-03", "type": "案例", "status": "已处理", "department": "市场监督管理局"},
                    "sparse_vector": {"餐厅": 0.8, "食品": 0.9, "投诉": 0.7, "整改": 0.6},
                    "dense_vector": [0.4] * 1024
                }
            ],
            "delegated": [
                {
                    "id": "deleg_001",
                    "text": "关于加强食品安全监管的通知：各相关部门要加强协调配合，形成监管合力。",
                    "metadata": {"type": "通知", "level": "市级", "category": "食品安全"},
                    "sparse_vector": {"监管": 0.9, "食品": 0.8, "通知": 0.7, "部门": 0.6},
                    "dense_vector": [0.5] * 1024
                }
            ]
        }

        return base_documents.get(collection_name, [])

    def _calculate_sparse_similarity(self, query_vector: Dict, doc_vector: Dict) -> float:
        """计算稀疏向量余弦相似度"""
        try:
            # 计算交集
            common_keys = set(query_vector.keys()) & set(doc_vector.keys())
            if not common_keys:
                return 0.0

            # 计算点积
            dot_product = sum(query_vector[key] * doc_vector[key] for key in common_keys)

            # 计算模长
            query_norm = sum(val ** 2 for val in query_vector.values()) ** 0.5
            doc_norm = sum(val ** 2 for val in doc_vector.values()) ** 0.5

            if query_norm == 0 or doc_norm == 0:
                return 0.0

            return dot_product / (query_norm * doc_norm)
        except Exception as e:
            logger.warning(f"稀疏向量相似度计算失败: {str(e)}")
            return 0.0

    def _calculate_dense_similarity(self, query_vector: List[float], doc_vector: List[float]) -> float:
        """计算密集向量余弦相似度"""
        try:
            if len(query_vector) != len(doc_vector):
                return 0.0

            # 计算点积
            dot_product = sum(a * b for a, b in zip(query_vector, doc_vector))

            # 计算模长
            query_norm = sum(a ** 2 for a in query_vector) ** 0.5
            doc_norm = sum(b ** 2 for b in doc_vector) ** 0.5

            if query_norm == 0 or doc_norm == 0:
                return 0.0

            return dot_product / (query_norm * doc_norm)
        except Exception as e:
            logger.warning(f"密集向量相似度计算失败: {str(e)}")
            return 0.0

    def _apply_filters(self, metadata: Dict, filters: Dict) -> bool:
        """应用过滤条件"""
        try:
            for key, value in filters.items():
                if key not in metadata or metadata[key] != value:
                    return False
            return True
        except Exception as e:
            logger.warning(f"过滤条件应用失败: {str(e)}")
            return True  # 过滤失败时默认通过

    def ensure_collection(self, collection_name: str):
        """确保集合存在"""
        try:
            # 检查Milvus客户端是否可用
            if self.milvus_client is None:
                logger.warning(f"Milvus客户端不可用，跳过集合 {collection_name} 检查")
                return

            # 检查集合是否存在，不存在则创建
            collections = self.milvus_client.list_collections()
            if collection_name not in collections:
                logger.info(f"创建集合: {collection_name}")
                self.milvus_client.create_collection(
                    collection_name=collection_name,
                    dimension=self.settings.MILVUS_DIMENSION,
                    description=f"AI接诉即办助手v3.0 - {collection_name}集合",
                    enable_sparse=True
                )
            else:
                logger.info(f"集合 {collection_name} 已存在")
        except Exception as e:
            logger.error(f"确保集合 {collection_name} 存在失败: {str(e)}")
            # 不抛出异常，允许在离线模式下继续运行
    
    async def hybrid_retrieve(
        self,
        collection_name: str,
        query: str,
        top_k: Optional[int] = None,
        use_rerank: Optional[bool] = None,
        filters: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        混合检索（密集+稀疏）

        Args:
            collection_name: 集合名称
            query: 查询文本
            top_k: 返回结果数量
            use_rerank: 是否使用重排序
            filters: 过滤条件

        Returns:
            检索结果
        """
        start_time = time.time()

        try:
            # 获取配置
            config = self.retrieval_config.get(collection_name, {})
            actual_top_k = top_k or config.get("top_k", 5)
            actual_use_rerank = use_rerank if use_rerank is not None else config.get("use_rerank", False)
            dense_weight = config.get("dense_weight", 0.7)
            sparse_weight = config.get("sparse_weight", 0.3)

            # 检查Milvus客户端是否可用以及是否为离线模式
            is_offline_mode = self.milvus_client is None or getattr(self.milvus_client, '_offline_mode', False)

            if is_offline_mode:
                logger.warning(f"Milvus不可用，使用GPU+API离线向量检索: {collection_name}")
                # 使用GPU服务进行真实的向量检索（离线模式）
                search_results = await self._offline_vector_search(
                    collection_name=collection_name,
                    query=query,
                    top_k=actual_top_k,
                    dense_weight=dense_weight,
                    sparse_weight=sparse_weight,
                    filters=filters
                )

                # 在离线模式下为每个结果添加模式标识
                for result in search_results:
                    if hasattr(result, 'metadata'):
                        result.metadata['retrieval_mode'] = 'offline'
                        result.metadata['mode_description'] = 'GPU+API离线向量计算'
                    else:
                        result.metadata = {
                            'retrieval_mode': 'offline',
                            'mode_description': 'GPU+API离线向量计算'
                        }
            else:
                # 确保集合存在
                self.ensure_collection(collection_name)

                # 构建过滤表达式
                filter_expr = None
                if filters:
                    # 简化的过滤表达式构建
                    filter_parts = []
                    for key, value in filters.items():
                        if isinstance(value, str):
                            filter_parts.append(f'metadata["{key}"] == "{value}"')
                        else:
                            filter_parts.append(f'metadata["{key}"] == {value}')
                    filter_expr = " and ".join(filter_parts) if filter_parts else None

                # 使用优化的Milvus客户端进行混合检索
                search_results = self.milvus_client.hybrid_search(
                    collection_name=collection_name,
                    query_text=query,
                    top_k=actual_top_k,
                    dense_weight=dense_weight,
                    sparse_weight=sparse_weight,
                    filters=filter_expr
                )

                # 在在线模式下为每个结果添加模式标识
                for result in search_results:
                    if hasattr(result, 'metadata'):
                        result.metadata['retrieval_mode'] = 'online'
                        result.metadata['mode_description'] = 'Milvus混合检索'
                    else:
                        result.metadata = {
                            'retrieval_mode': 'online',
                            'mode_description': 'Milvus混合检索'
                        }

            # 重排序（如果启用）
            if actual_use_rerank and len(search_results) > 1:
                try:
                    # 这里可以添加重排序逻辑
                    logger.debug(f"对 {len(search_results)} 个结果进行重排序")
                except Exception as e:
                    logger.warning(f"重排序失败，使用原始结果: {str(e)}")

            processing_time = time.time() - start_time

            logger.info(f"混合检索完成: {collection_name}, 查询: {query[:50]}..., "
                       f"结果数: {len(search_results)}, 耗时: {processing_time:.3f}秒")

            return {
                "nodes": [
                    {
                        "id": result.id,
                        "text": result.text,
                        "score": result.score,
                        "metadata": result.metadata
                    }
                    for result in search_results
                ],
                "processing_time": processing_time,
                "retrieval_mode": "offline" if is_offline_mode else "online",
                "mode_description": "GPU+API离线向量计算" if is_offline_mode else "Milvus混合检索",
                "system_status": {
                    "milvus_available": not is_offline_mode,
                    "gpu_service_available": self.sparse_embedding_function is not None,
                    "api_service_available": True  # 假设API服务可用
                },
                "config": {
                    "collection_name": collection_name,
                    "top_k": actual_top_k,
                    "use_rerank": actual_use_rerank,
                    "dense_weight": dense_weight,
                    "sparse_weight": sparse_weight,
                    "architecture": "GPU+CPU分离架构",
                    "milvus_optimized": True
                }
            }

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"混合检索失败: {collection_name}, 查询: {query[:50]}..., "
                        f"错误: {str(e)}, 耗时: {processing_time:.3f}秒")
            raise
    
    async def parallel_retrieve_all(
        self,
        query: str,
        department_name: Optional[str] = None,
        order_status: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        并行检索所有集合
        
        Args:
            query: 查询文本
            department_name: 部门名称（用于过滤）
            order_status: 工单状态（用于过滤）
            
        Returns:
            所有集合的检索结果
        """
        start_time = time.time()
        
        try:
            # 构建过滤条件
            filters = {}
            if department_name:
                filters["department_name"] = department_name
            if order_status:
                filters["order_status"] = order_status
            
            # 并行检索所有集合
            tasks = []
            collections = ["department", "guideline", "historical", "delegated"]
            
            for collection in collections:
                task = self.hybrid_retrieve(
                    collection_name=collection,
                    query=query,
                    filters=filters
                )
                tasks.append(task)
            
            # 等待所有任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            final_results = {}
            for i, collection in enumerate(collections):
                if isinstance(results[i], Exception):
                    logger.error(f"集合 {collection} 检索失败: {str(results[i])}")
                    final_results[collection] = []
                else:
                    final_results[collection] = results[i]["nodes"]
            
            total_processing_time = time.time() - start_time
            
            logger.info(f"并行检索完成: 查询: {query[:50]}..., "
                       f"总耗时: {total_processing_time:.3f}秒")
            
            # 添加元数据
            is_offline_mode = self.milvus_client is None or getattr(self.milvus_client, '_offline_mode', False)
            final_results["total_processing_time"] = total_processing_time
            final_results["query"] = query
            final_results["architecture"] = "GPU+CPU分离架构"
            final_results["optimization"] = "v3.0优化版"
            final_results["retrieval_mode"] = "offline" if is_offline_mode else "online"
            final_results["mode_description"] = "GPU+API离线向量计算" if is_offline_mode else "Milvus混合检索"
            final_results["system_status"] = {
                "milvus_available": not is_offline_mode,
                "gpu_service_available": self.sparse_embedding_function is not None,
                "api_service_available": True
            }
            
            return final_results
            
        except Exception as e:
            total_processing_time = time.time() - start_time
            logger.error(f"并行检索失败: 查询: {query[:50]}..., "
                        f"错误: {str(e)}, 耗时: {total_processing_time:.3f}秒")
            raise
    
    def get_collections_info(self) -> List[Dict[str, Any]]:
        """获取集合信息"""
        collections_info = []

        for name, config in self.retrieval_config.items():
            collection_info = {
                "name": name,
                "config": config,
                "architecture": "GPU+CPU分离架构"
            }

            # 添加状态信息
            if self.milvus_client is None or getattr(self.milvus_client, '_offline_mode', False):
                collection_info["status"] = "offline_mode"
                collection_info["message"] = "Milvus不可用，使用GPU+API离线向量检索"
                collection_info["capabilities"] = ["GPU稀疏嵌入", "API密集嵌入", "真实向量计算"]
            else:
                collection_info["status"] = "online"
                collection_info["message"] = "Milvus正常运行，全功能可用"
                collection_info["capabilities"] = ["Milvus混合检索", "GPU稀疏嵌入", "API密集嵌入"]

            collections_info.append(collection_info)

        return collections_info
    
    def get_retrieval_config(self) -> Dict[str, Any]:
        """获取检索配置"""
        config = {
            "retrieval_config": self.retrieval_config,
            "architecture": "GPU+CPU分离架构",
            "dense_embedding": "API调用",
            "sparse_embedding": "GPU服务",
            "vector_database": "Milvus优化版",
            "features": [
                "混合检索（密集+稀疏）",
                "并行检索",
                "智能回退机制",
                "重排序支持",
                "Milvus原生混合搜索",
                "GPU稀疏嵌入集成"
            ],
            "optimizations": [
                "直接使用Milvus混合搜索API",
                "GPU服务稀疏嵌入替换LlamaIndex默认实现",
                "优化的连接池和缓存机制",
                "延迟初始化客户端连接"
            ]
        }

        # 添加状态信息
        if self.milvus_client is None or getattr(self.milvus_client, '_offline_mode', False):
            config["status"] = "offline_mode"
            config["milvus_status"] = "不可用"
            config["message"] = "Milvus不可用，使用GPU+API进行真实向量检索"
            config["retrieval_mode"] = "离线向量计算"
        else:
            config["status"] = "online"
            config["milvus_status"] = "可用"
            config["message"] = "所有服务正常运行"
            config["retrieval_mode"] = "Milvus混合检索"

        if self.sparse_embedding_function is None:
            config["gpu_service_status"] = "不可用"
        else:
            config["gpu_service_status"] = "可用"

        return config


# 单例模式
_retrieval_service = None


@lru_cache()
def get_retrieval_service() -> OptimizedRetrievalService:
    """获取检索服务实例（单例模式）"""
    global _retrieval_service
    
    if _retrieval_service is None:
        _retrieval_service = OptimizedRetrievalService()
        logger.info("创建检索服务实例")
    
    return _retrieval_service
