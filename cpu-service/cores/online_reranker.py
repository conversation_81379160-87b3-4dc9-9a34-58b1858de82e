"""
通用在线Rerank重排序器，适配硅基流Rerank API接口。
参考文档: https://docs.siliconflow.cn/cn/api-reference/rerank/create-rerank
"""

import json
import requests
import time
import logging
from typing import List, Optional, Dict, Any, Callable, Union, Tuple

from llama_index.core.schema import NodeWithScore, QueryBundle
from llama_index.core.postprocessor.types import BaseNodePostprocessor
from llama_index.core.bridge.pydantic import Field

# 设置日志记录器
logger = logging.getLogger(__name__)

class OnlineReranker(BaseNodePostprocessor):
    """通用在线重排序器，适配硅基流Rerank API接口。

    参数:
        api_url (str): Rerank API的URL，默认为 "https://api.siliconflow.cn/v1/rerank"
        top_n (int): 返回的文档数量，默认为3
        api_key (str): API密钥，用于认证
        model (str): 使用的模型名称，默认为 "BAAI/bge-reranker-v2-m3"
        max_retries (int): 最大重试次数，默认为3
        timeout (int): 请求超时时间（秒），默认为30
    """

    api_url: str = Field(description="Rerank API的URL")
    top_n: int = Field(description="返回的文档数量")
    api_key: str = Field(description="API密钥")
    model: str = Field(description="使用的模型名称")
    max_retries: int = Field(description="最大重试次数")
    timeout: int = Field(description="请求超时时间（秒）")

    def __init__(
        self,
        api_url: str = "https://api.siliconflow.cn/v1/rerank",
        top_n: int = 3,
        api_key: str = "",
        model: str = "BAAI/bge-reranker-v2-m3",
        max_retries: int = 3,
        timeout: int = 30,
        **kwargs
    ):
        """初始化在线重排序器。"""
        # 初始化基类
        super().__init__(
            api_url=api_url,
            top_n=top_n,
            api_key=api_key,
            model=model,
            max_retries=max_retries,
            timeout=timeout,
            **kwargs
        )

    def _postprocess_nodes(
        self,
        nodes: List[NodeWithScore],
        query_bundle: Optional[QueryBundle] = None,
    ) -> List[NodeWithScore]:
        """重新排序文档片段。

        参数:
            nodes: 待排序的文档片段（带分数）
            query_bundle: 查询内容

        返回:
            排序后的文档片段
        """
        # 记录开始时间
        start_time = time.time()

        if not nodes:
            logger.warning("没有文档需要重排序，返回空列表")
            return []

        if query_bundle is None:
            logger.warning("未提供查询内容，返回前N个文档")
            return nodes[: self.top_n]

        reranked_nodes = self._rerank_documents(nodes, query_bundle.query_str)

        # 计算并记录耗时
        elapsed_time = time.time() - start_time
        logger.info(f"重排序: 耗时={elapsed_time:.2f}秒, 结果数量={len(reranked_nodes[:self.top_n])}")

        return reranked_nodes[: self.top_n]

    def _validate_input(self, nodes: List[NodeWithScore], query: str) -> Tuple[List[str], str, bool]:
        """验证输入数据并进行预处理。

        参数:
            nodes: 待排序的文档片段
            query: 查询字符串

        返回:
            Tuple[List[str], str, bool]: 处理后的文档列表、处理后的查询字符串、输入是否有效
        """
        # 验证查询不为空
        if not query or not isinstance(query, str) or len(query.strip()) == 0:
            logger.warning("查询为空，无法进行重排序")
            return [], "", False

        # 提取文档文本并过滤空值
        documents = []
        for node in nodes:
            content = node.node.get_content()
            if content and isinstance(content, str) and len(content.strip()) > 0:
                documents.append(content.strip())

        # 如果没有有效文档，返回无效标志
        if not documents:
            logger.warning("没有有效的文档内容，无法进行重排序")
            return [], query.strip(), False

        return documents, query.strip(), True

    def _call_rerank_api(self, documents: List[str], query: str) -> Dict[str, Any]:
        """调用Rerank API并处理响应。

        参数:
            documents: 文档文本列表
            query: 查询字符串

        返回:
            Dict[str, Any]: API响应数据

        异常:
            requests.exceptions.RequestException: 请求异常
            requests.exceptions.HTTPError: HTTP错误
            ValueError: 响应格式错误
        """
        # 构建请求头
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        # 构建请求体，严格按照硅基流API文档格式
        # 确保文档不为空并且是字符串列表
        if not documents:
            raise ValueError("文档列表不能为空")

        # 确保每个文档都是字符串且不为空
        for i, doc in enumerate(documents):
            if not isinstance(doc, str) or not doc.strip():
                raise ValueError(f"文档[{i}]不是有效的字符串")

        # 确保查询是字符串且不为空
        if not isinstance(query, str) or not query.strip():
            raise ValueError("查询不是有效的字符串")

        request_data = {
            "model": self.model,
            "query": query,
            "documents": documents,
            "top_n": min(self.top_n, len(documents))  # 确保top_n不超过文档数量
        }

        # 记录请求详情
        logger.info(f"调用Rerank API: {self.api_url}")
        logger.debug(f"查询内容: '{query[:100]}...'" if len(query) > 100 else f"'{query}'")
        logger.info(f"文档数量: {len(documents)}")
        logger.debug(f"文档示例: '{documents[0][:100]}...'" if documents and len(documents[0]) > 100 else f"'{documents[0]}'" if documents else "无文档")
        logger.debug(f"请求体: {json.dumps(request_data, ensure_ascii=False)[:1000]}..." if len(json.dumps(request_data, ensure_ascii=False)) > 1000 else json.dumps(request_data, ensure_ascii=False))

        # 手动实现重试机制
        retries = 0
        last_exception = None

        while retries <= self.max_retries:
            try:
                # 发送请求
                response = requests.post(
                    self.api_url,
                    headers=headers,
                    json=request_data,
                    timeout=self.timeout
                )

                # 记录响应状态
                logger.info(f"响应状态码: {response.status_code}")

                # 如果是4xx错误，记录响应内容以便调试
                if 400 <= response.status_code < 500:
                    try:
                        error_content = response.text
                        logger.error(f"响应错误内容: {error_content}")
                    except Exception as content_e:
                        logger.error(f"无法获取错误响应内容: {str(content_e)}")

                # 检查HTTP错误
                response.raise_for_status()

                # 解析响应
                response_data = response.json()

                # 验证响应格式
                if "results" not in response_data:
                    raise ValueError(f"API响应格式错误，缺少'results'字段: {response_data}")

                return response_data

            except (requests.exceptions.RequestException, requests.exceptions.HTTPError) as e:
                # 如果是客户端错误（400-499），不重试
                if hasattr(e, 'response') and 400 <= e.response.status_code < 500:
                    raise

                last_exception = e
                retries += 1

                # 如果还有重试机会，等待后重试
                if retries <= self.max_retries:
                    # 计算等待时间（指数退避）
                    wait_time = 2 ** retries  # 简单的指数退避策略
                    logger.warning(f"Rerank API调用失败，正在进行第{retries}次重试，等待{wait_time:.2f}秒")
                    time.sleep(wait_time)
            except ValueError as e:
                # 响应格式错误不重试
                raise

        # 如果所有重试都失败，抛出最后一个异常
        if last_exception:
            raise last_exception

        # 这行代码应该不会执行，因为如果所有重试都失败，上面会抛出异常
        raise RuntimeError("所有Rerank API调用重试都失败")

    def _process_response(self, response_data: Dict[str, Any], nodes: List[NodeWithScore]) -> List[NodeWithScore]:
        """处理API响应并重新排序节点。

        参数:
            response_data: API响应数据
            nodes: 原始节点列表

        返回:
            List[NodeWithScore]: 重新排序后的节点列表
        """
        # 记录响应详情
        logger.debug(f"响应ID: {response_data.get('id')}")
        logger.debug(f"使用模型: {response_data.get('model')}")
        logger.debug(f"Token使用: {response_data.get('usage', {})}")

        results = response_data.get("results", [])
        logger.debug(f"结果数量: {len(results)}")

        # 创建索引到原始节点的映射
        node_map = {i: node for i, node in enumerate(nodes)}
        reranked_nodes = []

        for result in results:
            idx = result.get("index")
            score = result.get("relevance_score")

            # 记录每个结果的详细信息
            logger.debug(f"结果项: index={idx}, score={score}")

            if idx is not None and idx in node_map:
                # 获取原始节点并更新分数
                node = node_map[idx]
                node.score = score
                reranked_nodes.append(node)

        return reranked_nodes

    def _rerank_documents(
        self,
        nodes: List[NodeWithScore],
        query: str
    ) -> List[NodeWithScore]:
        """调用在线API进行文档重排序。

        参数:
            nodes: 待排序的文档片段
            query: 查询字符串

        返回:
            重新排序后的文档片段
        """
        # 记录API调用开始时间
        api_start_time = time.time()

        # 验证输入并预处理
        documents, processed_query, is_valid = self._validate_input(nodes, query)
        if not is_valid:
            return sorted(nodes, key=lambda x: x.score or 0, reverse=True)

        try:
            # 调用API
            response_data = self._call_rerank_api(documents, processed_query)

            # 处理响应
            reranked_nodes = self._process_response(response_data, nodes)

            # 如果没有结果，返回原始排序
            if not reranked_nodes:
                logger.warning("未从API响应中获取到有效结果，返回原始排序")
                return sorted(nodes, key=lambda x: x.score or 0, reverse=True)

            # 记录API调用耗时
            api_elapsed_time = time.time() - api_start_time
            logger.info(f"Rerank API调用成功: 耗时={api_elapsed_time:.2f}秒, 结果数量={len(reranked_nodes)}")

            return reranked_nodes

        except (requests.exceptions.RequestException, ValueError) as e:
            # 记录错误并返回原始排序
            api_elapsed_time = time.time() - api_start_time
            logger.error(f"Rerank API调用失败: {str(e)}, 耗时={api_elapsed_time:.2f}秒")
            return sorted(nodes, key=lambda x: x.score or 0, reverse=True)
