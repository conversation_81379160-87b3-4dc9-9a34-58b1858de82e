"""
嵌入模型核心模块
提供统一的嵌入模型和LLM模型获取接口
"""

import logging
from typing import Optional

logger = logging.getLogger(__name__)

# 尝试导入共享模块
try:
    from shared.config.settings import get_settings
    from shared.clients.embedding_client import get_api_embedding_model
    _shared_available = True
except ImportError:
    _shared_available = False

# 尝试导入LlamaIndex模块
try:
    from llama_index.embeddings.openai import OpenAIEmbedding
    from llama_index.embeddings.huggingface import HuggingFaceEmbedding
    from llama_index.llms.openai import OpenAI
    _llamaindex_available = True
except ImportError:
    _llamaindex_available = False

from cores.config import my_settings


def get_api_embed_model():
    """
    获取API嵌入模型
    优先使用共享模块，如果不可用则使用本地实现
    """
    try:
        if _shared_available:
            # 使用共享模块的API嵌入模型
            return get_api_embedding_model()
        elif _llamaindex_available:
            # 使用LlamaIndex的OpenAI兼容嵌入模型
            return OpenAIEmbedding(
                model=my_settings.API_EMBED_MODEL,
                api_key=my_settings.API_EMBED_KEY,
                api_base=my_settings.API_EMBED_BASE,
                embed_batch_size=my_settings.API_EMBED_BATCH_SIZE
            )
        else:
            logger.error("无法创建API嵌入模型：缺少必要的依赖")
            return None
    except Exception as e:
        logger.error(f"创建API嵌入模型失败: {str(e)}")
        return None


def get_local_embed_model():
    """
    获取本地嵌入模型
    """
    try:
        if _llamaindex_available:
            return HuggingFaceEmbedding(
                model_name=my_settings.LOCAL_EMBED_MODEL,
                cache_folder="./models"
            )
        else:
            logger.error("无法创建本地嵌入模型：缺少HuggingFace依赖")
            return None
    except Exception as e:
        logger.error(f"创建本地嵌入模型失败: {str(e)}")
        return None


def get_llm_model():
    """
    获取LLM模型
    """
    try:
        if _llamaindex_available:
            return OpenAI(
                model=my_settings.LLM_MODEL,
                api_key=my_settings.LLM_API_KEY,
                api_base=my_settings.LLM_BASE_URL,
                temperature=0.1,
                max_tokens=4096
            )
        else:
            logger.error("无法创建LLM模型：缺少OpenAI依赖")
            return None
    except Exception as e:
        logger.error(f"创建LLM模型失败: {str(e)}")
        return None


def get_default_embed_model():
    """
    获取默认嵌入模型
    优先使用API模型，如果失败则回退到本地模型
    """
    # 首先尝试API模型
    api_model = get_api_embed_model()
    if api_model:
        logger.info("使用API嵌入模型")
        return api_model
    
    # 如果API模型失败，尝试本地模型
    local_model = get_local_embed_model()
    if local_model:
        logger.info("使用本地嵌入模型")
        return local_model
    
    logger.error("无法创建任何嵌入模型")
    return None


# 为了兼容性，提供一些别名
def get_embed_model():
    """获取嵌入模型的别名"""
    return get_default_embed_model()


def get_embedding_model():
    """获取嵌入模型的另一个别名"""
    return get_default_embed_model()
