"""
CPU服务核心配置模块
兼容旧版本代码的配置适配器
"""

import os
from typing import Optional

# 尝试导入共享配置，如果失败则使用本地配置
try:
    from shared.config.settings import get_settings
    _use_shared_config = True
except ImportError:
    _use_shared_config = False

def _get_shared_settings():
    """延迟获取共享配置"""
    if _use_shared_config:
        return get_settings()
    return None


class LegacySettingsAdapter:
    """
    旧版本配置适配器
    将新的共享配置适配到旧版本代码期望的接口
    """
    
    def __init__(self):
        self.ENV_LOADED = True
        self._settings_loaded = False

    def _ensure_settings_loaded(self):
        """确保配置已加载（延迟初始化）"""
        if not self._settings_loaded:
            # 如果有共享配置，使用共享配置；否则使用环境变量
            shared_settings = _get_shared_settings()
            if _use_shared_config and shared_settings:
                self._load_from_shared_settings(shared_settings)
            else:
                self._load_from_env()
            self._settings_loaded = True
    
    def _load_from_shared_settings(self, settings):
        """从共享配置加载"""
        
        # API配置
        self.API_EMBED_MODEL = settings.API_EMBED_MODEL
        self.API_EMBED_KEY = settings.API_EMBED_KEY
        self.API_EMBED_BASE = settings.API_EMBED_BASE
        self.API_EMBED_BATCH_SIZE = settings.API_EMBED_BATCH_SIZE
        
        # 重排序配置
        self.RERANK_MODEL = settings.RERANK_MODEL
        self.RERANK_TOP_N = getattr(settings, 'RERANK_TOP_N', 10)
        self.API_RERANK_URL = settings.RERANK_BASE_URL
        self.API_RERANK_KEY = settings.RERANK_API_KEY
        self.API_RERANK_MODEL = settings.RERANK_MODEL
        
        # LLM配置
        self.LLM_MODEL = getattr(settings, 'LLM_MODEL', 'qwen2.5-32b-instruct')
        self.LLM_API_KEY = getattr(settings, 'LLM_API_KEY', settings.API_EMBED_KEY)
        self.LLM_BASE_URL = getattr(settings, 'LLM_BASE_URL', settings.API_EMBED_BASE)
        
        # 本地模型配置
        self.LOCAL_EMBED_MODEL = getattr(settings, 'LOCAL_EMBED_MODEL', 'BAAI/bge-m3')
        self.BGE_M3_MODEL_PATH = settings.BGE_M3_MODEL_PATH
        
        # GPU服务配置
        self.GPU_SERVICE_URL = settings.GPU_SERVICE_URL or f"http://{settings.GPU_SERVICE_HOST}:{settings.GPU_SERVICE_PORT}"
        
        # Milvus配置
        self.MILVUS_URI = getattr(settings, 'MILVUS_URI', 'http://localhost:19530')
        self.MILVUS_HOST = getattr(settings, 'MILVUS_HOST', 'localhost')
        self.MILVUS_PORT = getattr(settings, 'MILVUS_PORT', 19530)
        self.MILVUS_USER = getattr(settings, 'MILVUS_USER', '')
        self.MILVUS_PASSWORD = getattr(settings, 'MILVUS_PASSWORD', '')
        
        # 环境配置
        self.ENVIRONMENT = settings.ENVIRONMENT
        self.DEBUG = settings.DEBUG

    def __getattr__(self, name):
        """属性访问器，实现延迟初始化"""
        self._ensure_settings_loaded()
        return object.__getattribute__(self, name)
        
    def _load_from_env(self):
        """从环境变量加载（回退模式）"""
        # API配置
        self.API_EMBED_MODEL = os.getenv("API_EMBED_MODEL", "BAAI/bge-m3")
        self.API_EMBED_KEY = os.getenv("API_EMBED_KEY")
        self.API_EMBED_BASE = os.getenv("API_EMBED_BASE", "https://api.siliconflow.cn/v1")
        self.API_EMBED_BATCH_SIZE = int(os.getenv("API_EMBED_BATCH_SIZE", "8"))
        
        # 重排序配置
        self.RERANK_MODEL = os.getenv("RERANK_MODEL", "BAAI/bge-reranker-v2-m3")
        self.RERANK_TOP_N = int(os.getenv("RERANK_TOP_N", "10"))
        self.API_RERANK_URL = os.getenv("RERANK_BASE_URL", "https://api.siliconflow.cn/v1")
        self.API_RERANK_KEY = os.getenv("RERANK_API_KEY", self.API_EMBED_KEY)
        self.API_RERANK_MODEL = os.getenv("API_RERANK_MODEL", self.RERANK_MODEL)
        
        # LLM配置
        self.LLM_MODEL = os.getenv("LLM_MODEL", "qwen2.5-32b-instruct")
        self.LLM_API_KEY = os.getenv("LLM_API_KEY", self.API_EMBED_KEY)
        self.LLM_BASE_URL = os.getenv("LLM_BASE_URL", self.API_EMBED_BASE)
        
        # 本地模型配置
        self.LOCAL_EMBED_MODEL = os.getenv("LOCAL_EMBED_MODEL", "BAAI/bge-m3")
        self.BGE_M3_MODEL_PATH = os.getenv("BGE_M3_MODEL_PATH", "BAAI/bge-m3")
        
        # GPU服务配置
        gpu_host = os.getenv("GPU_SERVICE_HOST", "localhost")
        gpu_port = os.getenv("GPU_SERVICE_PORT", "8001")
        self.GPU_SERVICE_URL = os.getenv("GPU_SERVICE_URL", f"http://{gpu_host}:{gpu_port}")
        
        # Milvus配置
        self.MILVUS_URI = os.getenv("MILVUS_URI", "http://localhost:19530")
        self.MILVUS_HOST = os.getenv("MILVUS_HOST", "localhost")
        self.MILVUS_PORT = int(os.getenv("MILVUS_PORT", "19530"))
        self.MILVUS_USER = os.getenv("MILVUS_USER", "")
        self.MILVUS_PASSWORD = os.getenv("MILVUS_PASSWORD", "")
        
        # 环境配置
        self.ENVIRONMENT = os.getenv("ENVIRONMENT", "development")
        self.DEBUG = os.getenv("DEBUG", "false").lower() == "true"


# 创建全局配置实例
my_settings = LegacySettingsAdapter()

# 为了兼容性，导出my_settings对象
# 其他模块应该通过 my_settings.ATTRIBUTE_NAME 来访问配置
