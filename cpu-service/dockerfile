# AI接诉即办助手 v3.0 - CPU服务 (优化版本)
# BuildKit + 国内镜像源 + 缓存挂载 + 多阶段构建

# syntax=docker/dockerfile:1.4
FROM python:3.11-slim AS system-base

# 设置构建时环境变量（增强网络配置）
ENV PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive \
    PIP_INDEX_URL=http://mirrors.aliyun.com/pypi/simple/ \
    PIP_TRUSTED_HOST="mirrors.aliyun.com files.pythonhosted.org pypi.org pypi.python.org" \
    PIP_TIMEOUT=600 \
    PIP_RETRIES=5 \
    PIP_DEFAULT_TIMEOUT=600

# 更换为阿里云APT镜像源（适配Debian 12，完全替换）
RUN rm -f /etc/apt/sources.list /etc/apt/sources.list.d/* && \
    echo "deb http://mirrors.aliyun.com/debian/ bookworm main contrib non-free" > /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian/ bookworm-updates main contrib non-free" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian-security/ bookworm-security main contrib non-free" >> /etc/apt/sources.list && \
    rm -rf /var/lib/apt/lists/* && \
    echo 'Acquire::http::Timeout "60";' > /etc/apt/apt.conf.d/99aliyun && \
    echo 'Acquire::Retries "3";' >> /etc/apt/apt.conf.d/99aliyun && \
    echo 'APT::Install-Recommends "false";' >> /etc/apt/apt.conf.d/99aliyun

# 使用缓存挂载安装系统依赖
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked \
    --mount=type=cache,target=/var/lib/apt,sharing=locked \
    apt-get update && apt-get install -y --no-install-recommends \
    curl=7.* \
    wget=1.21.* \
    ca-certificates \
    build-essential=12.* \
    gcc \
    g++ \
    git=1:2.* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# ================================
# Python基础环境阶段
# ================================
FROM system-base AS python-base

# pip镜像源已通过环境变量配置

# 使用pip缓存挂载升级pip
RUN --mount=type=cache,target=/root/.cache/pip \
    pip install --upgrade \
    pip==23.3.* \
    setuptools==69.* \
    wheel==0.42.*

# ================================
# 依赖构建阶段（使用缓存挂载）
# ================================
FROM python-base AS deps-builder

# 创建依赖安装目录
RUN mkdir -p /opt/deps

# 复制requirements文件
COPY cpu-service/requirements.txt /tmp/requirements.txt

# pip镜像源已通过环境变量配置，在所有阶段自动生效

# 使用pip缓存挂载分层安装依赖
# 第一层：基础框架依赖（兼容版本）
RUN --mount=type=cache,target=/root/.cache/pip \
    pip install --target=/opt/deps \
    fastapi==0.104.1 \
    uvicorn[standard]==0.24.0 \
    pydantic==2.5.3 \
    "pydantic-settings>=2.1.0,<3.0.0"

# 第二层：LlamaIndex核心（兼容版本）
RUN --mount=type=cache,target=/root/.cache/pip \
    pip install --target=/opt/deps --no-deps \
    llama-index==0.10.57 \
    llama-index-core==0.10.57 \
    llama-index-vector-stores-milvus==0.1.20 \
    llama-index-embeddings-openai==0.1.10 \
    llama-index-llms-openai==0.1.24

# 第三层：数据处理依赖
RUN --mount=type=cache,target=/root/.cache/pip \
    pip install --target=/opt/deps --no-deps \
    numpy>=1.24.0 \
    pandas>=2.0.0 \
    scipy>=1.11.0 \
    pymilvus>=2.3.0

# 第四层：其他依赖（保留关键依赖）
RUN --mount=type=cache,target=/root/.cache/pip \
    pip install --target=/opt/deps \
    requests>=2.31.0 \
    python-multipart==0.0.6 \
    python-dotenv==1.0.0 \
    pyyaml>=6.0 \
    redis>=4.5.0 \
    minio>=7.1.0 \
    structlog>=23.1.0 \
    aiofiles>=23.1.0 \
    httpx>=0.24.0 \
    uvloop>=0.19.0 \
    httptools>=0.6.0 \
    faiss-cpu>=1.7.4

# 清理缓存和编译文件
RUN find /opt/deps -name "*.pyc" -delete && \
    find /opt/deps -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true && \
    find /opt/deps -name "*.pyo" -delete && \
    find /opt/deps -name "*.egg-info" -type d -exec rm -rf {} + 2>/dev/null || true

# ================================
# 运行时阶段
# ================================
FROM python:3.11-slim AS runtime

# 设置运行时环境变量（只保留必要的系统级变量）
ENV PYTHONPATH=/app:/opt/deps \
    # CPU性能优化
    OMP_NUM_THREADS=4 \
    MKL_NUM_THREADS=4 \
    NUMEXPR_NUM_THREADS=4 \
    MALLOC_ARENA_MAX=4 \
    # FastAPI优化
    UVICORN_BACKLOG=2048 \
    UVICORN_TIMEOUT_KEEP_ALIVE=5

# 更换为阿里云APT镜像源（运行时阶段，适配Debian 12，完全替换）
RUN rm -f /etc/apt/sources.list /etc/apt/sources.list.d/* && \
    echo "deb http://mirrors.aliyun.com/debian/ bookworm main" > /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian/ bookworm-updates main" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian-security/ bookworm-security main" >> /etc/apt/sources.list && \
    rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 使用缓存挂载安装运行时依赖
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked \
    --mount=type=cache,target=/var/lib/apt,sharing=locked \
    apt-get update && apt-get install -y --no-install-recommends \
    curl=7.* \
    ca-certificates \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# 从依赖构建阶段复制Python包
COPY --from=deps-builder /opt/deps /opt/deps

# 设置工作目录
WORKDIR /app

# 复制共享代码
COPY --chown=appuser:appuser shared /app/shared

# 复制应用代码
COPY --chown=appuser:appuser cpu-service /app/

# 创建配置目录并生成生产环境配置文件
RUN mkdir -p /app/configs && \
    echo "# AI接诉即办助手 v3.0 - 生产环境配置" > /app/configs/.env.prod && \
    echo "# 生产环境专用配置文件" >> /app/configs/.env.prod && \
    echo "" >> /app/configs/.env.prod && \
    echo "# ================================" >> /app/configs/.env.prod && \
    echo "# 基础配置" >> /app/configs/.env.prod && \
    echo "# ================================" >> /app/configs/.env.prod && \
    echo "ENVIRONMENT=production" >> /app/configs/.env.prod && \
    echo "DEBUG=false" >> /app/configs/.env.prod && \
    echo "LOG_LEVEL=INFO" >> /app/configs/.env.prod && \
    echo "" >> /app/configs/.env.prod && \
    echo "# ================================" >> /app/configs/.env.prod && \
    echo "# API嵌入配置（密集嵌入）" >> /app/configs/.env.prod && \
    echo "# ================================" >> /app/configs/.env.prod && \
    echo "API_EMBED_MODEL=BAAI/bge-m3" >> /app/configs/.env.prod && \
    echo "API_EMBED_KEY=sk-hconcoielayrcqyfoqibziztdnchplrhkmupsvddcfkwehrd" >> /app/configs/.env.prod && \
    echo "API_EMBED_BASE=https://api.siliconflow.cn/v1" >> /app/configs/.env.prod && \
    echo "API_EMBED_BATCH_SIZE=8" >> /app/configs/.env.prod && \
    echo "" >> /app/configs/.env.prod && \
    echo "# ================================" >> /app/configs/.env.prod && \
    echo "# LLM配置" >> /app/configs/.env.prod && \
    echo "# ================================" >> /app/configs/.env.prod && \
    echo "LLM_MODEL=Qwen/Qwen2.5-32B-Instruct" >> /app/configs/.env.prod && \
    echo "LLM_API_KEY=sk-hconcoielayrcqyfoqibziztdnchplrhkmupsvddcfkwehrd" >> /app/configs/.env.prod && \
    echo "LLM_BASE_URL=https://api.siliconflow.cn/v1" >> /app/configs/.env.prod && \
    echo "" >> /app/configs/.env.prod && \
    echo "# ================================" >> /app/configs/.env.prod && \
    echo "# 重排序配置" >> /app/configs/.env.prod && \
    echo "# ================================" >> /app/configs/.env.prod && \
    echo "RERANK_MODEL=BAAI/bge-reranker-v2-m3" >> /app/configs/.env.prod && \
    echo "RERANK_API_KEY=sk-hconcoielayrcqyfoqibziztdnchplrhkmupsvddcfkwehrd" >> /app/configs/.env.prod && \
    echo "RERANK_BASE_URL=https://api.siliconflow.cn/v1" >> /app/configs/.env.prod && \
    echo "" >> /app/configs/.env.prod && \
    echo "# ================================" >> /app/configs/.env.prod && \
    echo "# Milvus向量数据库配置" >> /app/configs/.env.prod && \
    echo "# ================================" >> /app/configs/.env.prod && \
    echo "MILVUS_HOST=************" >> /app/configs/.env.prod && \
    echo "MILVUS_PORT=19530" >> /app/configs/.env.prod && \
    echo "MILVUS_USER=" >> /app/configs/.env.prod && \
    echo "MILVUS_PASSWORD=" >> /app/configs/.env.prod && \
    echo "MILVUS_DB_NAME=default" >> /app/configs/.env.prod && \
    echo "MILVUS_TIMEOUT=30" >> /app/configs/.env.prod && \
    echo "" >> /app/configs/.env.prod && \
    echo "# ================================" >> /app/configs/.env.prod && \
    echo "# GPU服务配置" >> /app/configs/.env.prod && \
    echo "# ================================" >> /app/configs/.env.prod && \
    echo "GPU_SERVICE_URL=http://gpu-service:8001" >> /app/configs/.env.prod && \
    echo "GPU_SERVICE_TIMEOUT=30" >> /app/configs/.env.prod && \
    echo "" >> /app/configs/.env.prod && \
    echo "# ================================" >> /app/configs/.env.prod && \
    echo "# CORS配置" >> /app/configs/.env.prod && \
    echo "# ================================" >> /app/configs/.env.prod && \
    echo 'CORS_ORIGINS=*' >> /app/configs/.env.prod && \
    echo 'CORS_METHODS=GET,POST,PUT,DELETE' >> /app/configs/.env.prod && \
    echo 'CORS_HEADERS=*' >> /app/configs/.env.prod && \
    chown -R appuser:appuser /app/configs

# 创建必要目录并设置权限
RUN mkdir -p /app/logs && \
    chown -R appuser:appuser /app && \
    chmod +x /app/*.py

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令（使用主启动脚本）
CMD ["python", "main.py"]
