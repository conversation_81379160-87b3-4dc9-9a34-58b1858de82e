"""查找相似工单助手"""

import asyncio
import os
import time
import logging

from autogen_agentchat.agents import AssistantAgent
from autogen_core import CancellationToken
from autogen_core.models import ModelFamily
from autogen_ext.models.openai import OpenAIChatCompletionClient
from cores.config import my_settings

# 设置日志记录器
logger = logging.getLogger(__name__)

class SimilarAssistant:
    def __init__(self, query: list[str]=None):
        self.query = query


    async def find_similar(self) -> str:
        # 记录开始时间
        start_time = time.time()
        logger.info(f"开始查找相似工单: 查询长度={len(self.query) if self.query else 0}")
        
        model_client = OpenAIChatCompletionClient(
            model=my_settings.LLM_MODEL,
            base_url=my_settings.BASE_URL,
            api_key=my_settings.API_KEY,
            response_format={"type": "json_object"},
            model_info={
                "vision": False,
                "function_calling": True,
                "json_output": True,
                "family": ModelFamily.UNKNOWN,
                "structured_output": True
            },
        )

        system_message = """
        你是工单信息提取助手，任务是从历史工单列表中提取工单编号和工单标题。仅提取以下信息，并以 JSON 格式返回结果。若无工单信息，返回空数组 []。

        **提取信息**：
        - order_id: 工单编号
        - order_title: 标题

        **注意**：
        - 无需额外分析或评价。
        - 确保返回格式符合以下示例：

        **返回格式示例**：
        {
          "similar_orders": []
        }
        或
        {
          "similar_orders": [
            {"order_id": "12345", "order_title": "具体诉求描述1"},
            {"order_id": "67890", "order_title": "具体诉求描述2"}
          ]
        }

        **任务说明**：
        - 从提供的工单列表中提取每个工单的编号和标题。
        - 确保返回结果是一个包含多个工单信息的数组，并包裹在 `"similar_orders"` 字段中。
        """
        assistant = AssistantAgent(
            "assistant",
            model_client=model_client,
            system_message=system_message
        )

        # 记录模型调用开始时间
        model_start_time = time.time()
        logger.info("开始调用大模型提取相似工单信息")

        response = await assistant.run(task=f"检索的历史工单信息为：{self.query}，请开始提取",
                               cancellation_token=CancellationToken())

        # 记录模型调用结束时间和耗时
        model_elapsed_time = time.time() - model_start_time
        logger.info(f"大模型调用完成: 耗时={model_elapsed_time:.2f}秒")

        await model_client.close()
        
        # 计算并记录总耗时
        elapsed_time = time.time() - start_time
        logger.info(f"相似工单查找完成: 总耗时={elapsed_time:.2f}秒")
        
        return response.messages[-1].content if hasattr(response, "messages") else response


if __name__ == "__main__":
    resolution = SimilarAssistant(query=["市民反映：2025年3月20日12点左右，乘坐车牌号：蒙AY5463的出租车从新城区文苑街大学城到赛罕区机场路附近的宾馆入住，但因为出租车拿了宾馆的提成导致市民无法退房，市民对此不认可，市民诉求：请有关部门协助处理出租车吃回扣问题，"])
    print(asyncio.run(resolution.find_similar()))
