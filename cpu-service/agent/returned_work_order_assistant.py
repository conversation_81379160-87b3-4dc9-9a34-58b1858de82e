"""增强的退回工单审核助手

提供更稳健、更专业的工具调用实现，包括：
- 统一的错误处理和异常管理
- 性能监控和日志记录
- 工具调用链路追踪
- 智能重试和降级机制
"""

import time
import logging
import uuid
from typing import Any, Optional

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.base import TaskResult
from autogen_core import CancellationToken
from autogen_core.models import ModelFamily
from autogen_ext.models.openai import OpenAIChatCompletionClient


# 设置日志记录器
logger = logging.getLogger(__name__)

class EnhancedReturnedOrderAssistant:
    """退回工单审核助手类

    提供更稳健的工具调用机制和更好的错误处理能力
    """

    def __init__(self, query: str = None):
        """初始化增强的退回工单审核助手

        Args:
            query: 包含工单信息的查询字符串
            session_id: 会话ID，用于日志记录
        """
        self.query = query


    async def handle_returned(self) -> TaskResult | None | Any:

        model_client = OpenAIChatCompletionClient(
            model=my_settings.LLM_MODEL_ANALYZE,
            base_url=my_settings.BASE_URL_ANALYZE,
            api_key=my_settings.API_KEY_ANALYZE,
            model_info={
                "vision": False,
                "function_calling": True,
                "json_output": True,
                "family": ModelFamily.UNKNOWN,
                "structured_output": False
            },
        )

        system_message = await self._build_system_message()

        assistant = AssistantAgent(
            "enhanced_assistant",
            model_client=model_client,
            tools=[
                enhanced_get_department_info,
                enhanced_get_department_hierarchy,
                enhanced_validate_return_workflow,
                enhanced_get_correct_workflow_recommendation
            ],  # 使用增强的工具列表
            system_message=system_message,
            model_client_stream=False
        )

        task_prompt = self._build_task_prompt()
        response = await assistant.run(
            task=task_prompt,
            cancellation_token=CancellationToken()
        )


        if hasattr(response, "messages") and response.messages:
            output_content = response.messages[-1].content
        else:
            output_content = str(response)

        print("退单审核结果", output_content)
        return output_content





    def _build_task_prompt(self) -> str:
        """构建任务提示"""
        return f"""请对以下提供的工单及相关信息进行专业分析：

            {self.query}

            请注意，上述信息包含了工单内容、当前处置部门、部门职责信息、派单导则信息、历史工单信息和单位下放职责信息。

            **重要：请严格按照以下优先级顺序进行决策分析：**
            1. **派单导则（最高优先级）**：首先查阅派单导则的相关规定，如有明确规定应严格执行
            2. **历史工单（次高优先级）**：参考类似工单的成功处理经验和模式
            3. **部门职责和下放职责（基础参考）**：作为基础参考，但在与高优先级信息冲突时应服从高优先级

            当多个信息源存在冲突时，必须按照上述优先级顺序进行决策，高优先级信息优于低优先级信息。

            请确保分析要点明确、逻辑清晰、结论明确。在详细分析部分，请按照优先级顺序组织内容：先分析派单导则，再分析历史经验，最后参考部门职责。

            请严格按照系统提示中的输出格式提供分析结果，确保推荐部门和处理建议保持一致，且符合工单流转规则，不能跨级派单。

            **重要提醒**：
            - 推荐部门必须是最终处理部门，不能是中间流转部门
            - 同级部门之间不能直接转派，必须通过上级部门
            - 例如：赛罕区分中心（2级）不能直接转派给市住房和城乡建设局（2级）
            - 正确路径：赛罕区住房和城乡建设局 → 赛罕区分中心 → 市接诉即办 → 市住房和城乡建设局
            - 如有疑问，可使用可选工具进行验证
            """

    def _process_response(self, response) -> str:
        """处理大模型响应"""
        if hasattr(response, "messages") and response.messages:
            return response.messages[-1].content
        else:
            return str(response)

# 使用示例
if __name__ == "__main__":
    import asyncio

    async def test_enhanced_assistant():
        assistant = EnhancedReturnedOrderAssistant(
            query="市民反映：出租车司机收取回扣问题，当前处置部门：市公安局"
        )
        result = await assistant.handle_returned()
        print("分析结果:", result)

    asyncio.run(test_enhanced_assistant())
