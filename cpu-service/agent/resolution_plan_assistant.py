"""推荐处置方案助手"""
import asyncio
import os
import time
import logging

from autogen_agentchat.agents import AssistantAgent
from autogen_core import CancellationToken
from autogen_core.models import ModelFamily
from autogen_ext.models.openai import OpenAIChatCompletionClient
from cores.config import my_settings

# 设置日志记录器
logger = logging.getLogger(__name__)

class ResolutionAssistant:
    def __init__(self, query: str=None):
        self.query = query

    async def recommend_solution(self) -> str:
        # 记录开始时间
        start_time = time.time()
        logger.info(f"开始生成处置方案推荐: 查询长度={len(self.query) if self.query else 0}")
        
        model_client = OpenAIChatCompletionClient(
            model=my_settings.LLM_MODEL,
            base_url=my_settings.BASE_URL,
            api_key=my_settings.API_KEY,
            model_info={
                "vision": False,
                "function_calling": True,
                "json_output": True,
                "family": ModelFamily.UNKNOWN,
                "structured_output": False
            },
        )

        system_message = """
            你是工单处置方案专家，负责根据工单内容和本部门历史工单处理经验，给当前工单推荐最佳处置方案。

            请仔细分析工单内容，参考本部门历史上处理过的类似案例，提供一个完整的处置方案，包含以下两个部分：

            ## 处置步骤
            请列出处理该工单的具体操作步骤，包括：
            1. 初步核实与了解情况的方法
            2. 需要联系的相关单位或人员
            3. 实地调查或处理的具体流程
            4. 后续跟进与回访安排
            5. 可能遇到的困难和应对措施

            ## 答复模板
            请提供一份给诉求人的正式答复模板，要求：
            1. 开头表达对诉求人问题的理解和重视
            2. 中间说明处理过程和结果
            3. 结尾提供后续服务保障和联系方式
            4. 语言应简洁、专业、亲切且易于理解

            **重要提示**：
            - 必须基于本部门历史工单中类似问题的成功处理经验来制定方案
            - 确保方案符合部门职责范围和工作规范
            - 注重处理效率和提高市民满意度
            - 答复模板应体现政府服务的专业性和亲民性
        """
        assistant = AssistantAgent(
            "assistant",
            model_client=model_client,
            system_message=system_message
        )

        # 记录模型调用开始时间
        model_start_time = time.time()
        logger.info("开始调用大模型生成处置方案")

        response = await assistant.run(task=f"请根据以下信息{self.query}，制定一个处置方案。",
                               cancellation_token=CancellationToken())

        # 记录模型调用结束时间和耗时
        model_elapsed_time = time.time() - model_start_time
        logger.info(f"大模型调用完成: 耗时={model_elapsed_time:.2f}秒")

        await model_client.close()
        
        # 计算并记录总耗时
        elapsed_time = time.time() - start_time
        logger.info(f"处置方案推荐完成: 总耗时={elapsed_time:.2f}秒")
        
        return response.messages[-1].content if hasattr(response, "messages") else response


if __name__ == "__main__":
    resolution = ResolutionAssistant(query=["市民反映：2025年3月20日12点左右，乘坐车牌号：蒙AY5463的出租车从新城区文苑街大学城到赛罕区机场路附近的宾馆入住，但因为出租车拿了宾馆的提成导致市民无法退房，市民对此不认可，市民诉求：请有关部门协助处理出租车吃回扣问题，"])
    print(asyncio.run(resolution.recommend_solution()))
