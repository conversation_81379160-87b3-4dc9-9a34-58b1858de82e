"""部门管理模块

提供部门层级结构、关系查询和格式化输出的统一接口。
整合了原有的department_relations_helper.py和enhanced_department_structure.py的功能。
"""

import json
import os
import logging
from typing import Dict, List, Any, Optional, Tuple

logger = logging.getLogger(__name__)

class DepartmentManager:
    """部门管理类

    统一管理部门层级结构、关系查询和格式化输出。
    """

    def __init__(self):
        """初始化部门管理类"""
        self.departments_data = {}
        self.relations_data = {}
        self.loaded = False

    def load_data(self) -> bool:
        """加载部门数据和工作流规则，实现懒加载和缓存

        Returns:
            bool: 加载是否成功
        """
        if self.loaded:
            return True

        try:
            # 尝试从相对路径加载
            base_paths = [
                '',  # 当前目录
                os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # 项目根目录
            ]

            for base_path in base_paths:
                departments_path = os.path.join(base_path, 'data/dept-level/department_hierarchy.json')
                relations_path = os.path.join(base_path, 'data/dept-level/department_workflow_rules.json')

                if os.path.exists(departments_path) and os.path.exists(relations_path):
                    with open(departments_path, 'r', encoding='utf-8') as f_dept:
                        self.departments_data = json.load(f_dept)

                    with open(relations_path, 'r', encoding='utf-8') as f_rel:
                        self.relations_data = json.load(f_rel)

                    self.loaded = True
                    logger.info(f"成功加载部门数据: {len(self.departments_data.get('hierarchy', {}).get('level_1', {})) if 'hierarchy' in self.departments_data else 0} 个一级部门")
                    logger.info(f"成功加载特殊关系: {len(self.relations_data.get('special_relations', []))} 个特殊关系")
                    return True

            logger.error("未找到部门数据文件")
            return False

        except Exception as e:
            logger.error(f"加载部门数据失败: {str(e)}")
            return False

    def _process_department_data(self, department_name: str, processor_func):
        """统一处理部门数据的方法

        Args:
            department_name: 部门名称
            processor_func: 处理函数，接收部门名称、层级、当前名称和父级名称

        Returns:
            处理函数的返回值
        """
        if not self.loaded and not self.load_data():
            return None

        # 检查新格式的数据结构
        if "metadata" in self.departments_data and "hierarchy" in self.departments_data:
            hierarchy = self.departments_data["hierarchy"]

            # 检查一级部门
            for level1_name in hierarchy["level_1"].keys():
                if department_name == level1_name or department_name == "市接诉即办中心":
                    return processor_func(department_name, 1, level1_name, None)

                # 检查二级部门
                for level2_name in hierarchy["level_1"][level1_name]["level_2"].keys():
                    if department_name == level2_name:
                        return processor_func(department_name, 2, level2_name, level1_name)

                    # 检查三级部门
                    if "level_3" in hierarchy["level_1"][level1_name]["level_2"][level2_name]:
                        for level3_name in hierarchy["level_1"][level1_name]["level_2"][level2_name]["level_3"]:
                            if department_name == level3_name:
                                return processor_func(department_name, 3, level3_name, level2_name)
        else:
            # 旧格式的数据结构
            # 检查一级部门
            if department_name == "市接诉即办中心" or department_name == "市接诉即办":
                return processor_func(department_name, 1, department_name, None)

            # 检查二级部门
            if department_name in self.departments_data.get("市接诉即办", {}):
                return processor_func(department_name, 2, department_name, "市接诉即办中心")

            # 检查三级部门
            for level2_dept, level3_depts in self.departments_data.get("市接诉即办", {}).items():
                if department_name in level3_depts:
                    return processor_func(department_name, 3, department_name, level2_dept)

        # 检查特殊关系
        for relation in self.relations_data.get("special_relations", []):
            if department_name in relation.get("children", []):
                return processor_func(department_name, 3, department_name, relation.get("parent"))

        # 检查特殊部门规则
        if "special_department_rules" in self.relations_data:
            for rule_key in self.relations_data["special_department_rules"].keys():
                dept1, dept2 = rule_key.split("-")
                if department_name == dept1 or department_name == dept2:
                    return processor_func(department_name, 2, department_name, "市接诉即办中心")

        # 如果是常见的市直部门，则返回2级
        if department_name.startswith("市") and len(department_name) > 1:
            return processor_func(department_name, 2, department_name, "市接诉即办中心")

        # 如果是常见的区分中心，则返回2级
        if "区分中心" in department_name:
            return processor_func(department_name, 2, department_name, "市接诉即办中心")

        return processor_func(department_name, None, department_name, None)

    def get_department_level(self, department_name: str) -> Optional[int]:
        """获取部门的层级

        Args:
            department_name: 部门名称

        Returns:
            Optional[int]: 部门层级（1、2或3），如果未找到则返回None
        """
        def level_processor(name, level, current, parent):
            return level

        return self._process_department_data(department_name, level_processor)

    def get_parent_department(self, department_name: str) -> Optional[str]:
        """获取部门的上级部门

        Args:
            department_name: 部门名称

        Returns:
            Optional[str]: 上级部门名称，如果未找到则返回None
        """
        def parent_processor(name, level, current, parent):
            return parent

        return self._process_department_data(department_name, parent_processor)

    def is_special_relation(self, department_name: str) -> bool:
        """检查部门是否属于特殊关系

        Args:
            department_name: 部门名称

        Returns:
            bool: 是否属于特殊关系
        """
        if not self.loaded and not self.load_data():
            return False

        for relation in self.relations_data.get("special_relations", []):
            if department_name in relation.get("children", []):
                return True

        return False

    def can_assign_to(self, from_dept: str, to_dept: str) -> Tuple[bool, str]:
        """检查是否可以从一个部门派单给另一个部门

        Args:
            from_dept: 派单部门
            to_dept: 接收部门

        Returns:
            Tuple[bool, str]: (是否可以派单, 原因)
        """
        if not self.loaded and not self.load_data():
            return False, "部门数据未加载"

        # 检查是否是特殊部门规则
        special_rule_key = f"{from_dept}-{to_dept}"
        if "special_department_rules" in self.relations_data and special_rule_key in self.relations_data["special_department_rules"]:
            rule = self.relations_data["special_department_rules"][special_rule_key]
            if rule.get("dispatch", False):
                return True, rule.get("description", "符合特殊派单规则")
            else:
                return False, rule.get("dispatch_restriction", "不符合特殊派单规则")

        from_level = self.get_department_level(from_dept)
        to_level = self.get_department_level(to_dept)

        if from_level is None:
            return False, f"未找到部门: {from_dept}"

        if to_level is None:
            return False, f"未找到部门: {to_dept}"

        # 检查是否符合派单规则
        assignment_rules = self.relations_data.get("workflow_rules", {}).get("assignment_rules", [])
        for rule in assignment_rules:
            if rule.get("from_level") == from_level and to_level in rule.get("to_levels", []):
                # 检查是否是直接下级
                if to_level == from_level + 1:
                    # 对于二级到三级的派单，需要检查是否是其下级部门
                    if from_level == 2:
                        parent = self.get_parent_department(to_dept)
                        if parent != from_dept:
                            # 检查特殊关系
                            if self.is_special_relation(to_dept):
                                return False, f"{to_dept}是特殊关系部门，不是{from_dept}的直接下级"
                            return False, f"{to_dept}不是{from_dept}的直接下级"
                    return True, "符合派单规则"

        return False, f"不符合派单规则: {from_level}级部门不能派单给{to_level}级部门"

    def can_return_to(self, from_dept: str, to_dept: str) -> Tuple[bool, str]:
        """检查是否可以从一个部门退单给另一个部门

        Args:
            from_dept: 退单部门
            to_dept: 接收部门

        Returns:
            Tuple[bool, str]: (是否可以退单, 原因)
        """
        if not self.loaded and not self.load_data():
            return False, "部门数据未加载"

        # 检查是否是特殊部门规则
        special_rule_key = f"{from_dept}-{to_dept}"
        if "special_department_rules" in self.relations_data and special_rule_key in self.relations_data["special_department_rules"]:
            rule = self.relations_data["special_department_rules"][special_rule_key]
            if rule.get("return", False):
                return True, rule.get("description", "符合特殊退单规则")
            else:
                return False, rule.get("return_restriction", "不符合特殊退单规则")

        from_level = self.get_department_level(from_dept)
        to_level = self.get_department_level(to_dept)

        if from_level is None:
            return False, f"未找到部门: {from_dept}"

        if to_level is None:
            return False, f"未找到部门: {to_dept}"

        # 检查是否符合退单规则
        return_rules = self.relations_data.get("workflow_rules", {}).get("return_rules", [])
        for rule in return_rules:
            if rule.get("from_level") == from_level and to_level in rule.get("to_levels", []):
                # 检查是否是直接上级
                parent = self.get_parent_department(from_dept)
                if parent != to_dept:
                    return False, f"{to_dept}不是{from_dept}的直接上级"
                return True, "符合退单规则"

        return False, f"不符合退单规则: {from_level}级部门不能退单给{to_level}级部门"

    def generate_enhanced_structure(self, full_structure=True) -> str:
        """生成增强的部门层级结构字符串

        Args:
            full_structure: 是否生成完整结构，False则生成简化版本

        Returns:
            str: 格式化的部门层级结构字符串
        """
        if not self.loaded and not self.load_data():
            return "部门数据未加载"

        # 如果需要简化版本，则调用简化版生成函数
        if not full_structure:
            return self.generate_simplified_structure()

        result = []

        # 添加标题
        result.append("# 呼和浩特市接诉即办系统部门层级结构")
        result.append("")

        # 添加层级说明
        result.append("## 层级说明")
        result.append("- 一级部门：市接诉即办中心")
        result.append("- 二级部门：市直部门、旗县区分中心和市属国企")
        result.append("- 三级部门：属地部门")
        result.append("")

        # 添加部门层级结构
        result.append("## 部门层级结构")

        # 处理不同格式的部门数据
        if "metadata" in self.departments_data and "hierarchy" in self.departments_data:
            # 新格式
            hierarchy = self.departments_data["hierarchy"]

            # 遍历一级部门
            for level1_name, level1_data in hierarchy["level_1"].items():
                result.append(f"### 一级部门：{level1_name}")

                # 遍历二级部门
                for level2_name, level2_data in level1_data["level_2"].items():
                    result.append(f"#### 二级部门：{level2_name}")

                    # 遍历三级部门
                    level3_depts = level2_data.get("level_3", [])
                    if level3_depts:
                        result.append("##### 三级部门：")
                        for level3_name in level3_depts:
                            result.append(f"- {level3_name}")
                    else:
                        result.append("##### 无三级部门")

                    result.append("")  # 添加空行分隔
        else:
            # 旧格式
            # 遍历一级部门
            for level1_name, level2_depts in self.departments_data.items():
                result.append(f"### 一级部门：{level1_name}")

                # 遍历二级部门
                for level2_name, level3_depts in level2_depts.items():
                    result.append(f"#### 二级部门：{level2_name}")

                    # 遍历三级部门
                    if level3_depts:
                        result.append("##### 三级部门：")
                        for level3_name in level3_depts:
                            result.append(f"- {level3_name}")
                    else:
                        result.append("##### 无三级部门")

                    result.append("")  # 添加空行分隔

        # 添加特殊关系说明
        if "special_relations" in self.relations_data:
            result.append("## 特殊部门关系")

            for relation in self.relations_data.get("special_relations", []):
                parent = relation.get("parent")
                children = relation.get("children", [])
                note = relation.get("note", "")

                result.append(f"### {parent}")
                result.append(f"**关系说明**：{note}")
                result.append("**下属部门**：")
                for child in children:
                    result.append(f"- {child}")

                result.append("")  # 添加空行分隔

        # 添加工单流转规则
        if "workflow_rules" in self.relations_data:
            result.append("## 工单流转规则")

            # 派单规则
            if "assignment_rules" in self.relations_data["workflow_rules"]:
                result.append("### 派单规则")
                for rule in self.relations_data["workflow_rules"]["assignment_rules"]:
                    result.append(f"- {rule['description']}")
                result.append("")

            # 退回规则
            if "return_rules" in self.relations_data["workflow_rules"]:
                result.append("### 退回规则")
                for rule in self.relations_data["workflow_rules"]["return_rules"]:
                    result.append(f"- {rule['description']}")
                result.append("")

            # 转派限制
            if "transfer_restrictions" in self.relations_data["workflow_rules"]:
                result.append("### 转派限制")
                for rule in self.relations_data["workflow_rules"]["transfer_restrictions"]:
                    result.append(f"- {rule['description']}")
                    if "example" in rule:
                        result.append(f"  例如：{rule['example']}")
                result.append("")

            # 跨级限制
            if "cross_level_restrictions" in self.relations_data["workflow_rules"]:
                result.append("### 跨级限制")
                for rule in self.relations_data["workflow_rules"]["cross_level_restrictions"]:
                    result.append(f"- {rule['description']}")
                    if "example" in rule:
                        result.append(f"  例如：{rule['example']}")
                result.append("")

        # 添加决策原则
        if "decision_principles" in self.relations_data:
            result.append("## 决策原则")

            for principle in self.relations_data["decision_principles"]:
                result.append(f"### {principle['name']}")
                result.append(f"{principle['description']}")
                result.append("")

        return "\n".join(result)

    def generate_simplified_structure(self) -> str:
        """生成简化版的部门层级结构

        Returns:
            str: 简化的部门层级结构字符串
        """
        if not self.loaded and not self.load_data():
            return "部门数据未加载"

        result = []

        # 添加标题
        result.append("# 呼和浩特市接诉即办系统部门层级结构（简化版）")
        result.append("")

        # 添加层级说明
        result.append("## 层级说明")
        result.append("- 一级部门：市接诉即办中心")
        result.append("- 二级部门：市直部门、旗县区分中心和市属国企")
        result.append("- 三级部门：属地部门")
        result.append("")

        # 处理不同格式的部门数据
        if "metadata" in self.departments_data and "hierarchy" in self.departments_data:
            # 新格式
            hierarchy = self.departments_data["hierarchy"]

            # 添加一级部门概述
            result.append("## 一级部门概述")
            for level1_name in hierarchy["level_1"].keys():
                result.append(f"- {level1_name}")
            result.append("")

            # 添加二级部门概述（只列出部分主要部门）
            result.append("## 二级部门概述（部分）")
            level2_count = 0
            for level1_name, level1_data in hierarchy["level_1"].items():
                for level2_name in level1_data["level_2"].keys():
                    if level2_count < 10:  # 只列出前10个二级部门
                        result.append(f"- {level2_name}")
                        level2_count += 1
            result.append("- ... （更多二级部门）")
            result.append("")

            # 添加三级部门示例
            result.append("## 三级部门示例")
            level3_count = 0
            for level1_name, level1_data in hierarchy["level_1"].items():
                for level2_name, level2_data in level1_data["level_2"].items():
                    if "level_3" in level2_data:
                        for level3_name in level2_data["level_3"]:
                            if level3_count < 10:  # 只列出前10个三级部门
                                result.append(f"- {level3_name} (附属于: {level2_name})")
                                level3_count += 1
            result.append("- ... （更多三级部门）")
            result.append("")
        else:
            # 旧格式
            # 添加一级部门概述
            result.append("## 一级部门概述")
            for level1_name in self.departments_data.keys():
                result.append(f"- {level1_name}")
            result.append("")

            # 添加二级部门概述（只列出部分主要部门）
            result.append("## 二级部门概述（部分）")
            level2_count = 0
            for level1_name, level2_depts in self.departments_data.items():
                for level2_name in level2_depts.keys():
                    if level2_count < 10:  # 只列出前10个二级部门
                        result.append(f"- {level2_name}")
                        level2_count += 1
            result.append("- ... （更多二级部门）")
            result.append("")

            # 添加三级部门示例
            result.append("## 三级部门示例")
            level3_count = 0
            for level1_name, level2_depts in self.departments_data.items():
                for level2_name, level3_depts in level2_depts.items():
                    for level3_name in level3_depts:
                        if level3_count < 10:  # 只列出前10个三级部门
                            result.append(f"- {level3_name} (附属于: {level2_name})")
                            level3_count += 1
            result.append("- ... （更多三级部门）")
            result.append("")

        # 添加工单流转规则概述
        if "workflow_rules" in self.relations_data:
            result.append("## 工单流转规则概述")

            # 添加派单规则概述
            if "assignment_rules" in self.relations_data["workflow_rules"]:
                result.append("### 派单规则")
                for rule in self.relations_data["workflow_rules"]["assignment_rules"][:3]:  # 只列出前3条规则
                    result.append(f"- {rule['description']}")
                result.append("")

            # 添加退回规则概述
            if "return_rules" in self.relations_data["workflow_rules"]:
                result.append("### 退回规则")
                for rule in self.relations_data["workflow_rules"]["return_rules"][:3]:  # 只列出前3条规则
                    result.append(f"- {rule['description']}")
                result.append("")

        # 添加说明
        result.append("## 注意")
        result.append("这只是部门结构的简化概览。使用部门查询工具可获取任何部门的详细信息和关系。")

        return "\n".join(result)

    def get_relations_summary(self) -> str:
        """获取部门关系和工单流转规则的摘要

        Returns:
            str: 部门关系和工单流转规则的摘要
        """
        if not self.loaded and not self.load_data():
            return "部门数据未加载"

        summary = []

        # 添加部门层级信息
        summary.append("## 部门层级结构")
        for level, info in self.relations_data.get("department_levels", {}).items():
            summary.append(f"{info['name']}: {info['description']}")
            summary.append(f"  示例: {', '.join(info['examples'])}")

        # 添加特殊关系信息
        summary.append("\n## 特殊部门关系")
        for relation in self.relations_data.get("special_relations", []):
            summary.append(f"{relation['parent']} → {', '.join(relation['children'])}")
            summary.append(f"  说明: {relation['note']}")

        # 添加工单流转规则
        summary.append("\n## 工单流转规则")

        # 派单规则
        summary.append("### 派单规则")
        for rule in self.relations_data.get("workflow_rules", {}).get("assignment_rules", []):
            summary.append(f"  {rule['description']}")

        # 退单规则
        summary.append("### 退单规则")
        for rule in self.relations_data.get("workflow_rules", {}).get("return_rules", []):
            summary.append(f"  {rule['description']}")

        # 转派限制
        summary.append("### 转派限制")
        for rule in self.relations_data.get("workflow_rules", {}).get("transfer_restrictions", []):
            summary.append(f"  {rule['description']}")
            summary.append(f"    示例: {rule['example']}")

        # 跨级限制
        summary.append("### 跨级限制")
        for rule in self.relations_data.get("workflow_rules", {}).get("cross_level_restrictions", []):
            summary.append(f"  {rule['description']}")
            summary.append(f"    示例: {rule['example']}")

        # 特殊领域规则
        summary.append("\n## 特殊领域规则")
        for rule in self.relations_data.get("special_domain_rules", []):
            summary.append(f"{rule['domain']} - 负责部门: {rule['responsible_department']}")
            summary.append(f"  范围: {', '.join(rule['scope'])}")
            summary.append(f"  规则: {rule['rule']}")

        return "\n".join(summary)

# 单例模式
department_manager = DepartmentManager()

# 测试代码
if __name__ == "__main__":
    # 测试部门管理器
    manager = DepartmentManager()
    if manager.load_data():
        print("数据加载成功")

        # 测试部门层级查询
        print(f"市公安局的层级: {manager.get_department_level('市公安局')}")
        print(f"回民区文体旅游广电局的层级: {manager.get_department_level('回民区文体旅游广电局')}")
        print(f"玉泉区自然资源分局的层级: {manager.get_department_level('玉泉区自然资源分局')}")

        # 测试特殊关系查询
        print(f"玉泉区自然资源分局是否特殊关系: {manager.is_special_relation('玉泉区自然资源分局')}")

        # 测试上级部门查询
        print(f"玉泉区自然资源分局的上级部门: {manager.get_parent_department('玉泉区自然资源分局')}")

        # 测试派单规则验证
        print(f"市接诉即办中心能否派单给回民区分中心: {manager.can_assign_to('市接诉即办中心', '回民区分中心')}")
        print(f"市接诉即办中心能否派单给回民区文体旅游广电局: {manager.can_assign_to('市接诉即办中心', '回民区文体旅游广电局')}")

        # 测试退单规则验证
        print(f"回民区文体旅游广电局能否退单给回民区分中心: {manager.can_return_to('回民区文体旅游广电局', '回民区分中心')}")
        print(f"回民区文体旅游广电局能否退单给市接诉即办中心: {manager.can_return_to('回民区文体旅游广电局', '市接诉即办中心')}")

        # 测试结构生成
        simplified = manager.generate_simplified_structure()
        print("\n简化版部门结构（前100个字符）:")
        print(simplified[:100] + "...")

        # 测试关系摘要
        summary = manager.get_relations_summary()
        print("\n关系摘要（前100个字符）:")
        print(summary[:100] + "...")
    else:
        print("数据加载失败")
