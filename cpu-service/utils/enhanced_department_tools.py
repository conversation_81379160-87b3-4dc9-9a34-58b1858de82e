"""增强的部门工具模块

该模块提供了更稳健、更专业的工具调用实现，包括：
- 统一的错误处理和异常管理
- 性能监控和日志记录
- 参数验证和类型安全
- 调用限制和保护机制
- 重试机制和超时保护
"""

import asyncio
import functools
import logging
import time
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass
from enum import Enum

from department_manager import department_manager

# 设置日志记录器
logger = logging.getLogger(__name__)

class ToolCallStatus(Enum):
    """工具调用状态枚举"""
    SUCCESS = "success"
    ERROR = "error"
    TIMEOUT = "timeout"
    RATE_LIMITED = "rate_limited"

@dataclass
class ToolCallResult:
    """工具调用结果"""
    status: ToolCallStatus
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None
    call_count: Optional[int] = None

class ToolCallLimiter:
    """工具调用限制器"""

    def __init__(self, max_calls_per_minute: int = 60, max_calls_per_session: int = 100):
        self.max_calls_per_minute = max_calls_per_minute
        self.max_calls_per_session = max_calls_per_session
        self.call_history = []
        self.session_calls = 0

    def can_call(self) -> bool:
        """检查是否可以进行调用"""
        now = time.time()

        # 清理1分钟前的调用记录
        self.call_history = [call_time for call_time in self.call_history if now - call_time < 60]

        # 检查频率限制
        if len(self.call_history) >= self.max_calls_per_minute:
            return False

        # 检查会话限制
        if self.session_calls >= self.max_calls_per_session:
            return False

        return True

    def record_call(self):
        """记录一次调用"""
        self.call_history.append(time.time())
        self.session_calls += 1

# 全局调用限制器
call_limiter = ToolCallLimiter()

def tool_wrapper(
    timeout: float = 5.0,
    max_retries: int = 1,  # 多容器架构：减少重试次数
    enable_rate_limit: bool = False,  # 多容器架构：禁用频率限制
    log_calls: bool = True
):
    """工具函数装饰器，提供统一的错误处理、性能监控和保护机制

    Args:
        timeout: 超时时间（秒）
        max_retries: 最大重试次数
        enable_rate_limit: 是否启用频率限制
        log_calls: 是否记录调用日志
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> ToolCallResult:
            func_name = func.__name__
            start_time = time.time()

            # 检查频率限制
            if enable_rate_limit and not call_limiter.can_call():
                logger.warning(f"工具调用频率限制: {func_name}")
                return ToolCallResult(
                    status=ToolCallStatus.RATE_LIMITED,
                    error="调用频率过高，请稍后重试",
                    execution_time=0
                )

            # 记录调用
            if enable_rate_limit:
                call_limiter.record_call()

            if log_calls:
                logger.info(f"开始调用工具: {func_name}, 参数: {args}, {kwargs}")

            # 重试机制
            last_error = None
            for attempt in range(max_retries + 1):
                try:
                    # 使用asyncio.wait_for实现超时控制
                    result = await asyncio.wait_for(
                        func(*args, **kwargs),
                        timeout=timeout
                    )

                    execution_time = time.time() - start_time

                    if log_calls:
                        logger.info(f"工具调用成功: {func_name}, 耗时: {execution_time:.3f}秒")

                    # 记录性能统计
                    performance_logger.info(f"工具调用-{func_name}: 耗时={execution_time:.3f}秒, 尝试次数={attempt + 1}")

                    return ToolCallResult(
                        status=ToolCallStatus.SUCCESS,
                        data=result,
                        execution_time=execution_time,
                        call_count=call_limiter.session_calls
                    )

                except asyncio.TimeoutError:
                    last_error = f"工具调用超时: {func_name} (超时时间: {timeout}秒)"
                    logger.warning(f"{last_error}, 尝试次数: {attempt + 1}")

                except Exception as e:
                    last_error = f"工具调用异常: {func_name}, 错误: {str(e)}"
                    logger.error(f"{last_error}, 尝试次数: {attempt + 1}")

                # 如果不是最后一次尝试，等待一段时间再重试
                if attempt < max_retries:
                    await asyncio.sleep(0.5 * (attempt + 1))  # 指数退避

            # 所有重试都失败
            execution_time = time.time() - start_time
            logger.error(f"工具调用最终失败: {func_name}, 总耗时: {execution_time:.3f}秒")

            return ToolCallResult(
                status=ToolCallStatus.TIMEOUT if "超时" in last_error else ToolCallStatus.ERROR,
                error=last_error,
                execution_time=execution_time,
                call_count=call_limiter.session_calls
            )

        return wrapper
    return decorator

def validate_department_name(department_name: str) -> bool:
    """验证部门名称格式"""
    if not department_name or not isinstance(department_name, str):
        return False

    # 去除首尾空格
    department_name = department_name.strip()

    # 检查长度
    if len(department_name) < 2 or len(department_name) > 50:
        return False

    # 检查是否包含特殊字符（可根据需要调整）
    invalid_chars = ['<', '>', '&', '"', "'", '\\', '/', '\n', '\r', '\t']
    if any(char in department_name for char in invalid_chars):
        return False

    return True

def validate_action_type(action_type: str) -> bool:
    """验证操作类型"""
    return action_type in ["dispatch", "return"]

# 增强的工具函数实现

@tool_wrapper(timeout=3.0, max_retries=1, log_calls=True)
async def enhanced_get_department_info(department_name: str) -> Dict[str, Any]:
    """
    获取部门详细信息（增强版）

    Args:
        department_name: 部门名称

    Returns:
        部门详细信息，包括ID、名称、层级等
    """
    # 参数验证
    if not validate_department_name(department_name):
        return {"error": "部门名称格式无效"}

    department_name = department_name.strip()

    try:
        # 获取部门层级
        level = department_manager.get_department_level(department_name)

        # 获取上级部门
        parent = department_manager.get_parent_department(department_name)

        # 构建详细信息
        details = {
            "name": department_name,
            "level": level,
            "level_description": _get_level_description(level),
            "parent_department": parent,
            "is_special_relation": department_manager.is_special_relation(department_name),
            "query_time": time.time()
        }

        return details

    except Exception as e:
        logger.error(f"获取部门信息时发生异常: {department_name}, 错误: {str(e)}")
        return {"error": f"获取部门信息失败: {str(e)}"}

@tool_wrapper(timeout=3.0, max_retries=1, log_calls=True)
async def enhanced_get_department_hierarchy(department_name: str) -> Dict[str, Any]:
    """
    获取部门的层级关系（增强版）

    Args:
        department_name: 部门名称

    Returns:
        部门的层级关系，包括上级部门、下级部门等
    """
    # 参数验证
    if not validate_department_name(department_name):
        return {"error": "部门名称格式无效"}

    department_name = department_name.strip()

    try:
        # 获取部门层级
        level = department_manager.get_department_level(department_name)
        if level is None:
            return {"error": f"未找到部门: {department_name}"}

        # 获取上级部门
        parent = department_manager.get_parent_department(department_name)

        # 构建层级关系
        hierarchy = {
            "department": department_name,
            "level": level,
            "level_description": _get_level_description(level),
            "parent_department": parent,
            "query_time": time.time()
        }

        # 添加下级部门逻辑（简化版，完整实现需要更多代码）
        children = []
        if level == 1:
            # 一级部门逻辑
            pass
        elif level == 2:
            # 二级部门逻辑
            pass

        hierarchy["children"] = children
        return hierarchy

    except Exception as e:
        logger.error(f"获取部门层级关系时发生异常: {department_name}, 错误: {str(e)}")
        return {"error": f"获取部门层级关系失败: {str(e)}"}

@tool_wrapper(timeout=5.0, max_retries=1, log_calls=True)
async def enhanced_validate_return_workflow(current_department: str, target_department: str) -> Dict[str, Any]:
    """
    验证退回工单的流转路径是否合规（增强版）

    Args:
        current_department: 当前处置部门
        target_department: 目标处置部门

    Returns:
        验证结果，包括是否合规、正确的流转路径等
    """
    # 参数验证
    if not validate_department_name(current_department):
        return {"error": "当前部门名称格式无效"}

    if not validate_department_name(target_department):
        return {"error": "目标部门名称格式无效"}

    current_department = current_department.strip()
    target_department = target_department.strip()

    try:
        # 直接实现验证逻辑，不再依赖已删除的department_tools
        current_level = department_manager.get_department_level(current_department)
        target_level = department_manager.get_department_level(target_department)

        # 获取上级部门
        current_parent = department_manager.get_parent_department(current_department)

        result = {
            "current_department": current_department,
            "current_level": current_level,
            "target_department": target_department,
            "target_level": target_level,
            "is_valid_direct_return": False,
            "correct_return_path": [],
            "reason": "",
            "recommendation": ""
        }

        # 检查是否可以直接退回
        can_return, return_reason = department_manager.can_return_to(current_department, target_department)

        if can_return:
            result["is_valid_direct_return"] = True
            result["correct_return_path"] = [current_department, target_department]
            result["reason"] = return_reason
            result["recommendation"] = f"{current_department}可以直接退回给{target_department}"
        else:
            # 不能直接退回，需要构建正确的退回路径
            result["reason"] = return_reason

            # 构建正确的退回路径
            if current_level and target_level:
                if current_level > target_level:
                    # 向上退回，需要通过上级部门
                    if current_parent:
                        result["correct_return_path"] = [current_department, current_parent, target_department]
                        result["recommendation"] = f"{current_department}应先退回给{current_parent}，再由{current_parent}退回给{target_department}"
                    else:
                        result["recommendation"] = f"{current_department}无法退回给{target_department}：{return_reason}"
                else:
                    result["recommendation"] = f"{current_department}无法退回给{target_department}：{return_reason}"
            else:
                result["recommendation"] = f"{current_department}无法退回给{target_department}：{return_reason}"

        # 添加查询时间戳
        result["query_time"] = time.time()

        return result

    except Exception as e:
        logger.error(f"验证退回工单流转路径时发生异常: {current_department} -> {target_department}, 错误: {str(e)}")
        return {"error": f"验证退回工单流转路径失败: {str(e)}"}

@tool_wrapper(timeout=5.0, max_retries=1, log_calls=True)
async def enhanced_get_correct_workflow_recommendation(current_department: str, target_department: str, action_type: str = "return") -> Dict[str, Any]:
    """
    获取正确的工单流转建议（增强版）

    Args:
        current_department: 当前处置部门
        target_department: 目标处置部门
        action_type: 操作类型，"return"（退回）或"dispatch"（派单）

    Returns:
        正确的流转建议
    """
    # 参数验证
    if not validate_department_name(current_department):
        return {"error": "当前部门名称格式无效"}

    if not validate_department_name(target_department):
        return {"error": "目标部门名称格式无效"}

    if action_type not in ["return", "dispatch"]:
        return {"error": "操作类型必须是 'return' 或 'dispatch'"}

    current_department = current_department.strip()
    target_department = target_department.strip()

    try:
        # 直接实现工作流建议逻辑，不再依赖已删除的department_tools
        if action_type == "return":
            # 调用退回验证逻辑
            result = await enhanced_validate_return_workflow(current_department, target_department)
        else:
            # 派单验证逻辑
            can_dispatch, dispatch_reason = department_manager.can_assign_to(current_department, target_department)

            result = {
                "current_department": current_department,
                "target_department": target_department,
                "action_type": action_type,
                "is_valid": can_dispatch,
                "reason": dispatch_reason,
                "recommendation": ""
            }

            if can_dispatch:
                result["recommendation"] = f"{current_department}可以直接派单给{target_department}"
            else:
                # 构建正确的派单路径
                current_level = department_manager.get_department_level(current_department)
                target_level = department_manager.get_department_level(target_department)

                if current_level and target_level and current_level < target_level:
                    # 向下派单，构建路径
                    current_parent = department_manager.get_parent_department(target_department)
                    if current_parent and current_parent == current_department:
                        result["correct_path"] = [current_department, target_department]
                        result["recommendation"] = f"正确的派单路径：{current_department} → {target_department}"
                    else:
                        result["recommendation"] = f"{current_department}无法派单给{target_department}：{dispatch_reason}"
                else:
                    result["recommendation"] = f"{current_department}无法派单给{target_department}：{dispatch_reason}"

        # 添加查询时间戳
        result["query_time"] = time.time()

        return result

    except Exception as e:
        logger.error(f"获取工单流转建议时发生异常: {current_department} -> {target_department} ({action_type}), 错误: {str(e)}")
        return {"error": f"获取工单流转建议失败: {str(e)}"}

def _get_level_description(level: Optional[int]) -> str:
    """获取层级描述"""
    if level is None:
        return "未知层级"

    level_descriptions = {
        1: "一级单位（市级中心单位）",
        2: "二级单位（市直部门、旗县区分中心和市属国企）",
        3: "三级单位（属地部门）"
    }

    return level_descriptions.get(level, f"未知层级({level})")

# 工具调用统计和监控
class ToolCallMonitor:
    """工具调用监控器"""

    def __init__(self):
        self.call_stats = {}
        self.error_stats = {}

    def record_call(self, tool_name: str, result: ToolCallResult):
        """记录工具调用统计"""
        if tool_name not in self.call_stats:
            self.call_stats[tool_name] = {
                "total_calls": 0,
                "success_calls": 0,
                "error_calls": 0,
                "timeout_calls": 0,
                "total_time": 0.0,
                "avg_time": 0.0
            }

        stats = self.call_stats[tool_name]
        stats["total_calls"] += 1

        if result.execution_time:
            stats["total_time"] += result.execution_time
            stats["avg_time"] = stats["total_time"] / stats["total_calls"]

        if result.status == ToolCallStatus.SUCCESS:
            stats["success_calls"] += 1
        elif result.status == ToolCallStatus.ERROR:
            stats["error_calls"] += 1
        elif result.status == ToolCallStatus.TIMEOUT:
            stats["timeout_calls"] += 1

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "call_stats": self.call_stats,
            "session_total_calls": call_limiter.session_calls,
            "current_rate_limit": len(call_limiter.call_history)
        }

# 全局监控器
tool_monitor = ToolCallMonitor()
