# CPU Service Dependencies - Main Business Service (Updated 2025-06-12)
# 现代化升级策略：使用最新稳定版本，提升性能和安全性

# Core Web Framework (最新稳定版本)
fastapi>=0.115.0  # 最新稳定版本，性能和功能改进
uvicorn[standard]>=0.30.6  # 最新稳定版本，支持最新特性
pydantic>=2.11.0  # 最新稳定版本，性能优化
pydantic-settings>=2.6.0  # 最新稳定版本
requests>=2.32.0  # 安全更新
python-multipart>=0.0.12  # 最新稳定版本
python-dotenv>=1.0.1  # 最新版本
pyyaml>=6.0.2  # 安全更新

# LlamaIndex Core (升级到最新稳定版本)
llama-index>=0.12.40  # 升级到最新稳定版本，重大功能改进
llama-index-core>=0.12.40  # 升级到最新稳定版本
llama-index-embeddings-openai>=0.2.0  # 升级到最新版本
llama-index-vector-stores-milvus>=0.2.0  # 升级到最新版本
llama-index-llms-openai>=0.2.0  # 升级到最新版本

# Vector Database (最新稳定版本)
pymilvus>=2.5.0  # 升级到最新稳定版本，性能改进

# Data Processing (最新稳定版本)
numpy>=1.26.0  # 最新稳定版本，性能优化
pandas>=2.2.0  # 最新稳定版本，新功能
scipy>=1.13.0  # 最新稳定版本

# Cache and Storage (最新稳定版本)
redis>=5.0.0  # 最新稳定版本
minio>=7.2.0  # 最新稳定版本

# Logging and Monitoring (最新稳定版本)
structlog>=24.1.0  # 最新稳定版本，性能改进

# Utility Libraries (最新稳定版本)
aiofiles>=24.1.0  # 最新稳定版本
httpx>=0.27.0  # 最新稳定版本，安全更新
deprecated>=1.2.14  # 保持当前版本

# High Performance Dependencies (Windows兼容)
# uvloop>=0.19.0  # 不支持Windows，保持注释
httptools>=0.6.0  # 保持当前版本

# Additional Vector Store Support (最新稳定版本)
faiss-cpu>=1.8.0  # 最新稳定版本，性能改进
