"""
CPU服务主程序 - 主业务服务

处理API密集嵌入调用和业务逻辑，通过HTTP调用GPU服务进行稀疏嵌入
"""

import os
import sys
import logging
from pathlib import Path
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 尝试导入共享模块，如果失败则使用本地配置
try:
    from shared.config.settings import get_settings
    from shared.utils.logging_config import setup_logging
except ImportError:
    # 如果共享模块不可用，使用简单配置
    import logging
    from pydantic_settings import BaseSettings

    class Settings(BaseSettings):
        CPU_SERVICE_HOST: str = "0.0.0.0"
        CPU_SERVICE_PORT: int = 8009
        CPU_SERVICE_WORKERS: int = 1
        GPU_SERVICE_URL: str = "http://localhost:8001"
        ENVIRONMENT: str = "development"
        CORS_ORIGINS: list = ["*"]
        CORS_METHODS: list = ["GET", "POST", "PUT", "DELETE"]
        CORS_HEADERS: list = ["*"]

    def get_settings():
        return Settings()

    def setup_logging(service_name="cpu-service"):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

try:
    from api.routes import api_router
    from api.health import health_router
    from api.system import system_router  # 新的系统管理路由
except ImportError:
    # 创建简单的路由器
    from fastapi import APIRouter

    health_router = APIRouter()
    api_router = APIRouter()
    system_router = APIRouter()  # 添加系统管理路由

    @health_router.get("/ready")
    async def readiness_check():
        return {"status": "ready", "service": "cpu-service", "version": "3.0.0"}

    @health_router.get("/live")
    async def liveness_check():
        return {"status": "alive", "service": "cpu-service", "version": "3.0.0"}

    @api_router.get("/")
    async def api_info():
        return {"api_version": "v2", "service": "cpu-service", "status": "running"}

# 设置日志
setup_logging(service_name="cpu-service")
logger = logging.getLogger(__name__)

# 获取配置
settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    try:
        logger.info("初始化CPU服务...")
        logger.info(f"环境: {settings.ENVIRONMENT}")
        logger.info(f"服务地址: http://{settings.CPU_SERVICE_HOST}:{settings.CPU_SERVICE_PORT}")
        logger.info(f"GPU服务地址: {settings.GPU_SERVICE_URL}")

        # 预热服务 - 在后台初始化关键组件
        logger.info("开始预热服务组件...")
        try:
            # 预热检索服务（触发Milvus客户端初始化）
            from cores.retrieval_service import get_retrieval_service
            retrieval_service = get_retrieval_service()

            # 触发Milvus客户端初始化（在后台进行）
            import threading

            def warmup_milvus():
                try:
                    logger.info("正在预热Milvus客户端...")
                    _ = retrieval_service.milvus_client  # 触发延迟初始化
                    logger.info("Milvus客户端预热完成")
                except Exception as e:
                    logger.warning(f"Milvus客户端预热失败: {str(e)}")

            # 在后台线程中预热，不阻塞启动
            warmup_thread = threading.Thread(target=warmup_milvus, daemon=True)
            warmup_thread.start()

            logger.info("服务预热已启动（后台进行）")

        except Exception as e:
            logger.warning(f"服务预热失败: {str(e)}")

        logger.info("CPU服务初始化完成")
        yield
    except Exception as e:
        logger.error(f"CPU服务初始化失败: {str(e)}")
        raise
    finally:
        # 清理资源
        logger.info("CPU服务关闭")


# 创建FastAPI应用
app = FastAPI(
    title="AI接诉即办助手 v3.0 - CPU服务",
    description="主业务服务，处理API密集嵌入调用和业务逻辑",
    version="3.0.0",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=settings.CORS_METHODS,
    allow_headers=settings.CORS_HEADERS,
)

# 注册路由
app.include_router(health_router, prefix="", tags=["健康检查"])
app.include_router(system_router, tags=["系统管理"])  # 新的系统管理路由
app.include_router(api_router, prefix="/v2", tags=["API v2"])


@app.get("/", summary="服务首页", description="AI接诉即办助手v3.0 CPU服务的欢迎页面")
async def root():
    """服务首页"""
    return {
        "欢迎": "AI接诉即办助手 v3.0 - CPU服务",
        "版本": "3.0.0",
        "状态": "正常运行",
        "架构": "GPU+CPU分离架构",
        "描述": "主业务服务，处理API密集嵌入调用和业务逻辑",
        "核心功能": [
            "智能检索：混合向量检索（密集+稀疏）",
            "智能索引：自动文档向量化和索引构建",
            "智能回退：多层容错和离线模式支持",
            "高性能：GPU+CPU分离架构，支持并发处理"
        ],
        "快速导航": {
            "健康检查": "/v2/system/health",
            "API服务": "/v2",
            "服务状态": "/v2/system/status",
            "API文档": "/docs",
            "ReDoc文档": "/redoc"
        },
        "技术栈": {
            "框架": "FastAPI",
            "向量数据库": "Milvus",
            "嵌入模型": "BGE-M3 (API + GPU)",
            "架构模式": "微服务 + GPU加速"
        }
    }


if __name__ == "__main__":
    # 从环境变量获取配置
    host = os.getenv("CPU_SERVICE_HOST", settings.CPU_SERVICE_HOST)
    port = int(os.getenv("CPU_SERVICE_PORT", settings.CPU_SERVICE_PORT))
    workers = int(os.getenv("CPU_SERVICE_WORKERS", settings.CPU_SERVICE_WORKERS))
    
    logger.info(f"启动CPU服务: {host}:{port}")
    
    # 统一启动配置 - 所有环境都禁用热重载以确保稳定性
    # 开发环境使用单worker，生产环境使用配置的worker数量
    actual_workers = 1 if settings.ENVIRONMENT == "development" else workers

    logger.info(f"启动模式: {settings.ENVIRONMENT}")
    logger.info(f"工作进程数: {actual_workers}")
    logger.info("热重载: 已禁用 (为确保服务稳定性)")

    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        workers=actual_workers,
        log_level="info",
        reload=False  # 所有环境都禁用热重载
    )
