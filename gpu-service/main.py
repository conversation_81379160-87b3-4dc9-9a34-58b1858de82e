"""
GPU服务主程序 - BGE-M3稀疏嵌入服务

专门运行本地BGE-M3模型，提供稀疏嵌入API服务
"""

import os
import sys
import logging
import time
from typing import List, Dict, Any
from contextlib import asynccontextmanager
from pathlib import Path

import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import torch

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 尝试导入共享模块，如果失败则使用本地配置
try:
    from shared.config.settings import get_settings
    from shared.utils.logging_config import setup_logging
except ImportError:
    # 如果共享模块不可用，使用简单配置
    import logging
    from pydantic_settings import BaseSettings

    class Settings(BaseSettings):
        BGE_M3_MODEL_PATH: str = "BAAI/bge-m3"
        GPU_SERVICE_HOST: str = "0.0.0.0"
        GPU_SERVICE_PORT: int = 8001

    def get_settings():
        return Settings()

    def setup_logging():
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)

# 获取配置
settings = get_settings()

# 导入简化GPU配置
try:
    from config.simple_gpu_config import get_simple_gpu_config, should_remove_batch_limit
    logger.info("简化GPU配置系统已加载")
except ImportError:
    logger.warning("GPU配置系统不可用，使用默认配置")

    def get_simple_gpu_config():
        return {"tier": "default", "max_batch_size": 48, "max_request_size": 1000, "worker_threads": 2}

    def should_remove_batch_limit(config):
        return False

# 全局模型实例
sparse_model = None


class SparseEmbeddingRequest(BaseModel):
    """稀疏嵌入请求模型"""
    texts: List[str]
    batch_size: int = 64  # 默认批处理大小，会被自适应配置覆盖


class SparseEmbeddingResponse(BaseModel):
    """稀疏嵌入响应模型"""
    embeddings: List[Dict[int, float]]
    processing_time: float
    model_info: str
    device: str


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str
    service: str
    device: str
    model_path: str
    gpu_info: Dict[str, Any] = None


class BGEMSparseEmbeddingService:
    """BGE-M3稀疏嵌入服务"""
    
    def __init__(self, model_path: str = None):
        """初始化BGE-M3稀疏嵌入服务"""
        self.model_path = model_path or settings.BGE_M3_MODEL_PATH
        self.model = None
        self.device = None
        self.gpu_config = None
        self._initialize_model()
    
    def _initialize_model(self):
        """初始化模型"""
        try:
            from FlagEmbedding import BGEM3FlagModel

            # 检测GPU
            if torch.cuda.is_available():
                self.device = "cuda"
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
                logger.info(f"使用GPU: {gpu_name}, 内存: {gpu_memory:.1f}GB")
            else:
                self.device = "cpu"
                logger.warning("GPU不可用，使用CPU")

            # 加载模型
            logger.info(f"加载BGE-M3模型: {self.model_path}")
            start_time = time.time()

            # 检查是否为本地路径
            import os
            if os.path.exists(self.model_path):
                logger.info(f"使用本地模型: {self.model_path}")
                model_path = self.model_path
            else:
                # 检查是否在离线模式
                if os.getenv("HF_HUB_OFFLINE") == "1":
                    logger.warning(f"离线模式下无法找到本地模型: {self.model_path}")
                    logger.info("尝试使用在线模型ID: BAAI/bge-m3")
                    model_path = "BAAI/bge-m3"
                else:
                    model_path = self.model_path

            # GPU优化设置
            if self.device == "cuda":
                torch.backends.cudnn.benchmark = True  # 启用cuDNN自动调优
                torch.backends.cudnn.deterministic = False  # 允许非确定性算法提升性能

                # 设置GPU内存优化
                torch.cuda.empty_cache()  # 清理缓存

                # 如果可用，启用TensorFloat-32 (TF32) 以提升性能
                if hasattr(torch.backends.cuda, 'matmul'):
                    torch.backends.cuda.matmul.allow_tf32 = True
                if hasattr(torch.backends.cudnn, 'allow_tf32'):
                    torch.backends.cudnn.allow_tf32 = True

            self.model = BGEM3FlagModel(
                model_path,
                use_fp16=True if self.device == "cuda" else False,
                device=self.device
            )
            load_time = time.time() - start_time
            logger.info(f"BGE-M3模型加载完成，耗时: {load_time:.3f}秒")

            # 初始化GPU配置
            self._initialize_gpu_config()

            # 模型预热 - 执行一次虚拟推理以优化性能
            self._warmup_model()

        except Exception as e:
            logger.error(f"模型初始化失败: {str(e)}")
            raise

    def _initialize_gpu_config(self):
        """初始化GPU配置"""
        try:
            # 获取简化GPU配置
            self.gpu_config = get_simple_gpu_config()

            # 应用环境变量覆盖
            self.gpu_config = self._apply_env_overrides(self.gpu_config)

            logger.info(f"GPU配置已初始化:")
            logger.info(f"  GPU等级: {self.gpu_config.get('tier', 'unknown')}")
            logger.info(f"  最大批处理大小: {self.gpu_config.get('max_batch_size', 48)}")
            logger.info(f"  最大请求大小: {self.gpu_config.get('max_request_size', 1000)}")
            logger.info(f"  工作线程数: {self.gpu_config.get('worker_threads', 2)}")

            # 检查是否移除批处理限制
            self.unlimited_mode = should_remove_batch_limit(self.gpu_config)
            if self.unlimited_mode:
                logger.info("🚀 企业级GPU检测到，启用无限制模式")

        except Exception as e:
            logger.warning(f"GPU配置初始化失败，使用默认配置: {str(e)}")
            self.gpu_config = {"tier": "fallback", "max_batch_size": 48, "max_request_size": 1000, "worker_threads": 2}
            self.unlimited_mode = False

    def _apply_env_overrides(self, config):
        """应用环境变量覆盖配置"""
        # 从环境变量读取配置覆盖
        env_overrides = {
            'max_batch_size': os.getenv('GPU_BATCH_SIZE'),
            'max_request_size': os.getenv('GPU_MAX_REQUEST_SIZE'),
            'worker_threads': os.getenv('GPU_WORKER_THREADS'),
            'memory_reserve': os.getenv('GPU_MEMORY_RESERVE'),
        }

        # 应用有效的环境变量
        for key, value in env_overrides.items():
            if value is not None:
                try:
                    config[key] = int(value)
                    logger.info(f"环境变量覆盖: {key} = {value}")
                except ValueError:
                    logger.warning(f"无效的环境变量值: {key} = {value}")

        # 检查无限制模式环境变量
        unlimited_env = os.getenv('GPU_UNLIMITED_MODE', '').lower()
        if unlimited_env in ['true', '1', 'yes']:
            config['unlimited_mode'] = True
            logger.info("环境变量启用无限制模式")

        return config

    def _warmup_model(self):
        """模型预热 - 执行虚拟推理以优化首次使用性能"""
        try:
            logger.info("开始模型预热...")
            warmup_start = time.time()

            # 使用简短的测试文本进行预热
            warmup_texts = [
                "模型预热测试文本",
                "Model warmup test text",
                "测试"
            ]

            # 执行预热推理
            _ = self.model.encode(
                warmup_texts,
                return_dense=False,
                return_sparse=True,
                return_colbert_vecs=False
            )

            # 如果是GPU，确保所有操作完成
            if self.device == "cuda":
                torch.cuda.synchronize()
                # 清理预热产生的缓存
                torch.cuda.empty_cache()

            warmup_time = time.time() - warmup_start
            logger.info(f"模型预热完成，耗时: {warmup_time:.3f}秒")
            logger.info("🚀 BGE-M3模型已就绪，首次推理性能已优化")

        except Exception as e:
            logger.warning(f"模型预热失败，但不影响正常使用: {str(e)}")

    def encode_sparse(self, texts: List[str]) -> List[Dict[int, float]]:
        """编码稀疏嵌入"""
        if not self.model:
            raise RuntimeError("模型未初始化")

        try:
            start_time = time.time()

            # 使用BGE-M3模型进行稀疏编码
            embeddings = self.model.encode(
                texts,
                return_dense=False,
                return_sparse=True,
                return_colbert_vecs=False
            )

            # 转换为标准格式
            sparse_embeddings = []
            for embedding in embeddings['lexical_weights']:
                sparse_dict = {}
                # 检查嵌入格式并适配
                if hasattr(embedding, 'indices') and hasattr(embedding, 'values'):
                    # 稀疏张量格式
                    for idx, weight in zip(embedding.indices, embedding.values):
                        sparse_dict[int(idx)] = float(weight)
                elif isinstance(embedding, dict):
                    # 字典格式
                    sparse_dict = {int(k): float(v) for k, v in embedding.items()}
                else:
                    # 其他格式，尝试直接转换
                    try:
                        sparse_dict = dict(embedding)
                    except:
                        logger.warning(f"无法解析稀疏嵌入格式: {type(embedding)}")
                        sparse_dict = {}
                sparse_embeddings.append(sparse_dict)

            processing_time = time.time() - start_time
            logger.info(f"稀疏嵌入编码完成: {len(texts)}个文本, 耗时: {processing_time:.3f}秒")

            return sparse_embeddings

        except Exception as e:
            logger.error(f"稀疏嵌入编码失败: {str(e)}")
            raise
    
    def get_gpu_info(self) -> Dict[str, Any]:
        """获取GPU信息"""
        if not torch.cuda.is_available() or self.device != "cuda":
            return {}

        try:
            gpu_info = {
                "gpu_name": torch.cuda.get_device_name(0),
                "gpu_memory_total": f"{torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB",
                "gpu_memory_allocated": f"{torch.cuda.memory_allocated(0) / 1024**3:.1f}GB",
                "gpu_memory_cached": f"{torch.cuda.memory_reserved(0) / 1024**3:.1f}GB"
            }

            # 尝试使用nvidia-ml-py获取更详细的GPU信息
            try:
                import pynvml
                pynvml.nvmlInit()
                handle = pynvml.nvmlDeviceGetHandleByIndex(0)

                # 获取GPU利用率
                utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
                gpu_info["gpu_utilization"] = f"{utilization.gpu}%"
                gpu_info["memory_utilization"] = f"{utilization.memory}%"

                # 获取温度
                try:
                    temp = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
                    gpu_info["temperature"] = f"{temp}°C"
                except:
                    pass

                # 获取功耗
                try:
                    power = pynvml.nvmlDeviceGetPowerUsage(handle) / 1000.0  # 转换为瓦特
                    gpu_info["power_usage"] = f"{power:.1f}W"
                except:
                    pass

            except ImportError:
                logger.warning("nvidia-ml-py未安装，无法获取详细GPU信息")
            except Exception as e:
                logger.warning(f"获取详细GPU信息失败: {str(e)}")

            return gpu_info

        except Exception as e:
            logger.error(f"获取GPU信息失败: {str(e)}")
            return {"error": str(e)}


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化模型
    global sparse_model
    try:
        logger.info("初始化BGE-M3稀疏嵌入服务...")
        sparse_model = BGEMSparseEmbeddingService()
        logger.info("BGE-M3稀疏嵌入服务初始化完成")
        yield
    except Exception as e:
        logger.error(f"服务初始化失败: {str(e)}")
        raise
    finally:
        # 清理资源
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        logger.info("BGE-M3稀疏嵌入服务关闭")


# 创建FastAPI应用
app = FastAPI(
    title="BGE-M3稀疏嵌入服务",
    description="专门提供BGE-M3模型的稀疏嵌入服务",
    version="3.0.0",
    lifespan=lifespan
)

# 添加CORS中间件（兼容性修复版本）
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"]
)


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查"""
    gpu_info = None
    if sparse_model:
        gpu_info = sparse_model.get_gpu_info()
    
    return HealthResponse(
        status="healthy",
        service="bge-m3-sparse-embedding",
        device=sparse_model.device if sparse_model else "unknown",
        model_path=sparse_model.model_path if sparse_model else "unknown",
        gpu_info=gpu_info
    )


@app.post("/embeddings/sparse", response_model=SparseEmbeddingResponse)
async def create_sparse_embeddings(request: SparseEmbeddingRequest):
    """创建稀疏嵌入"""
    if not sparse_model:
        raise HTTPException(status_code=503, detail="模型服务不可用")
    
    if not request.texts:
        raise HTTPException(status_code=400, detail="文本列表不能为空")
    
    # 使用GPU配置确定请求限制
    max_request_size = 1000  # 默认值
    if sparse_model.gpu_config:
        max_request_size = sparse_model.gpu_config.get('max_request_size', 1000)
        if sparse_model.gpu_config.get('unlimited_mode') or sparse_model.unlimited_mode:
            max_request_size = 50000  # 无限制模式

    if len(request.texts) > max_request_size:
        gpu_tier = sparse_model.gpu_config.get('tier', 'unknown') if sparse_model.gpu_config else 'unknown'
        raise HTTPException(
            status_code=400,
            detail=f"单次请求文本数量不能超过{max_request_size}（{gpu_tier}级GPU限制）"
        )
    
    try:
        start_time = time.time()
        
        # 优化：直接处理所有文本，让BGE-M3内部处理批次
        # 这样可以更好地利用GPU并行能力
        all_embeddings = sparse_model.encode_sparse(request.texts)
        
        processing_time = time.time() - start_time
        
        return SparseEmbeddingResponse(
            embeddings=all_embeddings,
            processing_time=processing_time,
            model_info=f"BGE-M3 on {sparse_model.device}",
            device=sparse_model.device
        )
        
    except Exception as e:
        logger.error(f"稀疏嵌入请求处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")


@app.get("/model/info")
async def get_model_info():
    """获取模型信息"""
    if not sparse_model:
        raise HTTPException(status_code=503, detail="模型服务不可用")
    
    info = {
        "model_path": sparse_model.model_path,
        "device": sparse_model.device,
        "service_type": "sparse_embedding",
        "version": "3.0.0"
    }
    
    # 添加GPU信息
    gpu_info = sparse_model.get_gpu_info()
    if gpu_info:
        info.update(gpu_info)
    
    return info


@app.get("/performance/benchmark")
async def performance_benchmark():
    """性能基准测试"""
    if not sparse_model:
        raise HTTPException(status_code=503, detail="模型服务不可用")

    # 生成测试数据
    test_texts = [
        f"这是测试文本 {i}，用于评估GPU稀疏嵌入的性能表现。"
        for i in range(50)
    ]

    # 测试不同批处理大小
    batch_sizes = [8, 16, 32, 64]
    results = {}

    for batch_size in batch_sizes:
        try:
            start_time = time.time()
            # 基准测试：直接使用固定的测试文本
            embeddings = sparse_model.encode_sparse(test_texts)
            end_time = time.time()

            processing_time = end_time - start_time
            docs_per_second = len(test_texts) / processing_time

            results[f"batch_{batch_size}"] = {
                "processing_time": round(processing_time, 3),
                "docs_per_second": round(docs_per_second, 1),
                "embeddings_count": len(embeddings)
            }
        except Exception as e:
            results[f"batch_{batch_size}"] = {"error": str(e)}

    # 获取GPU信息
    gpu_info = sparse_model.get_gpu_info()

    return {
        "service": "BGE-M3稀疏嵌入性能测试",
        "test_data_size": len(test_texts),
        "device": sparse_model.device,
        "gpu_info": gpu_info,
        "batch_performance": results,
        "timestamp": time.time()
    }


@app.get("/config/gpu")
async def get_gpu_config():
    """获取GPU配置信息"""
    if not sparse_model:
        raise HTTPException(status_code=503, detail="模型服务不可用")

    config_info = {
        "config_type": "simplified",
        "unlimited_mode": getattr(sparse_model, 'unlimited_mode', False)
    }

    if sparse_model.gpu_config:
        config_info.update({
            "gpu_tier": sparse_model.gpu_config.get('tier', 'unknown'),
            "gpu_name": sparse_model.gpu_config.get('gpu_name', 'unknown'),
            "gpu_memory_gb": sparse_model.gpu_config.get('gpu_memory_gb', 0),
            "max_batch_size": sparse_model.gpu_config.get('max_batch_size', 48),
            "max_request_size": sparse_model.gpu_config.get('max_request_size', 1000),
            "worker_threads": sparse_model.gpu_config.get('worker_threads', 2),
            "optimized_for_rtx4060": sparse_model.gpu_config.get('optimized_for_rtx4060', False)
        })

    # 添加环境变量信息
    env_vars = {
        "GPU_BATCH_SIZE": os.getenv('GPU_BATCH_SIZE'),
        "GPU_MAX_REQUEST_SIZE": os.getenv('GPU_MAX_REQUEST_SIZE'),
        "GPU_WORKER_THREADS": os.getenv('GPU_WORKER_THREADS'),
        "GPU_UNLIMITED_MODE": os.getenv('GPU_UNLIMITED_MODE')
    }
    config_info["environment_overrides"] = {k: v for k, v in env_vars.items() if v is not None}

    return config_info


@app.get("/")
async def root():
    """根路径"""
    return {
        "service": "BGE-M3稀疏嵌入服务",
        "version": "3.0.0",
        "status": "running",
        "gpu_config": "simplified",
        "endpoints": {
            "health": "/health",
            "sparse_embeddings": "/embeddings/sparse",
            "model_info": "/model/info",
            "performance_benchmark": "/performance/benchmark",
            "gpu_config": "/config/gpu"
        }
    }


if __name__ == "__main__":
    # 从环境变量获取配置
    host = os.getenv("GPU_SERVICE_HOST", "0.0.0.0")
    port = int(os.getenv("GPU_SERVICE_PORT", "8001"))
    workers = int(os.getenv("GPU_SERVICE_WORKERS", "2"))  # 增加默认工作线程数
    
    logger.info(f"启动BGE-M3稀疏嵌入服务: {host}:{port}")
    
    # 修复uvloop问题：在多进程模式下使用asyncio事件循环
    if workers > 1:
        uvicorn.run(
            "main:app",
            host=host,
            port=port,
            workers=workers,
            log_level="info",
            reload=False,
            loop="asyncio"  # 强制使用asyncio而不是uvloop
        )
    else:
        # 单进程模式，尝试使用uvloop，失败则回退到asyncio
        try:
            uvicorn.run(
                "main:app",
                host=host,
                port=port,
                workers=workers,
                log_level="info",
                reload=False
            )
        except ImportError:
            logger.warning("uvloop不可用，使用asyncio事件循环")
            uvicorn.run(
                "main:app",
                host=host,
                port=port,
                workers=workers,
                log_level="info",
                reload=False,
                loop="asyncio"
            )
