#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的GPU配置系统
基于GPU内存大小提供简单的配置建议
"""

import torch
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


def get_simple_gpu_config() -> Dict[str, Any]:
    """
    获取简化的GPU配置
    基于GPU内存大小提供合理的默认配置
    """
    try:
        if not torch.cuda.is_available():
            return {
                "device": "cpu",
                "max_batch_size": 16,
                "max_request_size": 100,
                "worker_threads": 1
            }
        
        # 获取GPU内存
        gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        gpu_name = torch.cuda.get_device_name(0)
        
        # 基于内存大小的简单映射
        if gpu_memory_gb >= 32:
            # 企业级GPU (32GB+)
            config = {
                "tier": "enterprise",
                "max_batch_size": 128,
                "max_request_size": 5000,
                "worker_threads": 4
            }
        elif gpu_memory_gb >= 16:
            # 高端GPU (16-32GB)
            config = {
                "tier": "high",
                "max_batch_size": 96,
                "max_request_size": 2000,
                "worker_threads": 3
            }
        elif gpu_memory_gb >= 10:
            # 中端GPU (10-16GB)
            config = {
                "tier": "mid",
                "max_batch_size": 64,
                "max_request_size": 1000,
                "worker_threads": 2
            }
        else:
            # 入门级GPU (<10GB)
            config = {
                "tier": "entry",
                "max_batch_size": 48,  # 基于RTX 4060测试结果
                "max_request_size": 500,
                "worker_threads": 2
            }
        
        # 添加基础信息
        config.update({
            "device": "cuda",
            "gpu_name": gpu_name,
            "gpu_memory_gb": round(gpu_memory_gb, 1),
            "optimized_for_rtx4060": gpu_memory_gb <= 8.5  # RTX 4060特殊优化
        })
        
        logger.info(f"GPU配置: {config['tier']}级 ({gpu_name}, {gpu_memory_gb:.1f}GB)")
        logger.info(f"推荐配置: 批处理{config['max_batch_size']}, 最大请求{config['max_request_size']}")
        
        return config
        
    except Exception as e:
        logger.error(f"GPU配置检测失败: {str(e)}")
        # 返回安全的默认配置
        return {
            "device": "cpu",
            "tier": "fallback",
            "max_batch_size": 16,
            "max_request_size": 100,
            "worker_threads": 1
        }


def should_remove_batch_limit(gpu_config: Dict[str, Any]) -> bool:
    """
    判断是否应该移除批处理限制
    只有企业级GPU才建议移除限制
    """
    return (
        gpu_config.get("tier") == "enterprise" and
        gpu_config.get("gpu_memory_gb", 0) >= 32
    )


if __name__ == "__main__":
    # 测试简化配置
    logging.basicConfig(level=logging.INFO)
    
    config = get_simple_gpu_config()
    print(f"GPU配置: {config}")
    
    remove_limit = should_remove_batch_limit(config)
    print(f"移除批处理限制: {remove_limit}")
