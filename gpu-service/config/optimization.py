#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU服务优化配置
包含所有性能优化相关的配置参数
"""

import os
from typing import Dict, Any


class GPUOptimizationConfig:
    """GPU服务优化配置类"""
    
    # 模型加载优化
    MODEL_OPTIMIZATION = {
        "use_fp16": True,  # 使用半精度浮点数
        "normalize_embeddings": True,  # 标准化嵌入
        "query_instruction_for_retrieval": None,  # 禁用查询指令
        "pooling_method": "cls",  # 使用CLS池化
        "max_length": 512,  # 最大序列长度
    }
    
    # CUDA优化设置
    CUDA_OPTIMIZATION = {
        "benchmark": True,  # 启用cuDNN自动调优
        "deterministic": False,  # 允许非确定性算法
        "empty_cache_frequency": 1,  # 每批次后清理缓存
        "synchronize_after_batch": True,  # 批次后同步
    }
    
    # 批处理优化
    BATCH_OPTIMIZATION = {
        "default_batch_size": 32,  # 默认批处理大小
        "max_batch_size": 64,  # 最大批处理大小
        "min_batch_size": 8,   # 最小批处理大小
        "auto_adjust": True,   # 自动调整批处理大小
    }
    
    # 内存管理
    MEMORY_OPTIMIZATION = {
        "prealloc_embeddings": True,  # 预分配嵌入列表
        "clear_cache_on_error": True,  # 错误时清理缓存
        "gc_frequency": 10,  # 垃圾回收频率（批次）
    }
    
    # 性能监控
    PERFORMANCE_MONITORING = {
        "log_processing_time": True,  # 记录处理时间
        "log_docs_per_second": True,  # 记录处理速度
        "log_gpu_utilization": True,  # 记录GPU利用率
        "benchmark_on_startup": False,  # 启动时运行基准测试
    }
    
    # API优化
    API_OPTIMIZATION = {
        "max_request_size": 100,  # 单次请求最大文本数
        "timeout_seconds": 300,   # 请求超时时间
        "enable_async": True,     # 启用异步处理
        "max_concurrent_requests": 4,  # 最大并发请求数
    }
    
    @classmethod
    def get_model_kwargs(cls) -> Dict[str, Any]:
        """获取模型初始化参数"""
        return cls.MODEL_OPTIMIZATION.copy()
    
    @classmethod
    def get_cuda_settings(cls) -> Dict[str, Any]:
        """获取CUDA优化设置"""
        return cls.CUDA_OPTIMIZATION.copy()
    
    @classmethod
    def get_batch_config(cls) -> Dict[str, Any]:
        """获取批处理配置"""
        return cls.BATCH_OPTIMIZATION.copy()
    
    @classmethod
    def get_optimal_batch_size(cls, text_count: int) -> int:
        """根据文本数量获取最优批处理大小"""
        config = cls.get_batch_config()
        
        if text_count <= config["min_batch_size"]:
            return text_count
        elif text_count <= config["default_batch_size"]:
            return config["default_batch_size"]
        else:
            return min(config["max_batch_size"], text_count)
    
    @classmethod
    def should_clear_cache(cls, batch_index: int) -> bool:
        """判断是否应该清理缓存"""
        return batch_index % cls.CUDA_OPTIMIZATION["empty_cache_frequency"] == 0
    
    @classmethod
    def should_run_gc(cls, batch_index: int) -> bool:
        """判断是否应该运行垃圾回收"""
        return batch_index % cls.MEMORY_OPTIMIZATION["gc_frequency"] == 0


class EnvironmentOptimization:
    """环境变量优化设置"""
    
    @staticmethod
    def apply_torch_optimizations():
        """应用PyTorch环境优化"""
        # 设置PyTorch优化环境变量
        os.environ.setdefault("TORCH_CUDNN_V8_API_ENABLED", "1")
        os.environ.setdefault("CUDA_LAUNCH_BLOCKING", "0")
        os.environ.setdefault("TORCH_USE_CUDA_DSA", "1")
        
        # 内存优化
        os.environ.setdefault("PYTORCH_CUDA_ALLOC_CONF", "max_split_size_mb:128")
        
        # 多线程优化
        os.environ.setdefault("OMP_NUM_THREADS", "4")
        os.environ.setdefault("MKL_NUM_THREADS", "4")
    
    @staticmethod
    def apply_huggingface_optimizations():
        """应用HuggingFace优化"""
        # 禁用不必要的警告
        os.environ.setdefault("TOKENIZERS_PARALLELISM", "false")
        os.environ.setdefault("TRANSFORMERS_VERBOSITY", "error")
        
        # 缓存优化
        os.environ.setdefault("HF_HUB_CACHE", "/app/models/cache")
        os.environ.setdefault("TRANSFORMERS_CACHE", "/app/models/cache")


class ProductionOptimization:
    """生产环境优化配置"""
    
    # 生产环境推荐配置
    PRODUCTION_CONFIG = {
        "model": {
            "use_fp16": True,
            "batch_size": 64,
            "max_length": 512,
            "normalize_embeddings": True,
        },
        "cuda": {
            "benchmark": True,
            "deterministic": False,
            "memory_fraction": 0.9,  # 使用90%GPU内存
        },
        "api": {
            "max_request_size": 100,
            "timeout_seconds": 300,
            "max_concurrent_requests": 4,
            "enable_compression": True,
        },
        "monitoring": {
            "log_level": "INFO",
            "performance_logging": True,
            "gpu_monitoring": True,
            "health_check_interval": 30,
        }
    }
    
    @classmethod
    def get_production_config(cls) -> Dict[str, Any]:
        """获取生产环境配置"""
        return cls.PRODUCTION_CONFIG.copy()
    
    @classmethod
    def apply_production_optimizations(cls):
        """应用生产环境优化"""
        # 应用环境优化
        EnvironmentOptimization.apply_torch_optimizations()
        EnvironmentOptimization.apply_huggingface_optimizations()
        
        # 设置生产环境变量
        os.environ.setdefault("ENVIRONMENT", "production")
        os.environ.setdefault("LOG_LEVEL", "INFO")
        
        # GPU优化
        os.environ.setdefault("CUDA_VISIBLE_DEVICES", "0")
        os.environ.setdefault("NVIDIA_VISIBLE_DEVICES", "0")


def get_optimization_summary() -> Dict[str, Any]:
    """获取优化配置摘要"""
    return {
        "model_optimization": GPUOptimizationConfig.MODEL_OPTIMIZATION,
        "cuda_optimization": GPUOptimizationConfig.CUDA_OPTIMIZATION,
        "batch_optimization": GPUOptimizationConfig.BATCH_OPTIMIZATION,
        "memory_optimization": GPUOptimizationConfig.MEMORY_OPTIMIZATION,
        "api_optimization": GPUOptimizationConfig.API_OPTIMIZATION,
        "production_config": ProductionOptimization.PRODUCTION_CONFIG
    }


def apply_all_optimizations():
    """应用所有优化设置"""
    # 应用环境优化
    EnvironmentOptimization.apply_torch_optimizations()
    EnvironmentOptimization.apply_huggingface_optimizations()
    
    # 如果是生产环境，应用生产优化
    if os.getenv("ENVIRONMENT") == "production":
        ProductionOptimization.apply_production_optimizations()
    
    print("✅ GPU服务优化配置已应用")
    print(f"📊 优化摘要: {len(get_optimization_summary())} 个优化模块已启用")


if __name__ == "__main__":
    # 测试优化配置
    apply_all_optimizations()
    
    # 打印配置摘要
    import json
    summary = get_optimization_summary()
    print("\n🔧 GPU服务优化配置摘要:")
    print(json.dumps(summary, indent=2, ensure_ascii=False))
