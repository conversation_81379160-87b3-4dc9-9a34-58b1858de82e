2025-06-01 05:31:20 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 05:31:20 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\ai-service-2025-06-01.log
2025-06-01 05:31:20 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\ai-service-error-2025-06-01.log
2025-06-01 05:38:44 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 05:38:44 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\ai-service-2025-06-01.log
2025-06-01 05:38:44 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\ai-service-error-2025-06-01.log
2025-06-01 05:39:06 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 05:39:06 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\ai-service-2025-06-01.log
2025-06-01 05:39:06 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\ai-service-error-2025-06-01.log
2025-06-01 05:40:11 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 05:40:11 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\ai-service-2025-06-01.log
2025-06-01 05:40:11 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\ai-service-error-2025-06-01.log
2025-06-01 16:26:22 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 16:26:22 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\ai-service-2025-06-01.log
2025-06-01 16:26:22 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\ai-service-error-2025-06-01.log
2025-06-01 16:26:22 [INFO] __main__ [main.py:318] - 启动BGE-M3稀疏嵌入服务: 0.0.0.0:8001
2025-06-01 16:26:22 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 16:26:22 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\ai-service-2025-06-01.log
2025-06-01 16:26:22 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\ai-service-error-2025-06-01.log
2025-06-01 16:26:22 [INFO] uvicorn.error [server.py:83] - Started server process [46860]
2025-06-01 16:26:22 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-01 16:26:22 [INFO] main [main.py:190] - 初始化BGE-M3稀疏嵌入服务...
2025-06-01 16:26:26 [INFO] datasets [config.py:54] - PyTorch version 2.7.0+cu126 available.
2025-06-01 16:26:28 [INFO] main [main.py:102] - 使用GPU: NVIDIA GeForce RTX 4060 Laptop GPU, 内存: 8.0GB
2025-06-01 16:26:28 [INFO] main [main.py:108] - 加载BGE-M3模型: BAAI/bge-m3
2025-06-01 16:26:32 [INFO] FlagEmbedding.finetune.embedder.encoder_only.m3.runner [runner.py:86] - loading existing colbert_linear and sparse_linear---------
2025-06-01 16:26:32 [INFO] main [main.py:114] - BGE-M3模型加载成功
2025-06-01 16:26:32 [INFO] main [main.py:192] - BGE-M3稀疏嵌入服务初始化完成
2025-06-01 16:26:32 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-01 16:26:32 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8001 (Press CTRL+C to quit)
2025-06-01 16:42:50 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 16:42:50 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\ai-service-2025-06-01.log
2025-06-01 16:42:50 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\ai-service-error-2025-06-01.log
2025-06-01 16:42:50 [INFO] __main__ [main.py:318] - 启动BGE-M3稀疏嵌入服务: 0.0.0.0:8001
2025-06-01 16:42:50 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-01 16:42:50 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\ai-service-2025-06-01.log
2025-06-01 16:42:50 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\ai-service-error-2025-06-01.log
2025-06-01 16:42:50 [INFO] uvicorn.error [server.py:83] - Started server process [42704]
2025-06-01 16:42:50 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-01 16:42:50 [INFO] main [main.py:190] - 初始化BGE-M3稀疏嵌入服务...
2025-06-01 16:42:52 [INFO] datasets [config.py:54] - PyTorch version 2.7.0+cu126 available.
2025-06-01 16:42:55 [INFO] main [main.py:102] - 使用GPU: NVIDIA GeForce RTX 4060 Laptop GPU, 内存: 8.0GB
2025-06-01 16:42:55 [INFO] main [main.py:108] - 加载BGE-M3模型: D:\huggingface_hub\hub\bge-m3
2025-06-01 16:42:58 [INFO] FlagEmbedding.finetune.embedder.encoder_only.m3.runner [runner.py:86] - loading existing colbert_linear and sparse_linear---------
2025-06-01 16:42:58 [INFO] main [main.py:114] - BGE-M3模型加载成功
2025-06-01 16:42:58 [INFO] main [main.py:192] - BGE-M3稀疏嵌入服务初始化完成
2025-06-01 16:42:58 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-01 16:42:58 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8001 (Press CTRL+C to quit)
