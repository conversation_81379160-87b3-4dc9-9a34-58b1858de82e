2025-06-03 09:47:46 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-03 09:47:46 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\ai-service-2025-06-03.log
2025-06-03 09:47:46 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\ai-service-error-2025-06-03.log
2025-06-03 09:47:46 [INFO] __main__ [main.py:389] - 启动BGE-M3稀疏嵌入服务: 0.0.0.0:8001
2025-06-03 09:47:46 [INFO] shared.utils.logging_config [logging_config.py:118] - 日志系统初始化完成 - 服务: ai-service, 级别: INFO
2025-06-03 09:47:46 [INFO] shared.utils.logging_config [logging_config.py:119] - 日志文件: logs\ai-service-2025-06-03.log
2025-06-03 09:47:46 [INFO] shared.utils.logging_config [logging_config.py:120] - 错误日志文件: logs\ai-service-error-2025-06-03.log
2025-06-03 09:47:46 [INFO] uvicorn.error [server.py:83] - Started server process [35752]
2025-06-03 09:47:46 [INFO] uvicorn.error [on.py:48] - Waiting for application startup.
2025-06-03 09:47:46 [INFO] main [main.py:261] - 初始化BGE-M3稀疏嵌入服务...
2025-06-03 09:47:49 [INFO] datasets [config.py:54] - PyTorch version 2.7.0+cu126 available.
2025-06-03 09:47:52 [INFO] main [main.py:102] - 使用GPU: NVIDIA GeForce RTX 4060 Laptop GPU, 内存: 8.0GB
2025-06-03 09:47:52 [INFO] main [main.py:108] - 加载BGE-M3模型: BAAI/bge-m3
2025-06-03 09:47:57 [INFO] FlagEmbedding.finetune.embedder.encoder_only.m3.runner [runner.py:86] - loading existing colbert_linear and sparse_linear---------
2025-06-03 09:47:57 [INFO] main [main.py:116] - BGE-M3模型加载完成，耗时: 4.434秒
2025-06-03 09:47:57 [INFO] main [main.py:128] - 开始模型预热...
2025-06-03 09:47:59 [INFO] main [main.py:153] - 模型预热完成，耗时: 1.914秒
2025-06-03 09:47:59 [INFO] main [main.py:154] - 🚀 BGE-M3模型已就绪，首次推理性能已优化
2025-06-03 09:47:59 [INFO] main [main.py:263] - BGE-M3稀疏嵌入服务初始化完成
2025-06-03 09:47:59 [INFO] uvicorn.error [on.py:62] - Application startup complete.
2025-06-03 09:47:59 [INFO] uvicorn.error [server.py:215] - Uvicorn running on http://0.0.0.0:8001 (Press CTRL+C to quit)
