# AI接诉即办助手 v3.0 - GPU服务生产版
# 充分利用PyTorch基础镜像，最小化额外依赖，优化首次加载性能

FROM pytorch/pytorch:2.7.0-cuda12.8-cudnn9-runtime

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    CUDA_VISIBLE_DEVICES=0 \
    GPU_SERVICE_HOST=0.0.0.0 \
    GPU_SERVICE_PORT=8001 \
    HF_HOME=/app/models \
    TRANSFORMERS_CACHE=/app/models \
    TORCH_HOME=/app/models \
    PIP_INDEX_URL=https://mirrors.cloud.tencent.com/pypi/simple/ \
    PIP_TRUSTED_HOST="mirrors.cloud.tencent.com files.pythonhosted.org pypi.org" \
    PIP_TIMEOUT=300 \
    PIP_RETRIES=3 \
    # PyTorch性能优化
    TORCH_CUDNN_V8_API_ENABLED=1 \
    CUDA_LAUNCH_BLOCKING=0 \
    # Transformers优化
    TOKENIZERS_PARALLELISM=true \
    HF_DATASETS_OFFLINE=1 \
    TRANSFORMERS_OFFLINE=1

# 更换为腾讯云APT镜像源
RUN rm -f /etc/apt/sources.list /etc/apt/sources.list.d/* && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ jammy main restricted universe multiverse" > /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ jammy-updates main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ jammy-security main restricted universe multiverse" >> /etc/apt/sources.list && \
    rm -rf /var/lib/apt/lists/*

# 安装系统依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# 升级pip
RUN pip install --upgrade pip

# 验证基础镜像已有的包
RUN python -c "import torch; print(f'PyTorch: {torch.__version__}'); import numpy; print(f'NumPy: {numpy.__version__}')"

# 安装轻量级Web框架依赖（兼容版本）
RUN pip install --no-deps \
    fastapi==0.110.0 \
    uvicorn==0.27.0 \
    pydantic==2.6.0 \
    starlette \
    anyio \
    sniffio \
    idna \
    h11 \
    click \
    typing-extensions

# 安装pydantic-settings（有少量依赖）
RUN pip install pydantic-settings

# 安装基础工具包
RUN pip install --no-deps \
    requests \
    urllib3 \
    certifi \
    charset-normalizer \
    python-multipart \
    python-dotenv \
    pyyaml

# 安装transformers（跳过PyTorch相关依赖）
RUN pip install --no-deps \
    transformers \
    tokenizers \
    safetensors \
    huggingface-hub \
    filelock \
    packaging \
    regex \
    tqdm

# 安装sentence-transformers的轻量级依赖
RUN pip install \
    scikit-learn \
    joblib \
    threadpoolctl \
    nltk \
    pillow

# 安装sentence-transformers主包（跳过所有依赖）
RUN pip install --no-deps sentence-transformers

# 安装FlagEmbedding的必要依赖
RUN pip install datasets peft

# 安装FlagEmbedding（跳过所有依赖）
RUN pip install --no-deps FlagEmbedding

# 安装GPU监控依赖
RUN pip install --no-deps \
    nvidia-ml-py \
    psutil

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 设置工作目录
WORKDIR /app

# 复制共享代码
COPY --chown=appuser:appuser shared /app/shared

# 复制应用代码
COPY --chown=appuser:appuser gpu-service /app/

# 创建必要目录并设置权限
RUN mkdir -p /app/models /app/logs && \
    chown -R appuser:appuser /app && \
    chmod +x /app/*.py

# 验证关键包是否可用（简化验证，避免复杂导入）
RUN python -c "import torch; print(f'✅ PyTorch: {torch.__version__}'); print(f'CUDA可用: {torch.cuda.is_available()}')"
RUN python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
RUN python -c "import sentence_transformers; print('✅ Sentence-Transformers导入成功')"
# 注意：FlagEmbedding验证在运行时进行，避免构建时的复杂依赖问题

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# 启动命令（使用主启动脚本）
CMD ["python", "main.py"]
