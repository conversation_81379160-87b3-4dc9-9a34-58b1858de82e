# GPU服务依赖 - 专门用于BGE-M3稀疏嵌入（现代化升级 2025-06-12）
# 与CPU服务保持版本一致性，使用最新稳定版本

# Core Web Framework (与CPU服务版本一致)
fastapi>=0.115.0  # 与CPU服务保持一致，最新稳定版本
uvicorn[standard]>=0.30.6  # 与CPU服务保持一致，最新稳定版本
uvloop>=0.19.0  # Linux容器环境支持，最新稳定版本
pydantic>=2.11.0  # 与CPU服务保持一致，最新稳定版本
pydantic-settings>=2.6.0  # 与CPU服务保持一致

# AI/ML Core Dependencies (升级到最新稳定版本)
# torch>=2.0.0  # 使用基础镜像自带的PyTorch，避免版本冲突
transformers>=4.45.0  # 升级到最新稳定版本，支持最新模型
FlagEmbedding>=1.3.0  # 升级到最新稳定版本，保持BGE-M3兼容性

# Data Processing (与CPU服务版本一致)
# numpy>=1.26.0  # 使用PyTorch基础镜像自带的NumPy，避免冲突
scipy>=1.13.0  # 与CPU服务保持一致

# Utility Libraries (与CPU服务版本一致)
requests>=2.32.0  # 与CPU服务保持一致
python-multipart>=0.0.12  # 与CPU服务保持一致
python-dotenv>=1.0.1  # 与CPU服务保持一致
pyyaml>=6.0.2  # 与CPU服务保持一致

# GPU监控和管理 (最新稳定版本)
nvidia-ml-py>=12.560.30  # 最新稳定版本，支持最新GPU
psutil>=6.0.0  # 最新稳定版本
