#!/usr/bin/env python3
"""
检索性能测试
"""

import time
import requests

def test_retrieval_performance():
    """测试检索性能"""
    start_time = time.time()
    
    response = requests.post(
        "http://localhost:8009/v2/retrieval/search",
        json={"query": "测试查询", "collection": "department", "top_k": 5}
    )
    
    response_time = time.time() - start_time
    
    assert response.status_code == 200
    assert response_time < 5.0  # 5秒内响应
    
    print(f"检索响应时间: {response_time:.3f}秒")

if __name__ == "__main__":
    test_retrieval_performance()
