#!/usr/bin/env python3
"""
综合验收测试

验证所有四个优先级问题的解决情况
"""

import logging
import requests
import json
import time
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# API配置
API_BASE_URL = "http://localhost:8009"
API_V2_BASE = f"{API_BASE_URL}/v2"


def test_problem_1_indexing_build():
    """验收测试：问题1 - 索引构建失败修复"""
    logger.info("🎯 验收测试：问题1 - 索引构建失败修复")
    
    try:
        response = requests.post(f"{API_V2_BASE}/indexing/test", timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            status = data.get('status', 'unknown')
            
            # 验证状态从 "partial_success" 变为 "success"
            if status == 'success':
                logger.info("✅ 问题1验收通过：索引构建完全成功")
                logger.info(f"   状态: {status}")
                logger.info(f"   消息: {data.get('message', 'no message')}")
                return True
            else:
                logger.error(f"❌ 问题1验收失败：索引构建状态异常 - {status}")
                return False
        else:
            logger.error(f"❌ 问题1验收失败：HTTP {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 问题1验收异常: {str(e)}")
        return False


def test_problem_2_offline_mode_optimization():
    """验收测试：问题2 - 离线模式优化"""
    logger.info("🎯 验收测试：问题2 - 离线模式优化")
    
    try:
        # 测试检索API的模式标识
        search_request = {
            "query": "食品安全监管问题",
            "collection": "department",
            "top_k": 3
        }
        
        response = requests.post(
            f"{API_V2_BASE}/retrieval/search",
            json=search_request,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            metadata = data.get('metadata', {})
            
            # 检查是否有模式标识
            retrieval_mode = metadata.get('retrieval_mode')
            mode_description = metadata.get('mode_description')
            system_status = metadata.get('system_status', {})
            
            if retrieval_mode and mode_description:
                logger.info("✅ 问题2验收通过：离线模式优化完成")
                logger.info(f"   检索模式: {retrieval_mode}")
                logger.info(f"   模式描述: {mode_description}")
                logger.info(f"   Milvus可用: {system_status.get('milvus_available', 'unknown')}")
                return True
            else:
                logger.error("❌ 问题2验收失败：缺少模式标识信息")
                return False
        else:
            logger.error(f"❌ 问题2验收失败：HTTP {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 问题2验收异常: {str(e)}")
        return False


def test_problem_3_api_restructure():
    """验收测试：问题3 - API接口重构"""
    logger.info("🎯 验收测试：问题3 - API接口重构")
    
    try:
        # 测试新的系统管理API
        endpoints_to_test = [
            (f"{API_V2_BASE}/system/info", "系统信息"),
            (f"{API_V2_BASE}/system/health", "健康检查"),
            (f"{API_V2_BASE}/system/status", "服务状态")
        ]
        
        success_count = 0
        for url, name in endpoints_to_test:
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    
                    # 检查标准化字段名（英文字段名）
                    expected_fields = {
                        "系统信息": ["service_name", "version", "status", "architecture"],
                        "健康检查": ["status", "service_name", "version", "architecture"],
                        "服务状态": ["service_name", "version", "status", "component_status"]
                    }

                    required_fields = expected_fields.get(name, [])
                    has_required_fields = all(field in data for field in required_fields)

                    if has_required_fields:
                        success_count += 1
                        logger.info(f"✅ {name}端点测试通过")
                    else:
                        missing_fields = [field for field in required_fields if field not in data]
                        logger.error(f"❌ {name}端点缺少必需字段: {missing_fields}")
                else:
                    logger.error(f"❌ {name}端点返回HTTP {response.status_code}")
            except Exception as e:
                logger.error(f"❌ {name}端点测试异常: {str(e)}")
        
        # 验收标准：至少80%的端点通过
        success_rate = success_count / len(endpoints_to_test)
        if success_rate >= 0.8:
            logger.info("✅ 问题3验收通过：API接口重构完成")
            logger.info(f"   成功率: {success_rate*100:.0f}%")
            return True
        else:
            logger.error(f"❌ 问题3验收失败：API重构未达标 ({success_rate*100:.0f}%)")
            return False
            
    except Exception as e:
        logger.error(f"❌ 问题3验收异常: {str(e)}")
        return False


def test_problem_4_test_file_cleanup():
    """验收测试：问题4 - 测试文件清理"""
    logger.info("🎯 验收测试：问题4 - 测试文件清理")
    
    try:
        # 检查tests目录是否创建
        tests_dir = os.path.join(project_root, "tests")
        if not os.path.exists(tests_dir):
            logger.error("❌ 问题4验收失败：tests目录未创建")
            return False
        
        # 检查子目录
        required_subdirs = ["unit", "integration", "performance", "diagnostics", "analysis"]
        missing_subdirs = []
        for subdir in required_subdirs:
            subdir_path = os.path.join(tests_dir, subdir)
            if not os.path.exists(subdir_path):
                missing_subdirs.append(subdir)
        
        if missing_subdirs:
            logger.error(f"❌ 问题4验收失败：缺少子目录 {missing_subdirs}")
            return False
        
        # 检查是否有文件被移动到tests目录
        moved_files = []
        for root, dirs, files in os.walk(tests_dir):
            for file in files:
                if file.endswith('.py'):
                    moved_files.append(file)
        
        if len(moved_files) >= 3:  # 至少应该有几个文件被移动
            logger.info("✅ 问题4验收通过：测试文件清理完成")
            logger.info(f"   tests目录: 已创建")
            logger.info(f"   子目录: {len(required_subdirs)} 个")
            logger.info(f"   移动文件: {len(moved_files)} 个")
            return True
        else:
            logger.error(f"❌ 问题4验收失败：移动文件数量不足 ({len(moved_files)})")
            return False
            
    except Exception as e:
        logger.error(f"❌ 问题4验收异常: {str(e)}")
        return False


def test_overall_system_stability():
    """验收测试：整体系统稳定性"""
    logger.info("🎯 验收测试：整体系统稳定性")
    
    try:
        # 测试多个端点的稳定性
        endpoints = [
            f"{API_BASE_URL}/health",
            f"{API_V2_BASE}/system/health",
            f"{API_V2_BASE}/retrieval/config",
            f"{API_V2_BASE}/indexing/config"
        ]
        
        stable_count = 0
        for endpoint in endpoints:
            try:
                response = requests.get(endpoint, timeout=10)
                if response.status_code == 200:
                    stable_count += 1
            except Exception:
                pass
        
        stability_rate = stable_count / len(endpoints)
        
        if stability_rate >= 0.9:  # 90%稳定性
            logger.info("✅ 整体系统稳定性验收通过")
            logger.info(f"   稳定性: {stability_rate*100:.0f}%")
            return True
        else:
            logger.error(f"❌ 整体系统稳定性验收失败：稳定性不足 ({stability_rate*100:.0f}%)")
            return False
            
    except Exception as e:
        logger.error(f"❌ 整体系统稳定性验收异常: {str(e)}")
        return False


def main():
    """综合验收测试主函数"""
    logger.info("🏆 开始综合验收测试...")
    logger.info("="*80)
    
    # 验收测试结果
    acceptance_results = {}
    
    # 问题1验收
    acceptance_results["problem_1_indexing_build"] = test_problem_1_indexing_build()
    
    # 问题2验收
    acceptance_results["problem_2_offline_mode"] = test_problem_2_offline_mode_optimization()
    
    # 问题3验收
    acceptance_results["problem_3_api_restructure"] = test_problem_3_api_restructure()
    
    # 问题4验收
    acceptance_results["problem_4_test_cleanup"] = test_problem_4_test_file_cleanup()
    
    # 整体稳定性验收
    acceptance_results["overall_system_stability"] = test_overall_system_stability()
    
    # 汇总验收结果
    logger.info("\n" + "="*80)
    logger.info("🏆 综合验收测试结果:")
    logger.info("="*80)
    
    problem_mapping = {
        "problem_1_indexing_build": "问题1：索引构建失败修复",
        "problem_2_offline_mode": "问题2：离线模式优化",
        "problem_3_api_restructure": "问题3：API接口重构",
        "problem_4_test_cleanup": "问题4：测试文件清理",
        "overall_system_stability": "整体系统稳定性"
    }
    
    for test_name, passed in acceptance_results.items():
        problem_name = problem_mapping.get(test_name, test_name)
        status = "✅ 通过" if passed else "❌ 失败"
        logger.info(f"{problem_name:30s}: {status}")
    
    total_tests = len(acceptance_results)
    passed_tests = sum(acceptance_results.values())
    
    logger.info(f"\n验收通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.0f}%)")
    
    # 最终判定
    if passed_tests == total_tests:
        logger.info("\n🎉🎉🎉 恭喜！所有问题验收测试通过！ 🎉🎉🎉")
        logger.info("✅ 问题1：索引构建失败已完全修复")
        logger.info("✅ 问题2：离线模式已优化，用户体验提升")
        logger.info("✅ 问题3：API接口重构完成，结构清晰")
        logger.info("✅ 问题4：测试文件清理完成，符合生产标准")
        logger.info("✅ 整体系统稳定性达到验收标准")
        logger.info("\n🚀 CPU服务四个具体问题全部解决！")
        return True
    else:
        logger.error("\n❌ 部分验收测试未通过，需要进一步优化")
        failed_problems = [
            problem_mapping[test_name] 
            for test_name, passed in acceptance_results.items() 
            if not passed
        ]
        logger.error(f"未通过的问题: {', '.join(failed_problems)}")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("用户中断测试")
        sys.exit(1)
    except Exception as e:
        logger.error(f"验收测试过程中发生错误: {str(e)}")
        sys.exit(1)
