# AI接诉即办助手 v3.0 - 开发环境配置
# 开发环境专用配置文件

# ================================
# 基础配置
# ================================
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG

# ================================
# API嵌入配置（密集嵌入）
# ================================
API_EMBED_MODEL=BAAI/bge-m3
API_EMBED_BATCH_SIZE=4

# ================================
# BGE-M3模型配置（稀疏嵌入）
# ================================
BGE_M3_MODEL_PATH=BAAI/bge-m3

# ================================
# GPU服务配置
# ================================
GPU_SERVICE_HOST=0.0.0.0
GPU_SERVICE_PORT=8001
GPU_SERVICE_WORKERS=1
# 开发环境GPU服务URL
GPU_SERVICE_URL=http://localhost:8001

# ================================
# CPU服务配置
# ================================
CPU_SERVICE_HOST=0.0.0.0
CPU_SERVICE_PORT=8000
CPU_SERVICE_WORKERS=1

# ================================
# 数据库配置
# ================================
MILVUS_URI=http://localhost:19530
MILVUS_DIMENSION=1024

# ================================
# Redis配置
# ================================
REDIS_URL=redis://localhost:6379/0

# ================================
# MinIO配置
# ================================
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=ai-jiesujiban-dev

# ================================
# 检索配置
# ================================
RETRIEVAL_TOP_K_DEPARTMENT=2
RETRIEVAL_TOP_K_GUIDELINE=2
RETRIEVAL_TOP_K_HISTORICAL=3
RETRIEVAL_TOP_K_DELEGATED=2

# ================================
# 安全配置
# ================================
ADMIN_API_KEY=sk-dev-test-key

# ================================
# CORS配置
# ================================
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
CORS_METHODS=GET,POST,PUT,DELETE
CORS_HEADERS=*

# ================================
# 性能配置
# ================================
REQUEST_TIMEOUT=120
GPU_REQUEST_TIMEOUT=30
MAX_CONCURRENT_REQUESTS=10

# ================================
# 监控配置
# ================================
ENABLE_PERFORMANCE_LOGGING=true
PERFORMANCE_LOG_LEVEL=DEBUG
HEALTH_CHECK_INTERVAL=10
