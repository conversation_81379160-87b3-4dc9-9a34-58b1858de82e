# CPU服务本地开发配置 - 修复版
# AI接诉即办助手 v3.0 - GPU+CPU分离架构

# =============================================================================
# 基础配置
# =============================================================================
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG

# =============================================================================
# CPU服务配置
# =============================================================================
CPU_SERVICE_HOST=0.0.0.0
CPU_SERVICE_PORT=8009
CPU_SERVICE_WORKERS=1

# =============================================================================
# GPU服务配置（连接Docker部署的GPU服务）
# =============================================================================
GPU_SERVICE_URL=http://localhost:8001

# =============================================================================
# API嵌入配置（SiliconFlow）
# =============================================================================
API_EMBED_MODEL=BAAI/bge-m3
API_EMBED_KEY=sk-hconcoielayrcqyfoqibziztdnchplrhkmupsvddcfkwehrd
API_EMBED_BASE=https://api.siliconflow.cn/v1
API_EMBED_BATCH_SIZE=4

# =============================================================================
# Milvus配置
# =============================================================================
MILVUS_URI=http://************:19530
MILVUS_DIMENSION=1024

# =============================================================================
# 开发调试配置
# =============================================================================
CORS_ORIGINS=http://localhost:3000,http://localhost:8080,http://127.0.0.1:3000
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=*

# =============================================================================
# 性能配置（开发环境优化）
# =============================================================================
REQUEST_TIMEOUT=60
GPU_REQUEST_TIMEOUT=30
MAX_CONCURRENT_REQUESTS=5
